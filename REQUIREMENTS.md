Order Management QC App (Mini-ERP) - Director's Vision & Comprehensive Requirements
I am the Director of the company, and while my technical background isn't that of a seasoned Linux engineer, I possess a broad knowledge of many things and a passion for building. I manage Proxmox clusters in my office as a hobby, and building solutions is a core interest. Recently, I've delved into Vibe coding, and within just 15 days, I've successfully built over three internal applications for similar operational needs, including a courier management app and an order management app for one of our locations, which incorporated some complex functionalities. I understand how things should work, even if I don't always know the precise technical steps to make them work, which is why I leverage Vibe coding. This current project, the Order Management QC App, is of paramount importance to our company, and I am planning it meticulously because it's expected to be significantly larger and more intricate than my previous builds, potentially reaching 50,000 to 100,000 lines of code. My experience has shown that while AI-assisted coding is powerful, codebases can become difficult to manage and troubleshoot as they grow, and AI can start to hallucinate, leading to costly token usage for fixing minor issues. Therefore, establishing clear rules, a detailed plan, and a structured approach from the outset, possibly using a methodology like <PERSON>master, is crucial for this project's success.
Project Overview & Goals
The application will be named the Order Management QC App, functioning as a Mini-ERP. Its primary goal is to develop a robust internal web application that will replace our current reliance on Google Sheets. This new system will manage laptop part orders, intricate procurement processes, multi-stage quality control (QC), kitting, packing, invoicing, and complex inter-location transfers. A key driver is to significantly improve operational efficiency, planning accuracy, data integrity, and drastically reduce errors, especially for our golden customers like HP and Lenovo, whose satisfaction is critical to our revenue. This application is intended solely for internal company staff, approximately 20-30 individuals across various roles and our two main locations: the SB Location, which is our main warehouse and primary operations hub for order fulfillment, kitting, packing, and shipping; and the NP Location, a secondary site focused on specialized tasks like motherboard testing/QC and specific parts arrangement, acting as a stocking and QC hub for components like motherboards. This is not a public-facing website or a SaaS product for sale. A core philosophy is that the system must be resilient, allowing for manual overrides or alternative workflows if any automation component fails; we must avoid overly rigid constraints that would hinder our ability to handle exceptions.
User Roles & Permissions
The system will have distinct user roles with specific permissions:
The Director/Admin role will have full, unrestricted access to all system features, modules, data (including all financial information like prices and costs), and all administrative settings. They can manage user accounts, assign roles, define permissions, and will have the authority to override system warnings, such as the use of a duplicate CT number, following an established approval workflow or if possessing direct override rights. Directors/Admins will also receive critical system notifications, like alerts for new device login attempts, and will be responsible for configuring global system settings and acting as approvers in critical workflows.
Accountants will primarily access the invoicing module and related financial reports. They will be able to view order prices and all financial details necessary for generating invoices, manage invoice statuses, input invoice numbers, link invoices to orders, upload signed delivery documents, and export data for external accounting software. They will generally not have visibility into operational stages like NPQC or detailed packing/QC logs unless specifically granted for reporting.
Warehouse Operations Managers at the SB Location will oversee the entire order processing pipeline there. They will manage task assignments to SB staff, oversee inventory pulling requests, and monitor the progress of orders through kitting, packing, and the SB screening/QC stages. Critically, they will not be able to view order prices but will see quantities, ETAs, and customer names for orders processed at SB.
SB Kitting/Packing Staff will use a simplified interface showing only parts assigned to them for kitting and packing. They will log the completion of these tasks against specific CT numbers, print necessary labels for parts and boxes, and will have the functionality to flag parts for "Hold at Kitting/Packing" or initiate a "Reject at Kitting/Packing" process. They will not see order prices or full customer order histories, only the part and quantity information essential for their tasks.
SB Screening/QC Staff, working after kitting and packing, will also use a simplified interface showing kitted/packed parts assigned for their screening/QC step. They will log pass, fail, or hold statuses against specific CT numbers, access FAI and master images for visual comparison, and can flag parts for "Hold at Screening/QC" or initiate a "Reject at Screening/QC." They will not see order prices.
NP Location Managers will primarily access the dedicated NPQC module. They will manage motherboard testing, specific parts arrangement and sourcing at the NP location, and will initiate and receive inter-location transfers between NP and SB. They will not see customer names or order prices but will view UIDs, Part Numbers, Internal Descriptions, and quantities relevant to NP's operations.
NP Location QC/Testing Staff will have a restricted view within the NPQC module, focused on parts assigned for NP testing or QC. They will log test results, be able to input/scan CT numbers injected into motherboard BIOS, access FAI/master images for comparison, and indicate parts as ready for transfer, on hold, or rejected at NP. They will not see customer names or order prices.
Procurement Staff will access order details relevant to sourcing parts, including pending quantities and part specifications. They will utilize procurement support features like the snippet generator for vendor communications and will be able to update Vendor IDs (VID), Master Shipping Carton (MSC) IDs, and their related ETAs on order lines. Their visibility of order prices will be permission-based, as determined by Directors/Admins.
System-wide permission granularity will ensure strict control over price visibility (default hidden for operational roles), customer name visibility (restricted for NP staff), limits on historical data access per role, and specific permissions for sensitive actions like overriding CT warnings or approving high-value procurements.
Core Application Modules & Functionalities
Order Management (Main Hub - Primarily SB Location Focus)
Order Import & Creation will support manual entry by authorized users, import from Excel/CSV files (with a provided template and robust data validation), and integration with a designated Google Sheet via N8N (or similar automation tools like Pipedream/Make) for pushing order lines into the application, even those initially lacking PO numbers and dates.
Order Data Fields for each order line are critical and must be clearly labeled. These include a system-generated, sequential, human-readable Unique Order Line ID (UID) with a configurable prefix (e.g., A001), prominently displayed for internal reference. A dropdown for Customer Name (admin-managed, visibility controlled). Text fields for Purchase Order (PO) Number (can be initially blank) and PO Date / Order Date (DDMMYY format, calendar picker, can be blank). The Customer's Part Number text field is key for matching. A Core Part Number text field (often unused but provided by customer). The Customer's Description (Short Text) field. An editable BPI DSC (Internal Part Description / Short Name) text field for the company's internal shorthand (e.g., "Laptop 840 G10 LCD"); this should attempt to auto-populate if previously defined for the part number but remain editable per line. A dropdown for ~15 predefined, admin-managed Product Categories. A numeric Order Quantity field. A numeric Price (per unit) field with decimal support, visibility strictly permission-controlled. A text field for Lead Time (from RFQ, e.g., "Ready Stock," "5 days"). A date field for Current ETA to Customer (DDMMYY). Optional text fields for VID (Vendor ID, with autocomplete/quick-add from a globally unique list of vendor short codes) and MSC (Master Shipping Carton/Consignment ID, alphanumeric 5-6 digits, unique per vendor's incoming consignment from China, no strict validation). Provision for 2-3 additional generic text fields for miscellaneous customer data.
Pre-Order Notification vs. Actual PO Handling: The system must accept pre-order data lacking PO Number/Date. When actual POs arrive, users update pre-order lines, matching primarily by Customer Part Number (HP doesn't split a pre-order part across multiple POs). If the actual PO details differ from the pre-order: Quantity Changes will be visually highlighted on the order line card (e.g., animation/glow), with a hover tooltip showing "Original pre-order quantity: X. Updated PO quantity: Y." If a part from the pre-order is missing on the PO, that line is marked "Cancelled by Customer." If the PO contains a new part not on the pre-order, it's added as a fresh order line.
ETA Management is a crucial system component. Each order line tracks RFQ_ETA_Date, First_ETA_Date, Second_ETA_Date, Third_ETA_Date, and Final_ETA_Date. A small, visual ETA status indicator bubble on the order card will display the current ETA date. Color Coding (configurable): Default style for RFQ ETA, Subtle Green for First ETA, Subtle Yellow/Orange for Second ETA, Subtle Red for Third ETA. A slow, steady, non-jarring blinking animation for Final ETAs or overdue ETAs. Hovering over this bubble must show a tooltip with the full ETA history (e.g., "RFQ ETA: [date]", "First ETA: [date]", etc.). An optional multiline text field for logging the reason for each ETA delay. A dedicated Bulk ETA Update System (page/modal) for Admin/Directors to efficiently update ETAs for multiple pending lines (e.g., weekly on Mondays), allowing filtering and batch updates of ETA date and reason. The system must generate an Excel/CSV file of updated ETAs formatted for manual emailing to HP.
User Assignment capability will allow assigning one or more internal users as "responsible" for specific order lines, visible on the order card.
Internal Communication (WhatsApp for SB Tasks) will be integrated via the Evolution API, relayed through N8N. A "WhatsApp" button on each order line card opens a dialog. This dialog allows selecting predefined, admin-configurable message templates (e.g., "Pull Part from Stock"), which auto-populate with dynamic data from the order line (e.g., {{PartNumber}}, {{QuantityNeeded}}) into an editable text area. Users select recipients from a system-managed list of internal workers' WhatsApp numbers. Clicking "Send" calls the N8N webhook. An admin page will manage N8N webhook URLs, internal user WhatsApp numbers, and message templates with placeholder definitions.
A simple WhatsApp-Based Part Mapping Approval Workflow is required. Before an SB order line proceeds to kitting, a templated WhatsApp message (via Evolution API/N8N) is sent to a pre-configured Director/Manager group. The message includes key part details (Customer Part#, BPI DSC, UID, Qty, main image) asking for confirmation of the internal part mapping. For MVP, the manager manually updates an "Part Mapping Approved" status on the order line in the app to allow it to proceed.
Quantity Flow & Status Tracking (Progressive System - SB Location) will provide a detailed, real-time view of unit quantities across operational stages, using a quantityLogs array per order line. SB Order Line States include: TotalOrderQuantity, PendingProcurementArrangement, RequestedFromStock, AwaitingKittingPacking, InKittingPacking, OnHoldAtKittingPacking, KittedPacked_AwaitingScreeningQC, InScreeningQC, OnHoldAtScreeningQC, ScreeningQCPassed_ReadyForInvoice, ScreeningQCRejected (returns to PendingProcurementArrangement), Invoiced, ShippedDelivered, CustomerReturned (placeholder), and Cancelled. The order card will visually display current quantities in these states (e.g., "Pending Proc: 5", "On Hold QC: 1"). quantityLogs entries include action_type, quantity_moved, timestamp, user_id, reason_text, and associated_ct_numbers. All movements must be traceable.
Serial Number (CT Number) Management
CT Number Properties: CT numbers are 14-digit ALPHANUMERIC, with ALL CHARACTERS STRICTLY UPPERCASE. It's noted that Lenovo uses serial numbers differently, so this CT system primarily applies to HP orders and internal tracking; Lenovo orders may require a distinct serial number management approach if their requirements differ significantly (details for Lenovo's specific serial number needs beyond this observation are not yet defined).
Uniqueness, Warning, and Override with Approval: CT numbers should ideally be unique forever. Upon manual entry/scan, the system performs an immediate historical lookup. If a duplicate is found, a vigorous warning message appears: "WARNING: CT Number '[CT_NUMBER]' is a duplicate. Previously used for Order '[Original_Order_UID]', Part '[Original_Part_Number]', supplied to '[Original_Customer]' on '[Original_Ship_Date]'." Users without direct override permission, if they attempt to proceed, will trigger an approval request (in-app notification/email to Directors/Admins). The duplicate CT can only be used once an authorized approver approves it within the system. Users with direct override permission see the warning but can "Acknowledge and Proceed with Duplicate Use." All duplicate uses, warnings, overrides, approvers, and timestamps must be meticulously logged. The system will never auto-generate a known duplicate CT.
CT Number Generation Options (within a dedicated UI): Base options include using an FAI "Master" CT String (a non-usable template string from an FAI image for a part, randomizing the last N uppercase alphanumeric characters, N selected from 2-5 by user); using the Last Used CT for the Same Part (retrieving the structure, randomizing the last N characters); or using a Global Prefix (admin-configurable) + Random Suffix (randomizing N uppercase alphanumeric characters).
CT Number Input/Assignment Interface (modal dialog from a "CT" button on order line): A multi-line, "live" expanding text box for scanning/typing CTs. Instantaneous validation: format check, duplicate check. Duplicates are visually flagged (e.g., red highlight) with the warning message appearing. Duplicates cannot be committed without successful override/approval. Entered/scanned CTs display as a numbered list for easy reference. A "Copy to Clipboard" button for all valid (or all entered) CTs. Interface shows "X of Y CTs assigned."
Linking: Each valid, assigned CT number is unequivocally linked to its specific order line and the individual physical unit.
FAI (First Article Inspection) Document Management
Association & Storage: Ability to associate FAI Excel files with specific Customer Part Numbers. A configurable storage location for FAI Excels (local NAS path or cloud storage bucket) is required.
Image Viewing & Access: A "View FAI Images" button on order lines and QC/Screening UIs. Images are extracted only from "Sheet2" of HP FAI Excels; system attempts to grab all images from this sheet. Clicking the button opens a modal/viewer. Initially, a thumbnail grid of all extracted FAI images for the part. Clicking a thumbnail opens it in a larger, full-screen/near-full-screen view. Easy navigation (swipe/buttons) through all available images (Master Image first, then FAI images).
Master Image Upload & Management: For each unique Customer Part Number, authorized users can upload one or more high-resolution "Master Pictures." The most recent/designated Master Picture is primary and displayed first.
SB Kitting/Packing & Screening/QC Workflow
Defined Workflow Order at SB: Procured/Pulled -> CT Assignment & Label Print -> Awaiting/In Kitting/Packing -> Kitted/Packed & Awaiting Screening/QC -> In Screening/QC -> [Passed -> Ready for Invoice] OR [Failed -> Rejected to Procurement Pool] OR [Held at Kitting/Screening -> Resolved to Pass/Reject].
Dedicated User Interfaces for SB Kitting/Packing staff and SB Screening/QC staff, showing only parts assigned to their stage. Seamless access to label printing from Kitting/Packing UI. Comprehensive logging: user, timestamp for start/complete of kitting/packing/screening per CT; Pass/Fail/Hold status with mandatory reasons for Fails/Holds.
"Hold" Functionality at SB (Kitting/Packing AND Screening/QC stages): Staff can mark CTs as OnHoldAtKittingPacking or OnHoldAtScreeningQC. Mandatory reason (predefined list + free text) required. Held parts physically remain in area for potential fixes. Quantity on hold is visible and part of the active order but doesn't proceed. Resolution of "Hold": "Hold Resolved - Passed" (continues workflow) or "Hold Escalated - Reject Part" (returns quantity to PendingProcurementArrangement pool).
"Reject Part (Return to Pool)" Functionality at SB: Can be triggered directly or as escalation from "Hold." Quantity for rejected CT(s) immediately returns to PendingProcurementArrangement pool. Mandatory rejection reason logged. All hold/rejection actions, reasons, resolutions logged against CT and order line history.
WhatsApp Notification on Definitive Screening/QC Rejection: If a part is "Rejected at Screening/QC" (not just held), an instant, automated WhatsApp message (via Evolution API/N8N) to a predefined group: "QC REJECTION ALERT: Order UID [UID], Part [Part#], CT [CT#], Reason: [Rejection Reason]."
Invoicing & Accounts (SB Location)
Dedicated "Invoicing" or "Accounting" section for Accountants (and Directors/Admins). Default view: order lines/quantities in ScreeningQCPassed_ReadyForInvoice status. Accountants see order prices. PO Number clearly displayed. Order lines visually grouped by PO Number. "Click-to-Copy" icons next to critical fields (Part No, Desc, Qty, Price, Total) for pasting to external accounting software. Warning if accountant attempts to assign a single invoice number to lines from multiple POs ("...HP may not allow this. Proceed?"). Accountant can acknowledge and proceed. Invoice Data Entry: field for Invoice Number from accounting software; ability to select multiple lines (ideally from same PO) and assign a single Invoice Number; optional Eway Bill Number field; accountant can adjust quantity being invoiced (logged with reason). Status Update: Mark lines/quantities as "Invoiced," moving them to Invoiced state. Data Export: Download list of items pending/just invoiced (CSV/Excel). Advanced Filtering & Searching for historical invoices/lines.
Upload & Link Signed Documents: Functionality to upload scanned signed Delivery Reports and customer-stamped Invoice copies, linked to the system Invoice Number and/or order lines. Multiple documents per invoice allowed. Easily retrievable for proof. Allow notation if delivery partially accepted by customer.
Shipment / Delivery & Post-Invoicing Communication (SB Location)
Daily Activity Summary Email Attachment for Customer (e.g., HP): System generates a professional Excel file (or PDF) for the user to manually attach to an email. Content: Customer Name, Report Date. For each Order Line shipped/delivered: PO No, PO Date, Customer Part No, Customer Desc, Total Order Qty, Qty Previously Delivered (with dates if relevant), Qty Delivered Today (with date), CT Numbers for items delivered today (child rows or associated column), Final Pending Qty for that line. Optional: URLs to view stored documents (luxury, requires thought on access).
General PDF Generation Utility (In-App): Front-end utility for A4 PDFs (e.g., basic delivery notes without prices, internal reports). Viewable/downloadable from browser.
Label Printing & Design (Crucial)
Integrated Label Design Canvas: Embed a feature-rich, open-source JavaScript label design canvas. Toolset: add/edit static/dynamic text (font, size, style); lines, rectangles, circles; various barcodes (Code 128, Code 39, QR essential), barcode data linkable to dynamic fields; static images (logos). Dynamic Data Field Integration: Easy selection/placement of app data fields onto label (UID, Part No, BPI DSC, Qty, CT No, PO No, Dates, etc.). User-defined custom label sizes (width/height in mm/inches), canvas reflects size. WYSIWYG preview.
Label Template Management: Save/load label designs as named templates. Admin can designate defaults per customer, part category, purpose (e.g., "Default CT Label"), or global.
ZPL Generation & Output: Converts visual design (with dynamic data) to ZPL/ZPL II. Option for Admin/Developer to view/copy raw ZPL (for Labelary testing). Option to download .zpl file.
Network Printer Integration (via Local NPS): Web app sends print jobs (ZPL + target printer) via HTTP POST to a generic NPS application on the local network. NPS forwards ZPL to selected local Zebra printer. Admin Printer Settings Page: Configure NPS URL(s); add/edit/remove network Zebra printers (Name, IP, Port); view basic printer status (if NPS provides it); support multiple printers. User selects target Zebra printer at print time. Default printer assignable per label template.
"Quick Print" for Generic Labels: Dedicated menu for 5-10 predefined generic label templates (designed in-app, e.g., "HP ORDER - EXTERNAL BOX," "FRAGILE"). User selects template, quantity, printer, and prints. Not tied to specific order data.

NP Location QC & Transfer Hub (NPQC Module)
Access & UI Presentation: A distinct "NPQC" module/page, access strictly limited to "NP Location Manager," "NP Location QC/Testing Staff" roles (and Directors/Admins). Simpler, focused UI than main SB view.
Data Visibility Restrictions for NP Staff: Displays order lines assigned to NP (e.g., "Motherboard" category items, parts explicitly transferred from SB, or parts NP procures). Visible Fields: Order Line UID, Customer Part Number, BPI DSC (Internal Description - on-screen editable by NP for their internal notes), quantity requiring NP action. Strictly Hidden: Customer Name, Order Price, HP-specific shipping info, extensive SB history. Limited historical data view (admin-configurable).
NP Internal Workflow & Quantity Tracking (Progressive, using quantityLogsAtNP): NP Order Line States: TotalQuantityForNP, PendingAtNP, In_NP_QC, NP_QC_Passed_ReadyForSB, NP_QC_Failed_HoldAtNP, NP_QC_Failed_ReturnToSource (if unfixable at NP), SentToSB (confirmed received by SB), CancelledAtNP. NP Order Card visualizes these quantities. quantityLogsAtNP detail actions. Overall order.statusAtNP derived from aggregate quantities.
Motherboard Testing & CT Injection Specifics at NP: NP QC staff log function test results. Input/scan and record unique CTs they inject into motherboard BIOS (for NP internal tracking/transfer). Print generic internal labels (designed in-app, no HP logos) for motherboards transferred to SB, including NP-assigned CT, Part No, UID.
Inter-Location Transfers (SB <-> NP): Authorized SB users create "Transfer to NP" records (parts, quantities, SB CTs if any, reason e.g., specialized QC). Authorized NP users create "Transfer to SB" records (processed parts, NP CTs). System generates basic printable transfer slip for each transfer. Both SB and NP have dedicated "Incoming Transfers" pages listing in-transit items. Receiving location staff must confirm receipt, verify quantities, and mark transfer as "Received," "Partially Received," or "Received with Discrepancies." SB can "Reject" items from an NP transfer (e.g., transit damage, failed SB re-inspection) with mandatory reason (predefined list + free text). Rejected items can trigger "Transfer Back to NP" or be handled at SB.
Automated Notifications for NP: In-app and optional WhatsApp (via N8N) to NP group/manager when SB creates "Transfer to NP" or assigns an order for NP procurement, including custom instructions.
Luxury Feature - NPQC Staff Image/Video Upload: NP QC staff can upload multiple images/short videos (e.g., motherboard test rig) for a specific part/CT's QC record.
Luxury Feature - NPQC FAI/Master Image Access: NP QC staff have read-only access to FAI/Master Images for parts they handle. Can upload a high-res image as "master image" if one doesn't exist or they have a better reference.
Procurement Support & Vendor Communication
Vendor Communication Snippet Generator (from Order Line Card "P" button): Opens modal dialog. Image Preview & Selection: Thumbnails of Master & FAI images; user selects which to include. Data Field Selection: Checkboxes for Customer Part No, BPI DSC, Customer Desc, Quantity Required (PendingProcurementArrangement qty); user selects which to include. Live Snippet Preview: Text area dynamically shows message for WhatsApp/WeChat as selections are made. Image Handling: Attempt to make images easily pastable (base64 small images, or accessible URLs if hosted by app – technical feasibility for direct paste varies by chat platform). "Copy to Clipboard" button for entire formatted snippet. Template Saving within Dialog: Functionality within this same dialog to save current field/image selection preferences as a named "Procurement Snippet Template." Users select from saved templates for quick reuse; option to "Set as Default Template."
Bulk Procurement Actions (from main order list view): Select multiple order lines. Toolbar/context menu for bulk actions: Generate Combined Procurement Snippet (consolidate info for one vendor message, using template logic); Export Selected to Excel for Vendors (chosen fields); Bulk Assign VID/MSC to selected lines.
Tracking Fields on Order Line: VID (Vendor ID) text field (autocomplete/dropdown from globally unique internal vendor short codes; quick-add for new VIDs by permissioned users). MSC (Master Shipping Carton/Consignment ID) alphanumeric 5-6 digit text field (unique per vendor's incoming consignment from China; no strict validation, for user reference).
ETA for MSC Numbers & Link to Customer ETAs: Ability to manually enter an ETA for a specific MSC number. When updating customer-facing ETAs for order lines associated with an MSC, the MSC's ETA is clearly visible as a reference.
Returns Management (Future Module - High-Level Placeholder)
System architecture must accommodate a future comprehensive module for customer returns: logging returned parts (CTs), linking to original orders/invoices, condition assessment, dispositions (restock, repair, scrap), financial adjustments, replacement orders.
Order Cancellation Workflow (Internal Request & Customer Communication)
Users (Managers/Directors) can select an order line (or partial quantity) and initiate "Request to Cancel." Reason can be added (e.g., "Unable to procure"). System helps generate a formatted email draft (for user to send manually) to HP/customer requesting cancellation. Upon internal confirmation (before/after customer ack), user updates status in-app. This updates Cancelled quantity, Global Pending for HP, and other progressive quantities, logging the action.
System-Wide Features & Concerns
UI/UX Philosophy & Design: "Live UI" with real-time updates on page without full refresh (reactive frontend). Main Order View: Card system. Card Content (per image provided): UID (e.g., Q1234), Part No (e.g., L12345-601), Status Tag (e.g., QC); Title/Desc (BPI DSC / Customer Desc); Key Dates/Info (PO Date, Current ETA with color/blink logic as per 3.1.4.2); Progressive Quantity Bar (colored segments for Pending, QC, Completed, Canceled); Quantity Tags/Badges ("3 Pending", "1 QC", "4 Completed", "2 Canceled"); "Global Pending for HP" Display ("X left" or "Global Pending: X" derived as Total Order - Sent to SB - Cancelled). Card Actions: Consistent buttons/dropdown for Print Labels, CTs, Update Status, View Images, Procure Query, WhatsApp, Request Cancel, View History (actions open modals). Optional/Future: Expandable card for more details. Desktop First Optimization (latest Chrome, Firefox, Edge). Mobile Browser Compatibility: General responsive design for basic viewing. Specific Mobile-Centric Pages: Director's Quick Status View (key metrics, priority orders); Director's Info Sharing (select part from mobile, view images, "Share to WhatsApp" copies key details for pasting). (Luxury) Shipment Re-check Scanning UI. Minimal Clicks & Efficiency. Future: Dedicated Order Line Page (drill-down from card for spacious UI with all card functions).
Date Format Standard: All dates displayed and input: DDMMYY.
User Authentication & Authorization: Secure username/password auth. Comprehensive RBAC (permissions assigned to Roles, users to Roles). Admin Panel: User Management (CRUD, activate/deactivate, reset pwd); Role Management (CRUD, assign permissions); User-Role Assignment.
Application Security & Monitoring: New Device Login Alert Workflow: Successful login from new device/browser triggers automated email AND/OR WhatsApp (via N8N) to Admin/Director ("User [Username] logged in from new device. Details: [Browser, OS, approx. IP location]"). Configurable Access Delay for New Devices (Admin Setting): Optional global setting for 1-5 min delay. If enabled, new device login shows waiting screen: "For security, Admin notified. Please wait [countdown] minutes..." Full access after delay.
Data History / Audit Trail (Order Line Centric): Detailed audit trail for each order line. Logged Events: Creation; all changes to major fields (Qty, ETA, Price, Assigned User, BPI DSC, Part No, PO No - log old/new value); all significant status/stage changes in quantity flow; CT assignment/unassignment; label prints (template, user); communication events (WhatsApp Sent); override events (Duplicate CT approved). Logged Data per Event: Timestamp, User ID, clear event/change description. Viewable history from order line. Crucial for delay/performance analysis.
UI Theme: Dark Mode requirement is REMOVED. Standard light theme only.
WhatsApp Integration (System-Wide): Evolution API & N8N. Uses: Internal tasks, Director approvals, QC failure notifications, NP notifications, Admin alerts. Centralized Admin WhatsApp Settings: N8N webhooks, message templates, internal user/group WhatsApp numbers.
Application Settings (Global - Admin Managed): UID prefixes, ETA color logic params, product categories, predefined rejection reasons, NPS URLs, Printer configs, WhatsApp settings, security settings (new device delay).
Luxury Features (Post-MVP; Prioritization by Director)
AI-Powered Email Scanning & Order Creation (Pre-Orders/POs): N8N for basic rule-based scan/extract. Advanced "MCP Server" (AI models) for semantic parsing of email bodies/attachments for order details from less structured emails. Auto create/update order lines or push to Google Sheet for review.
AI-Powered FAI Image Extraction & Association: N8N or "MCP Server" scans Gmail/shared drive for FAI Excels. AI extracts all images from Sheet2. System attempts auto-association with Customer Part Numbers, with user review/confirmation.
AI-Assisted Visual QC (SB Screening/QC Stage): Camera + tablet setup. Image capture (foot-switch/button). AI compares captured image to Master/FAI images. Flags potential abnormalities (e.g., keyboard layout, gross damage – Director notes AI to focus on obvious, rule-based visual checks initially due to complexity/false positives). Outcome: Alerts human QC on tablet with highlighted image comparison; human makes final Pass/Hold/Reject. AI finding logged.
Direct WhatsApp Sending to Vendors (Procurement Dialog): Extend procurement snippet dialog. Select vendor contact (system-managed list). "Send via WhatsApp" button directly sends snippet (text, image URLs if app hosts images) via Evolution API/N8N.
Mobile Device Scanning for Shipment Re-check & Loading Verification (Zebra TC26): Dedicated mobile-responsive web UI for handheld scanners. Workflow: Select/scan Shipment/Invoice ID -> UI shows Part Nos & expected qtys -> User scans box barcode (identifies Part No) -> UI prompts for CT scan (may show expected CTs) -> User scans CT(s) -> System validates CT for part & shipment (Green=match, Red=error) -> Running tally. UI inspired by Odoo barcode app.
Interactive User Dashboards: Visual dashboards. KPIs: Pending orders (Global Pending for HP), orders by ETA status, items in stages, daily/weekly throughput, rejection rates. Graphs/Charts: Trends, distributions. Role-Based Views: Data filtered/aggregated by user role (Director=company-wide, Warehouse Mgr=SB specific, etc.).
RAG-based AI Chatbot (Directors/Admins): AI chatbot with knowledge of app functions & access to its real-time data (RAG pattern). Interfaces: In-app chat widget ("Ask AI Agent" button); WhatsApp interaction (via dedicated company WhatsApp number routed via N8N to chatbot). Capabilities: Answer questions ("Status of Order UID [UID]?"), retrieve info ("FAI images for part [Part#]?"), generate summaries ("Email me today's HP shipments." – requires bot email capability).
NPQC Advanced QC Documentation Features: NP QC staff can upload multiple images/short videos (e.g., motherboard test) for a specific CT's NP QC record. NP QC staff have read-only access to FAI/Master Images. If Master Image doesn't exist, NP staff (with permission) can upload one.