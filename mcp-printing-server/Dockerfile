FROM node:18-alpine

# Install system dependencies for printing and discovery
RUN apk add --no-cache \
    avahi-dev \
    dbus \
    sqlite \
    curl \
    wget

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy application source
COPY . .

# Create necessary directories
RUN mkdir -p /app/data /app/queue /app/temp /app/logs

# Set permissions
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/mcp/health || exit 1

# Start the server
CMD ["npm", "start"]