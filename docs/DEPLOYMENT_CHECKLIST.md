# Deployment Checklist for Vercel

## Pre-Deployment
- [ ] All TypeScript errors resolved
- [ ] CT Modal auto-progress fixes tested locally
- [ ] Environment variables documented in `.env.example`
- [ ] `vercel.json` configuration file created
- [ ] Health check endpoint added (`/api/health`)

## Vercel Setup
- [ ] Connect GitHub repository to Vercel
- [ ] Configure build settings (Vite preset)
- [ ] Add all required environment variables:
  - [ ] `VITE_SUPABASE_URL`
  - [ ] `VITE_SUPABASE_ANON_KEY` 
  - [ ] `VITE_APP_TITLE`
  - [ ] `VITE_LOCATION`
  - [ ] `VITE_UID_PREFIX`

## Supabase Configuration
- [ ] Get anon key from Supabase Dashboard → Settings → API
- [ ] Update Authentication → URL Configuration with Vercel URL
- [ ] Ensure Realtime is enabled in Supabase project

## Post-Deployment Testing
- [ ] Health check endpoint working: `https://your-app.vercel.app/api/health`
- [ ] Login functionality working
- [ ] WebSocket connections stable (no timeout errors)
- [ ] CT Modal auto-progress working:
  - [ ] 3-step transition from procurement
  - [ ] CT numbers showing in kitting stage
  - [ ] CT scanning working without errors
- [ ] No CORS errors in console

## Optional Features
- [ ] Custom domain configured (if applicable)
- [ ] WhatsApp credentials added (if using)
- [ ] N8N webhook URL added (if using)
- [ ] MCP backend deployed separately (if needed)

## Monitoring
- [ ] Vercel Analytics enabled
- [ ] Error tracking set up (optional)
- [ ] Performance monitoring active

## Rollback Plan
- [ ] Know how to rollback in Vercel dashboard
- [ ] Keep note of last working deployment ID
- [ ] Document any manual database changes

## Success Criteria
✅ No WebSocket connection timeouts
✅ No CORS errors
✅ CT auto-progress works end-to-end
✅ All quantity transitions successful
✅ Real-time updates working properly