# Quantity State Naming Convention Guide

> **Updated**: Database cleaned - only SHORT columns remain
> **Created**: January 12, 2025
> **Purpose**: Document the state naming system for clarity

## ✅ Current Database Reality

The `order_line_quantities` table now has **ONLY SHORT COLUMNS** after cleanup:
- **SHORT columns** (e.g., `pending_procurement`) - CONTAIN ALL DATA
- **LONG columns** - REMOVED (were empty duplicates)

## 📋 State Name Mapping Reference

| Frontend State Name (LONG) | Database Column (SHORT) | Usage |
|---------------------------|------------------------|--------|
| `pending_procurement_arrangement` | `pending_procurement` | Initial state for new orders |
| `requested_from_stock` | `requested_from_stock` | Stock request state |
| `awaiting_kitting_packing` | `awaiting_kitting_packing` | Ready for kitting |
| `in_kitting_packing` | `in_kitting_packing` | Currently being kitted |
| `on_hold_at_kitting_packing` | `on_hold_kitting` | Kitting hold |
| `kitted_packed_awaiting_screening_qc` | `kitted_awaiting_qc` | Ready for QC |
| `in_screening_qc` | `in_screening_qc` | Currently in QC |
| `on_hold_at_screening_qc` | `on_hold_qc` | QC hold |
| `screening_qc_passed_ready_for_invoice` | `qc_passed_ready_invoice` | Passed QC |
| `screening_qc_rejected` | `qc_rejected` | Failed QC |
| `invoiced` | `invoiced` | Invoice created |
| `shipped_delivered` | `shipped_delivered` | Shipped to customer |
| `cancelled` | `cancelled` | Order cancelled |

## 🔧 How the System Works

### 1. Frontend Code
```typescript
// Frontend uses LONG descriptive names
const transition = {
  fromState: 'pending_procurement_arrangement',
  toState: 'awaiting_kitting_packing',
  quantity: 5
}
```

### 2. Database Function
```sql
-- transition_quantity function maps LONG → SHORT
WHEN 'pending_procurement_arrangement' THEN 'pending_procurement'
WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_awaiting_qc'
```

### 3. Actual Database Update
```sql
-- Updates SHORT columns only
UPDATE order_line_quantities 
SET pending_procurement = pending_procurement - 5,
    awaiting_kitting_packing = awaiting_kitting_packing + 5
```

## ✅ Best Practices

### DO:
1. **Always use LONG state names in frontend code** - More readable
2. **Let the database function handle mapping** - It works correctly
3. **Use `useQuantityTracking` hook** - Primary implementation
4. **Check `data.success` field** - Handle RPC responses properly

### DON'T:
1. **Don't use SHORT column names in frontend** - Use descriptive LONG names
2. **Don't bypass the mapping function** - It's essential
3. **Don't create duplicate columns again** - We just cleaned them up

## 🔍 Debugging Tips

### Check Current State Distribution:
```sql
-- This shows data in SHORT columns
SELECT 
  pending_procurement,
  awaiting_kitting_packing,
  in_kitting_packing,
  kitted_awaiting_qc,
  in_screening_qc,
  qc_passed_ready_invoice
FROM order_line_quantities
WHERE order_line_id = 'your-order-id';
```

### Test State Transition:
```typescript
// Use the hook with LONG names
const { transitionQuantity } = useQuantityTracking()

await transitionQuantity({
  orderLineId: 'xxx',
  fromState: 'pending_procurement_arrangement', // LONG name
  toState: 'awaiting_kitting_packing',         // Frontend name
  quantity: 1,
  reason: 'Test transition'
})
```

## 📝 Common Issues

### Issue: "Insufficient quantity" error when quantity exists
**Cause**: Using wrong state names or checking wrong columns
**Fix**: Use exact state names from the mapping table above

### Issue: Transitions work in SQL but not from UI
**Cause**: Frontend not handling RPC response properly
**Fix**: Check `data.success` field in response

### Issue: Quantities not updating in UI
**Cause**: Real-time subscriptions watching wrong columns
**Fix**: Ensure subscriptions refresh after transitions

## 🚀 Future Considerations

1. **Type Safety**: Create TypeScript enums for state names
2. **Documentation**: Keep this guide updated with any changes
3. **Performance**: System is now cleaner without duplicate columns

---

**Remember**: The system is now clean with only SHORT columns in the database. Continue using LONG descriptive state names in the frontend - the database function handles the mapping automatically.