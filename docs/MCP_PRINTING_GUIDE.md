# MCP Printing System - Complete User & Setup Guide

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Feature List](#feature-list)
3. [Setup Instructions](#setup-instructions)
4. [User Interface Guide](#user-interface-guide)
5. [Template Management](#template-management)
6. [Troubleshooting](#troubleshooting)
7. [API Reference](#api-reference)

## 🎯 System Overview

The **MCP (Model Context Protocol) Printing System** is an enterprise-grade printing solution that replaces traditional Network Print Servers with AI-enhanced local management. The system provides universal coverage across all printing workflows in the Mini-ERP application.

### Architecture
```
Frontend (React/TypeScript) ↔ MCP Server (Express.js) ↔ Physical Printers
      ↑                           ↑                         ↑
   Web Interface            Local SQLite DB             Zebra/Generic
   Real-time UI            Job Queue & Cache           Network Printers
```

### Key Benefits
- **Zero Network Dependencies**: Local server eliminates network print server failures
- **Real-time Monitoring**: Live 3-point connection status indicators
- **AI-Enhanced Reliability**: Automatic failover and recovery strategies
- **Universal Coverage**: Single system handles all printing workflows
- **Template Management**: Database-driven template system with WYSIWYG editing

## 🚀 Feature List

### Core Printing Features
- ✅ **Zebra ZPL Label Printing** - Direct ZPL generation and transmission
- ✅ **Generic PDF Document Printing** - HTML-to-PDF conversion and printing  
- ✅ **Real-time Printer Discovery** - Automatic Bonjour/mDNS discovery
- ✅ **Health Monitoring** - Live printer status with response time tracking
- ✅ **Print Queue Management** - SQLite-based job persistence with WebSocket updates
- ✅ **Multi-format Support** - ZPL, PDF, plain text printing capabilities

### Template System
- ✅ **Database-driven Templates** - Supabase integration with RLS policies
- ✅ **Category Organization** - 8 predefined categories (shipping, warning, priority, etc.)
- ✅ **WYSIWYG Template Editor** - Fabric.js-based visual label designer
- ✅ **Template Types** - Quick Print and CT Label template categories
- ✅ **Dynamic Data Integration** - Real order data injection into templates
- ✅ **Labelary Preview** - Real-time ZPL preview via Labelary.com API

### User Interface Integration
- ✅ **CT Assignment Modal** - Immediate print after CT number assignment
- ✅ **Quick Print Modal** - 7 professional warehouse operation templates
- ✅ **Quick Print Page** - Dedicated page for quick label printing
- ✅ **Orders Page Integration** - Smart template selection based on order context
- ✅ **Label Designer** - Full MCP integration with test print functionality
- ✅ **Template Manager Page** - Standalone template management interface
- ✅ **Print Queue Page** - Dedicated print job monitoring and management
- ✅ **Settings Panel** - Complete administration interface with direct navigation

### Advanced Features
- ✅ **3-Point Connection Monitoring** - Visual Frontend ↔ MCP ↔ Printers status
- ✅ **Smart Template Selection** - Context-aware template recommendations
- ✅ **Intelligent Quantity Limits** - Automatic print quantity validation
- ✅ **4-Tier Backup System** - MCP → Static Config → ZPL Download → Error Notification
- ✅ **Offline Operation** - Local queue persistence during network outages
- ✅ **Error Recovery** - Automatic retry logic and graceful degradation

## ⚙️ Setup Instructions

### Prerequisites
- Node.js 18+ installed
- Network access to printers (IP addresses)
- Zebra printers configured for ZPL over raw TCP (port 9100)
- Generic printers supporting IPP or raw TCP printing

### 1. MCP Server Installation

```bash
# Navigate to MCP server directory
cd mcp-server

# Install dependencies
npm install

# Start the server
npm start

# For development with auto-reload
npm run dev
```

### 2. Environment Configuration

Create or update `.env` file in project root:

```env
# MCP Printing Server Configuration
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_API_KEY=your-secure-api-key-here
VITE_LOCATION=SB
```

### 3. Database Schema Setup

The MCP system requires the `label_templates` table in your Supabase database:

```sql
-- Apply the schema from supabase/label_templates_schema.sql
-- This creates the label_templates table with RLS policies
```

### 4. Printer Network Configuration

#### Zebra Printers
- Ensure printers are on the same network as the MCP server
- Configure for ZPL over Raw TCP on port 9100
- Note printer IP addresses for manual configuration if needed

#### Generic Printers
- Configure for IPP printing or Raw TCP
- Ensure printer drivers are installed on the server system
- Note printer queue names or IP addresses

### 5. Frontend Integration

The frontend automatically connects to the MCP server when configured. Key files:
- `src/hooks/useMCPPrinting.ts` - Main MCP integration hook
- `src/components/ui/mcp-connection-status.tsx` - Connection status indicator
- `src/components/admin/MCPSettings.tsx` - Administration interface

## 🖥️ User Interface Guide

### Connection Status Indicator

The MCP connection status is visible in the navigation bar:
- 🟢 **Connected** - MCP server online, printers discovered
- 🟡 **Warning** - Connected but some printers offline  
- 🔴 **Offline** - MCP server unavailable

### Quick Print Modal

Access via Orders page "Print" button:

1. **Template Selection** - Choose from 7 predefined templates:
   - HP External Label
   - Fragile Warning
   - Priority Processing
   - QC Hold
   - Inspection Required
   - Motherboard Testing
   - Return Processing

2. **Printer Selection** - Choose from discovered printers
3. **Quantity Input** - Set print quantity (1-100)
4. **Print Execution** - Click "Print Labels" for immediate printing

### Quick Print Page

Access via Navigation → Printing → Quick Print:

1. **Standalone Interface** - Dedicated page for quick label printing
2. **Template Grid** - Visual selection of available templates
3. **Template Details** - Shows size, DPI, and type information
4. **Print Configuration** - Printer selection and quantity input
5. **Preview Function** - Preview labels before printing via Labelary
6. **MCP Status** - Real-time connection status indicator

### CT Assignment Integration

When assigning CT numbers to orders:

1. Assign CT numbers using the CT modal
2. System automatically detects CT assignment
3. Print prompt appears with CT-specific template
4. One-click printing with order data pre-filled

### Printing Navigation

The Printing menu in the main navigation provides access to all printing features:
- **Label Designer** - Design label templates
- **Template Manager** - Manage saved templates
- **Quick Print** - Print labels quickly
- **Print Queue** - View print jobs
- **MCP Settings** - Printer configuration

### Label Designer Integration

Access via Navigation → Printing → Label Designer:

1. **Canvas Design** - Visual drag-and-drop interface
2. **Text Elements** - Add and format text with fonts and sizing
3. **QR Code Generation** - Dynamic QR codes with order data
4. **Template Saving** - Save to database with categories
5. **Test Printing** - Direct MCP integration for test prints
6. **Template Loading** - Load existing templates for editing

### Template Manager Page

Access via Navigation → Printing → Template Manager:

1. **Standalone Template Management** - Dedicated page for template operations
2. **Full Template Listing** - Complete view of all saved templates
3. **Search and Filter** - Find templates by name or category
4. **Template Actions** - Edit, preview, and delete templates
5. **Direct Designer Access** - Click to edit templates in Label Designer
6. **Category Organization** - View templates grouped by category

### Print Queue Page

Access via Navigation → Printing → Print Queue:

1. **Real-time Job Monitoring** - Live view of all print jobs
2. **Job Statistics** - Overview cards showing pending, printing, completed, failed
3. **Advanced Filtering** - Filter by status, time range, and search terms
4. **Job Details** - View order info, CT numbers, printer, and timestamps
5. **Job Actions** - Retry failed jobs or cancel pending ones
6. **Clear Completed** - Bulk remove completed jobs from queue
7. **Connection Status** - MCP server connection indicator

### Settings Administration

Access via Settings → MCP Printing or Navigation → Printing → MCP Settings:

#### Overview Tab
- System status dashboard
- Connection health metrics
- Printer count summary
- Print queue statistics

#### Printers Tab
- Discovered printer list with health status
- Manual printer addition
- Printer configuration management
- Real-time status monitoring

#### Print Queue Tab
- Active print jobs monitoring
- Job status tracking (pending, printing, completed, failed)
- Error details and retry options
- Queue management tools

#### Templates Tab
- Complete template management interface
- Search and filter capabilities
- Template editing (routes to Label Designer)
- Category organization
- Preview and delete options

#### Configuration Tab
- MCP server settings
- Connection parameters
- Timeout and retry configuration
- Queue persistence settings

## 📝 Template Management

### Template Categories

The system organizes templates into 8 categories:

1. **Shipping** - Delivery and logistics labels
2. **Warning** - Safety and handling warnings  
3. **Priority** - Urgent processing indicators
4. **QC** - Quality control related labels
5. **Inspection** - Inspection status indicators
6. **Testing** - Testing process labels
7. **Returns** - Return processing labels
8. **CT Labels** - CT number assignment labels

### Template Types

- **Quick Print** - General purpose templates for warehouse operations
- **CT Label** - Specialized templates for CT number assignment

### Creating Templates

1. Navigate to Printing → Template Manager or Settings → MCP Printing → Templates
2. Click "New Template" or edit existing template
3. Redirected to Label Designer with template editor
4. Design label using visual interface
5. Save with appropriate category and type
6. Template becomes available across all printing interfaces

### Predefined Templates

The system includes 3 sample templates:
- **HP External Label** - Standard HP order label
- **QC Hold Warning** - Quality control hold indicator  
- **CT Assignment** - CT number assignment label

Load additional predefined templates using the "Load Predefined" button.

## 🔧 Troubleshooting

### Connection Issues

**Problem**: MCP Connection Status shows "Offline"
```
Solution Steps:
1. Check if MCP server is running: `cd mcp-server && npm start`
2. Verify environment variable: VITE_MCP_API_URL=http://localhost:3001/api/mcp
3. Check browser console for connection errors
4. Ensure port 3001 is not blocked by firewall
```

**Problem**: Printers not discovered
```
Solution Steps:
1. Verify printers are on same network as MCP server
2. Check printer IP addresses are accessible
3. Ensure Bonjour/mDNS is enabled on network
4. Manually add printers in Settings → MCP Printing → Printers
```

### Printing Issues

**Problem**: Print jobs stuck in "Pending" status
```
Solution Steps:
1. Check printer health status in Settings → MCP Printing → Printers
2. Verify printer IP address and port accessibility
3. Check printer power and network connection
4. Review MCP server logs for detailed error messages
```

**Problem**: ZPL labels not printing correctly
```
Solution Steps:
1. Verify printer supports ZPL (Zebra printers)
2. Check label size settings match printer configuration
3. Test with Labelary preview to validate ZPL syntax
4. Ensure printer is set to raw TCP mode on port 9100
```

### Template Issues

**Problem**: Templates not loading in Label Designer
```
Solution Steps:
1. Check database connection to Supabase
2. Verify label_templates table exists with correct schema
3. Check user permissions for template access
4. Review browser console for API errors
```

**Problem**: Template saves failing
```
Solution Steps:
1. Verify user authentication and permissions
2. Check template data format and required fields
3. Ensure category values match allowed constraints
4. Review RLS policies on label_templates table
```

### Performance Issues

**Problem**: Slow print job processing
```
Solution Steps:
1. Check MCP server system resources
2. Verify SQLite database is not corrupted
3. Clear old print jobs from queue
4. Restart MCP server to refresh connections
```

## 📡 API Reference

### MCP Server Endpoints

#### Health Check
```
GET /api/mcp/health
Response: { status: "ok", timestamp: "2025-06-08T03:19:00Z" }
```

#### Printer Discovery
```
GET /api/mcp/printers/zebra
Response: [{ id, name, ip_address, model, status, health }]

GET /api/mcp/printers/generic  
Response: [{ id, name, ip_address, type, status, health }]
```

#### Print Operations
```
POST /api/mcp/zebra/print
Body: { printer_id, zpl, metadata }
Response: { success: true, job_id: "uuid" }

POST /api/mcp/generic/print
Body: { printer_id, content, format, metadata }
Response: { success: true, job_id: "uuid" }
```

#### Queue Management
```
GET /api/mcp/queue
Response: [{ id, printer_id, status, created_at, metadata }]

GET /api/mcp/queue/:job_id
Response: { id, status, printer_id, error, metadata }
```

### WebSocket Events

Connect to `ws://localhost:3001` for real-time updates:

#### Printer Events
```javascript
// Printer status change
{
  type: 'printer_status',
  printer_id: 'zebra_001',
  status: 'online|offline|warning',
  timestamp: '2025-06-08T03:19:00Z'
}
```

#### Job Events
```javascript
// Print job status update
{
  type: 'job_status',
  job_id: 'uuid',
  status: 'pending|printing|completed|failed',
  error?: 'error message',
  timestamp: '2025-06-08T03:19:00Z'
}
```

### Frontend Integration

#### useMCPPrinting Hook
```typescript
const {
  useZebraPrinters,
  useGenericPrinters, 
  printZebraLabel,
  printGenericDocument,
  isConnected,
  connectionError
} = useMCPPrinting()
```

#### Connection Status Component
```typescript
<MCPConnectionStatus 
  showDetails={true} 
  compact={false} 
/>
```

---

## 📞 Support

For technical support or feature requests:
- Review troubleshooting section above
- Check MCP server logs for detailed error messages
- Verify network connectivity and printer configurations
- Ensure all environment variables are properly configured

**System Status**: ✅ Production Ready  
**Last Updated**: June 8, 2025  
**Version**: 1.0.0 - Universal MCP Printing Integration