# Mini-ERP User Guide

## 🚀 Getting Started

Welcome to the Mini-ERP Order Management QC App! This comprehensive guide will help you navigate and use all features of the system.

## 📋 Table of Contents

1. [Login & Authentication](#login--authentication)
2. [Dashboard Overview](#dashboard-overview)
3. [Order Management](#order-management)
4. [CT Number Management](#ct-number-management)
5. [Quality Control Workflows](#quality-control-workflows)
6. [Printing System](#printing-system)
7. [Workflow Dashboards](#workflow-dashboards)
8. [Admin Features](#admin-features)
9. [Settings & Configuration](#settings--configuration)
10. [Troubleshooting](#troubleshooting)

## 🔐 Login & Authentication

### First Time Login
1. Navigate to the application URL
2. Use your provided credentials:
   - **Username**: Your email address
   - **Password**: Provided by system administrator
3. Click "Sign In"

### Password Requirements
- Minimum 8 characters
- Must include uppercase and lowercase letters
- Must include at least one number
- Must include at least one special character

### New Device Security
- First login from a new device may require additional verification
- Check your email for new device notifications
- Contact admin if you experience login issues

## 📊 Dashboard Overview

The main dashboard provides a comprehensive view of your system status and activities.

### Key Sections
- **System Status**: Real-time health indicators
- **Recent Orders**: Latest order activities
- **Quick Actions**: Frequently used operations
- **Notifications**: Important alerts and updates

### Role-Based Views
Different user roles see different dashboard elements:
- **Directors/Admins**: Full system overview
- **Warehouse Staff**: Operational metrics
- **QC Staff**: Quality control indicators
- **Accountants**: Financial summaries

## 📦 Order Management

### Creating Orders

#### Manual Order Creation
1. Click **"New Order"** button
2. Fill in required information:
   - **Customer**: Select from dropdown
   - **Part Number**: Enter part identifier
   - **Description**: Detailed part description
   - **Quantity**: Total order quantity
   - **Price**: Unit price (if permitted by role)
   - **Due Date**: Expected completion date
3. Click **"Create Order"** to save

#### Bulk Order Import
1. Navigate to **Orders** → **Import**
2. Download the CSV template
3. Fill in order data following the template format
4. Upload completed CSV file
5. Review validation results
6. Confirm import

### Order Views

#### Card View
- Visual representation of orders
- Color-coded status indicators
- Progressive quantity bars
- Quick action buttons

#### Row View (Default)
- Compact tabular format
- More orders visible at once
- Sortable columns
- Efficient for bulk operations

### Order Status Workflow

The system tracks orders through these main statuses:
1. **Pending Procurement Arrangement**
2. **Requested from Stock**
3. **Awaiting Kitting/Packing**
4. **In Kitting/Packing**
5. **Kitted/Packed - Awaiting Screening/QC**
6. **In Screening/QC**
7. **Screening/QC Passed - Ready for Invoice**
8. **Invoiced**
9. **Shipped/Delivered**

### Quick Actions
Each order card/row provides quick action buttons:
- **Assign CT**: Assign CT numbers
- **Print Labels**: Generate and print labels
- **Update Status**: Change order status
- **View History**: See order timeline
- **Request Cancel**: Initiate cancellation
- **Upload FAI**: Add FAI documents

## 🏷️ CT Number Management

### CT Number Format
- **Length**: Exactly 14 characters
- **Format**: Alphanumeric (A-Z, 0-9)
- **Case**: All uppercase
- **Uniqueness**: Must be unique across the system

### Assigning CT Numbers

#### Single Assignment
1. Click **"Assign CT"** on order card
2. Enter CT number manually or scan barcode
3. System validates uniqueness
4. Confirm assignment

#### Batch Assignment
1. Select multiple orders
2. Click **"Batch CT Assignment"**
3. Enter CT numbers for each order
4. System validates all numbers
5. Confirm batch assignment

### CT Number Validation
The system automatically:
- Checks format compliance
- Validates uniqueness
- Provides duplicate warnings
- Suggests alternatives if duplicates found

## 🔍 Quality Control Workflows

### QC Dashboard
Access via **Workflow** → **Quality Control**

#### Key Features
- **Orders Awaiting QC**: List of orders ready for quality checks
- **QC In Progress**: Currently active QC sessions
- **QC Hold Items**: Orders with quality issues
- **Completed QC**: Recently passed items

### QC Process
1. **Start QC Session**: Select order from awaiting list
2. **Perform Checks**: Follow standard QC procedures
3. **Record Results**: 
   - **Pass**: Move to ready for invoice
   - **Hold**: Add hold reason and notes
   - **Reject**: Document rejection reasons
4. **Generate Reports**: Create QC documentation

### FAI Document Management
1. **Upload FAI**: Click FAI button on order
2. **Select File**: Choose Excel file with images
3. **Extract Images**: System automatically extracts Sheet2 images
4. **Review**: Verify extracted images
5. **Save**: Confirm FAI document storage

## 🖨️ Printing System

### MCP Universal Printing
The system uses Model Context Protocol (MCP) for intelligent printing across all Zebra and generic printers.

### Navigation
Access printing features through the **Printing** menu in the main navigation:
- **Label Designer** - Design custom label templates
- **Template Manager** - Manage saved templates
- **Quick Print** - Print labels without orders
- **Print Queue** - Monitor print jobs
- **MCP Settings** - Configure printers and system

#### Printer Discovery
- **Automatic**: System discovers network printers
- **Manual**: Add printers via IP address
- **Health Monitoring**: Real-time printer status

#### Label Templates
1. **Quick Print Templates**:
   - HP External Label
   - Fragile Warning
   - Priority Shipping
   - QC Hold
   - Inspection Required
   - Motherboard Testing
   - Return Authorization

2. **CT Assignment Templates**:
   - Automatically generated based on order data
   - Includes CT number, customer info, part details
   - QR codes for tracking

#### Printing Process
1. **Select Template**: Choose appropriate label type
2. **Configure**: Adjust quantity and printer
3. **Preview**: Review label before printing
4. **Print**: Send to selected printer
5. **Verify**: Confirm successful printing

### Label Designer
Access via **Tools** → **Label Designer**

#### Features
- **WYSIWYG Editor**: Visual label design
- **Element Types**: Text, shapes, images, barcodes
- **Dynamic Data**: Insert order/customer variables
- **ZPL Generation**: Automatic ZPL code creation
- **Template Management**: Save and reuse designs

## 📋 Workflow Dashboards

### Kitting Dashboard
**Access**: Workflow → Kitting Operations

#### Functions
- **Task Assignment**: View assigned kitting tasks
- **Progress Tracking**: Monitor completion status
- **Quantity Management**: Update kitted quantities
- **Hold Management**: Handle kitting issues
- **Completion**: Mark kitting complete

### Quality Control Dashboard
**Access**: Workflow → Quality Control

#### Functions
- **QC Queue**: Orders awaiting quality checks
- **Active Sessions**: Current QC activities
- **Hold Management**: Quality hold situations
- **Approval Workflow**: QC approval process
- **Reporting**: Quality metrics and reports

### Invoicing Dashboard
**Access**: Workflow → Invoicing

#### Functions
- **Ready for Invoice**: QC-passed orders
- **Batch Processing**: Multiple order invoicing
- **Financial Calculations**: Pricing and totals
- **Invoice Generation**: Create invoice documents
- **Status Updates**: Mark orders as invoiced

## ⚙️ Admin Features

### User Management
**Access**: Settings → User Management

#### Functions
- **Add Users**: Create new user accounts
- **Role Assignment**: Assign appropriate roles
- **Permission Management**: Configure access levels
- **Account Status**: Enable/disable accounts
- **Password Reset**: Reset user passwords

### System Monitoring
**Access**: Settings → System Monitoring

#### Features
- **Performance Metrics**: System performance data
- **Error Monitoring**: Track and resolve errors
- **Security Events**: Monitor security activities
- **Audit Logs**: Review system changes
- **Health Checks**: System component status

### Configuration
**Access**: Settings → Configuration

#### Options
- **System Settings**: Global system configuration
- **Security Policies**: Security rule management
- **Integration Settings**: External system connections
- **Backup Configuration**: Data backup settings
- **Maintenance**: System maintenance tools

## 🔧 Settings & Configuration

### Personal Settings
- **Profile**: Update personal information
- **Preferences**: Customize interface settings
- **Notifications**: Configure alert preferences
- **Security**: Change password, view sessions

### System Settings (Admin Only)
- **General**: Application-wide settings
- **Security**: Security policy configuration
- **Integration**: External service connections
- **Backup**: Data backup and recovery settings

## 🚨 Troubleshooting

### Common Issues

#### Login Problems
**Issue**: Cannot log in
**Solutions**:
1. Check username/password accuracy
2. Verify caps lock status
3. Clear browser cache
4. Contact system administrator

#### Printing Issues
**Issue**: Labels not printing
**Solutions**:
1. Check printer status in MCP dashboard
2. Verify network connectivity
3. Restart printer if offline
4. Contact IT support

#### Performance Issues
**Issue**: System running slowly
**Solutions**:
1. Close unnecessary browser tabs
2. Clear browser cache
3. Check internet connection
4. Report to system administrator

#### Data Loading Issues
**Issue**: Data not loading or updating
**Solutions**:
1. Refresh the page
2. Check internet connection
3. Clear browser cache
4. Contact support if persistent

### Error Messages

#### "CT Number Already Exists"
- **Cause**: Attempting to use duplicate CT number
- **Solution**: Enter unique CT number or check existing assignments

#### "Permission Denied"
- **Cause**: Insufficient user permissions
- **Solution**: Contact administrator for access rights

#### "Network Error"
- **Cause**: Connection issues
- **Solution**: Check internet connection and retry

### Getting Help

#### Contact Information
- **Technical Support**: <EMAIL>
- **System Administrator**: <EMAIL>
- **Emergency Contact**: Call IT helpdesk

#### Self-Help Resources
- **User Guide**: This document
- **Video Tutorials**: Available in Help section
- **FAQ**: Frequently asked questions
- **Release Notes**: Latest feature updates

## 📱 Mobile Usage

### Responsive Design
- **Tablet Optimized**: Best experience on tablets
- **Phone Compatible**: Basic functionality on phones
- **Touch Friendly**: Large buttons and touch targets

### Mobile Features
- **Barcode Scanning**: Use device camera for CT numbers
- **Quick Actions**: Essential functions accessible
- **Offline Support**: Basic functionality when offline
- **PWA Support**: Install as app on device

## 🔒 Security Best Practices

### Password Security
- Use strong, unique passwords
- Change passwords regularly
- Don't share account credentials
- Log out when finished

### Data Protection
- Don't share sensitive information
- Use secure networks only
- Report suspicious activity
- Follow company data policies

### Session Management
- Log out from shared devices
- Don't leave sessions unattended
- Monitor active sessions
- Report unauthorized access

## 📈 Performance Tips

### Browser Optimization
- Use latest browser versions
- Enable JavaScript
- Clear cache regularly
- Disable unnecessary extensions

### Network Optimization
- Use stable internet connection
- Close bandwidth-heavy applications
- Report connectivity issues
- Use recommended browsers

### System Usage
- Log out when not in use
- Don't open multiple sessions
- Use efficient workflows
- Report performance issues

---

**Document Version**: 1.0  
**Last Updated**: January 10, 2025  
**Next Review**: April 10, 2025

For additional support or suggestions, please contact the system administrator.