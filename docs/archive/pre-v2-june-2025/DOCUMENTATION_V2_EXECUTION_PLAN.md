# Documentation V2 Execution Plan - Detailed Workflow
**Date**: June 13, 2025  
**Goal**: Read all 62 MD files, verify against code, create interconnected v2 documentation

## SESSION 1: Core System Analysis (4 hours)

### Files to Read (21 files)
```
ROOT LEVEL:
1. CLAUDE.md - Current AI context
2. REQUIREMENTS.md - Original 90-page requirements
3. README.md - Project overview
4. PROJECT_SUMMARY.md - High-level summary
5. COMPREHENSIVE_SYSTEM_STATUS.md - System state claims
6. SYSTEM_ANALYSIS_COMPREHENSIVE_2025-01-10.md - Latest analysis
7. FRONTEND_STATUS.md - Frontend completion claims
8. BACKEND_VS_FRONTEND_ANALYSIS.md - Integration status
9. WORKFLOW_COMPLETION.md - Workflow status
10. COMMIT_HISTORY.md - Development timeline
11. SESSION_SUMMARY_2025-01-10.md - Recent session work

DOCS LEVEL:
12. docs/DEVELOPMENT_STATUS.md - Development claims
13. docs/ARCHITECTURE.md - System architecture
14. docs/PHASES.md - Phase completion claims
15. docs/TODO.md - Task tracking
16. docs/SUPABASE_SETUP.md - Database setup
17. docs/API_DOCUMENTATION.md - API structure
18. docs/USER_GUIDE.md - User documentation
19. docs/MCP_PRINTING_GUIDE.md - Printing guide
20. docs/QUANTITY_NAMING_CONVENTION.md - Naming rules
21. docs/MASTER_STATUS_2025-01-13.md - My verification
```

### How to Read & Verify
1. **Extract Claims**: List what each file claims is complete
2. **Code Verification**: 
   ```
   - Check src/pages/ for claimed features
   - Verify src/hooks/ for data integration
   - Check supabase/ for database claims
   - Verify src/components/ for UI claims
   ```
3. **Create Summary**: 
   - What's actually implemented
   - What's partially done
   - What's missing

### Output
Create: `docs/v2/SYSTEM_STATE.md` (comprehensive verified truth with all details)

---

## SESSION 2: Order Management & CT System (3 hours)

### Files to Read (15 files)
```
REQUIREMENTS:
1. docs/requirements/csv-import-functionality.md
2. docs/requirements/ct-approval-workflow.md
3. docs/requirements/quantity-tracking-workflow.md
4. docs/requirements/invoicing-procurement-features.md
5. docs/requirements/system-administration-security.md

QUANTITY TRACKING:
6. docs/quantity-tracking-workflow/README.md
7. docs/quantity-tracking-workflow/requirements.md
8. docs/quantity-tracking-workflow/investigation-summary.md
9. docs/quantity-tracking-workflow/duplicate-schema-logic-analysis.md
10. docs/quantity-tracking-workflow/gap-analysis-and-implementation-plan.md
11. docs/quantity-tracking-workflow/implementation-plan.md
12. docs/quantity-tracking-workflow/execution-guide.md
13. docs/quantity-tracking-workflow/phase-1-completion-summary.md

CHECKER:
14. docs/checker/ct-workflow-printing-unification.md
15. WORKFLOW_COMPLETION.md
```

### Verification Focus
```
- src/pages/Orders.tsx - Order management UI
- src/components/orders/CTNumberModal.tsx - CT workflow
- src/hooks/useOrders.ts - Data fetching
- src/components/orders/QuantityStateBadges.tsx - Quantity UI
- Database: order_lines, ct_numbers, order_line_quantities tables
```

### Output
Create: `docs/v2/features/order-management/` module with:
- README.md (complete overview with all linkages)
- order-crud.md (full CRUD implementation details)
- ct-numbers.md (complete workflow, validation rules, duplicate handling)
- quantity-tracking.md (all 15 states, transitions, database mappings)
- order-ui-components.md (OrderCard, OrderRowCard, all UI elements)
- real-time-subscriptions.md (how live updates work)

---

## SESSION 3: Printing Ecosystem (3 hours)

### Files to Read (11 files)
```
REQUIREMENTS:
1. docs/requirements/label-printing-system.md
2. docs/requirements/print-after-ct-assignment.md
3. docs/requirements/universal-printing-system.md

FEATURES:
4. docs/features/mcp-printing-system.md
5. docs/features/label-designer.md
6. docs/features/ct-workflow.md

ROOT:
7. MODAL_SIZING_IMPROVEMENTS.md
8. docs/MCP_PRINTING_GUIDE.md

ANALYSIS:
9. supabase/quantity_analysis.md
10. docs/checker/ct-workflow-printing-unification.md
11. COMPACT_PROTOCOL.md
```

### Verification Focus
```
- src/hooks/useMCPPrinting.ts - MCP integration
- src/components/labelDesigner/LabelDesigner.tsx - Designer
- src/components/admin/TemplateManager.tsx - Templates
- src/utils/labelGeneration.ts - ZPL generation
- mcp-server/ directory - Backend status
```

### Output
Create: `docs/v2/features/printing-ecosystem/` module with:
- README.md (complete printing architecture overview)
- mcp-server-integration.md (full MCP implementation, WebSocket details, current status)
- label-designer-complete.md (Fabric.js implementation, all tools, canvas features)
- template-management.md (database schema, CRUD operations, categories)
- zpl-generation.md (current implementation, gaps, both systems)
- printing-workflows.md (CT print, quick print, all integration points)
- printer-configuration.md (Zebra setup, network config, troubleshooting)

---

## SESSION 4: Integrations & Workflows (2 hours)

### Files to Read (9 files)
```
REQUIREMENTS:
1. docs/requirements/whatsapp-integration.md
2. docs/requirements/fai-document-integration.md
3. docs/requirements/np-location-transfers.md
4. docs/requirements/returns-management.md
5. docs/requirements/luxury-ai-features.md
6. docs/requirements/ui-ux-design-system.md

TESTING:
7. GEMMA_TESTING_HANDOFF.md
8. src/tests/gemma/README.md
9. docs/testing/untested-features.md
```

### Verification Focus
```
- src/services/ApprovalWorkflowManager.ts - WhatsApp
- src/components/whatsapp/ - Approval modals
- src/hooks/useApprovalWorkflow.ts - Integration
- FAI components and storage integration
```

### Output
Create: `docs/v2/features/integration-layer/` module with:
- README.md (all external integrations overview)
- whatsapp-complete-system.md (Meta Cloud API, N8N, Evolution API setup)
- approval-workflows.md (4 workflow types, modals, database integration)
- fai-document-system.md (Excel extraction, image viewing, master images)
- n8n-webhook-configuration.md (all webhooks, templates, setup guide)
- external-services-status.md (what's configured, what's pending)

---

## SESSION 5: Gap Analysis & Cleanup (2 hours)

### Files to Read (6 files)
```
CHECKER ASSESSMENTS:
1. docs/checker/comprehensive-gap-analysis.md
2. docs/checker/pending-updates.md
3. docs/checker/sprint-requirements-assessment/overall-compliance-scorecard.md
4. docs/checker/sprint-requirements-assessment/assessment-session-memory.md
5. docs/checker/sprint-requirements-assessment/new-features-requirements-gap.md
6. deployment-guide.md
```

### Tasks
1. Compare all gap analyses with verified state
2. Create final navigation README
3. Update CLAUDE.md to reference v2
4. Move all old docs to `/docs/archive/june-2025-sprint/`
5. Create feature relationship diagram

### Final Output Structure
```
docs/v2/
├── README.md (Complete navigation with feature relationship map)
├── SYSTEM_STATE.md (Comprehensive verified truth - 500+ lines)
├── ARCHITECTURE.md (Complete technical architecture)
├── features/
│   ├── order-management/ (6 detailed files)
│   ├── printing-ecosystem/ (7 detailed files)
│   ├── integration-layer/ (6 detailed files)
│   ├── user-workflows/ (4 detailed files)
│   └── database-schema/ (complete schema documentation)
├── gaps-and-roadmap.md (detailed gap analysis)
├── testing-guide.md (comprehensive testing procedures)
└── deployment-guide.md (production deployment steps)
```

### Documentation Standards
1. **Detail Level**: Each file should be 200-500 lines with complete information
2. **Code Examples**: Include actual code snippets from implementation
3. **Database Details**: Show exact table structures and relationships
4. **Error Scenarios**: Document known issues and solutions
5. **Configuration**: Include all ENV variables and settings needed

---

## Success Criteria
1. Every claim verified against actual code
2. All feature relationships documented
3. No duplicate information
4. Clear "what works" vs "what doesn't"
5. AI can read one module and understand complete feature context

---

## CRITICAL FOR MULTI-SESSION AI EXECUTION

### Session Handoff Files
Each session MUST create these files for the next AI:

1. **`docs/v2/SESSION_PROGRESS.md`** (Updated after each session)
   ```markdown
   # Documentation V2 Progress Tracker
   
   ## Session 1 - Core System (Completed: June 13, 2025)
   - Files Read: 21/21 ✅
   - Verified Claims:
     - Authentication: 100% complete ✅
     - Order Management: 90% complete (CSV import missing)
     - MCP Printing: Frontend complete, backend disabled
   - Files Created:
     - docs/v2/SYSTEM_STATE.md ✅
   - Next Session Should: Start with Session 2 files
   
   ## Session 2 - Order Management (Pending)
   - Files to Read: Listed in execution plan
   - Focus Areas: Order CRUD, CT workflow, quantities
   ```

2. **`docs/v2/VERIFICATION_LOG.md`** (Running log of all verifications)
   ```markdown
   # Code Verification Log
   
   ## Authentication System
   - Claim: "Complete with 8 roles"
   - Verified: src/contexts/AuthContext.tsx ✅
   - Reality: All 8 roles implemented, RLS working
   
   ## MCP Printing
   - Claim: "Universal integration complete"
   - Verified: src/hooks/useMCPPrinting.ts ✅
   - Reality: Frontend complete, backend missing
   ```

3. **`docs/v2/DISCREPANCIES.md`** (Critical findings)
   ```markdown
   # Documentation vs Reality Discrepancies
   
   1. **MCP Backend**: Claimed complete, actually disabled
   2. **CSV Import**: In requirements, not implemented
   3. **Template Delete**: UI exists, handler missing
   ```

### Missing from Current Plan

1. **Checker Assessment Files**: Need to add Session 0 to read all checker files FIRST
   - docs/checker/sprint-requirements-assessment/* (6 files)
   - These contain critical gap analysis already done

2. **User Workflows**: Not assigned to any session
   - SB location workflow
   - NP location workflow  
   - Approval chains
   - Need to add to Session 4

3. **Database Schema Documentation**: Not explicitly covered
   - Need to verify all 38 tables
   - Document relationships
   - Add to Session 5

4. **Environment & Configuration**: Missing from plan
   - All ENV variables
   - Deployment configuration
   - Security settings

### Updated Session Structure

**Session 0 (NEW)**: Read Previous Analysis (1 hour)
- All checker/ files
- Previous gap analyses
- Create initial discrepancy list

**Sessions 1-5**: As planned, but each MUST:
- Update SESSION_PROGRESS.md
- Add to VERIFICATION_LOG.md
- Update DISCREPANCIES.md
- Leave clear instructions for next session

### AI Context Instructions
Add to each session output:
```markdown
## For Next AI Session
1. Read docs/v2/SESSION_PROGRESS.md first
2. Check DISCREPANCIES.md for critical issues
3. Continue from Session X in execution plan
4. Key finding from this session: [summary]
```