# Documentation V2 Reorganization Plan
**Date**: June 13, 2025  
**Purpose**: Create interconnected, verified documentation structure

## Proposed Structure: `/docs/v2/`

### 1. Core System
```
docs/v2/
├── README.md (Navigation map with feature linkages)
├── SYSTEM_STATE.md (Single source of truth - verified)
├── ARCHITECTURE.md (How everything connects)
└── QUICK_START.md (For new developers)
```

### 2. Feature Modules (with linkages)
```
docs/v2/features/
├── printing-ecosystem/
│   ├── README.md (Links: templates, labels, CT workflow)
│   ├── mcp-server.md
│   ├── label-designer.md
│   └── template-management.md
├── order-management/
│   ├── README.md (Links: CT, quantities, WhatsApp)
│   ├── order-crud.md
│   ├── ct-numbers.md
│   └── quantity-tracking.md
├── integration-layer/
│   ├── README.md (External services)
│   ├── whatsapp-approvals.md
│   └── n8n-webhooks.md
└── user-workflows/
    ├── sb-location-flow.md
    ├── np-location-flow.md
    └── approval-chains.md
```

### 3. Verification Sessions Plan

**Session 1: Core Infrastructure (3-4 hours)**
- Read: CLAUDE.md, REQUIREMENTS.md, all root-level status files
- Verify: Database tables, authentication, basic UI
- Output: Verified SYSTEM_STATE.md

**Session 2: Order & CT System (2-3 hours)**  
- Read: All order-related docs, CT workflow docs
- Verify: Order CRUD, CT modal, quantity tracking
- Output: order-management/ module

**Session 3: Printing Ecosystem (2-3 hours)**
- Read: MCP, label designer, template docs
- Verify: Integration between components
- Output: printing-ecosystem/ module with clear linkages

**Session 4: Integrations & Workflows (2 hours)**
- Read: WhatsApp, checker files, gap analyses
- Verify: External service readiness
- Output: integration-layer/ module

**Session 5: Consolidation (1 hour)**
- Archive old docs to `/docs/archive/june-sprint/`
- Create navigation README with feature relationship map
- Update CLAUDE.md to reference v2 structure

## Feature Linkage Map

```mermaid
graph TD
    Orders[Order Management] --> CT[CT Numbers]
    CT --> Printing[Printing System]
    Printing --> Templates[Template Manager]
    Printing --> Designer[Label Designer]
    Orders --> Quantities[Quantity Tracking]
    Quantities --> WhatsApp[WhatsApp Alerts]
    CT --> Approvals[Approval Workflows]
    Approvals --> WhatsApp
```

## Benefits
1. **Clear Relationships**: Each feature shows what it connects to
2. **Verified Truth**: All docs checked against actual code
3. **AI-Friendly**: Easy to reference related features
4. **No Duplication**: Single source for each topic
5. **Version History**: Old docs archived, not deleted

## Execution Timeline
- Day 1: Sessions 1-2 (Core + Orders)
- Day 2: Sessions 3-4 (Printing + Integrations)  
- Day 3: Session 5 (Consolidation)

Total: ~12 hours over 3 days to have pristine documentation