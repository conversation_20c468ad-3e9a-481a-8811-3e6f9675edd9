# Label Designer System Documentation

## Overview
The Label Designer is a professional WYSIWYG (What You See Is What You Get) label design interface built with Fabric.js. It allows users to create custom label templates with text, shapes, barcodes, and QR codes for Zebra printer output.

## Current Implementation Status

### ✅ Visual Designer (100% Complete)
- **Location**: `src/components/labelDesigner/LabelDesigner.tsx`
- **Features**:
  - Full canvas-based design interface
  - Comprehensive toolbar
  - Object manipulation (move, resize, rotate)
  - Undo/redo functionality
  - Grid system for alignment
  - Zoom controls
  - Save to database

### ⚠️ ZPL Generation (70% Complete)
- **Basic ZPL**: Placeholder implementation in designer
- **Advanced ZPL**: Complete utilities exist separately
- **Gap**: Canvas-to-ZPL conversion not fully connected

## Components & Architecture

### Main Components
```
LabelDesigner.tsx (839 lines)
├── Toolbar
│   ├── Text Tool
│   ├── Barcode Tool (Code128, Code39, EAN13, UPC)
│   ├── QR Code Tool
│   ├── Shape Tools (Rectangle, Circle, Line)
│   └── Actions (Undo, Redo, Delete, Clear)
├── Canvas Area
│   ├── Fabric.js Canvas
│   ├── Grid Display
│   ├── Object Selection
│   └── Property Updates
├── Property Panel
│   ├── Text Properties (Font, Size, Color)
│   ├── Shape Properties (Fill, Stroke)
│   └── Position & Size
└── Actions
    ├── Save Template
    ├── Print (via MCP)
    └── Export ZPL
```

### Supporting Files
- `src/utils/labelGeneration.ts` - Predefined ZPL templates
- `src/utils/zplGenerator.ts` - Canvas to ZPL conversion class
- `src/utils/templateLoader.ts` - Template initialization

## Features

### Toolbar Tools
1. **Select Tool** - Default selection mode
2. **Text Tool** - Add text with formatting
3. **Barcode Tool** - Generate various barcode types
4. **QR Code Tool** - Create QR codes
5. **Rectangle Tool** - Draw rectangles
6. **Circle Tool** - Draw circles
7. **Line Tool** - Draw lines

### Canvas Features
- **Grid System**: 10-pixel grid for alignment
- **Zoom**: 50% to 200% zoom levels
- **Selection**: Multi-select with Shift key
- **Alignment**: Snap to grid functionality
- **History**: Undo/redo up to 50 actions

### Dynamic Fields
The system supports dynamic field replacement:
- `{{orderUID}}` - Order unique identifier
- `{{partNumber}}` - Part number
- `{{customerName}}` - Customer name
- `{{quantity}}` - Order quantity
- `{{ctNumber}}` - CT serial number
- `{{date}}` - Current date

## Database Integration

### Schema
```sql
CREATE TABLE label_templates (
  id UUID PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  template_type VARCHAR(50),
  label_size JSONB,
  zpl_template TEXT,
  canvas_data JSONB,
  is_active BOOLEAN DEFAULT true,
  created_by UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
);
```

### Template Categories
- `shipping` - Shipping labels
- `warning` - Warning labels
- `priority` - Priority/urgent labels
- `qc` - Quality control labels
- `inspection` - Inspection labels
- `testing` - Testing labels
- `returns` - Return labels
- `ct_labels` - CT number labels

## Usage

### Creating a New Label
1. Navigate to Settings → Label Designer
2. Set label dimensions (width × height)
3. Use toolbar to add elements
4. Configure properties in right panel
5. Save template with name and category

### Editing Existing Template
1. Go to Settings → MCP Printing → Templates
2. Click Edit on desired template
3. Modify in Label Designer
4. Save to update

### Using Templates
Templates are available in:
- CT Number Modal (for CT labels)
- Quick Print Modal (for operation labels)
- Print buttons throughout the system

## ZPL Generation

### Current State
Two separate ZPL systems exist:

1. **Basic Placeholder** (in LabelDesigner.tsx):
```javascript
const generateZPL = () => {
  return '^XA\n^FO50,50\n^ADN,36,20\n^FDTest Label^FS\n^XZ';
};
```

2. **Comprehensive System** (in labelGeneration.ts):
- 7 predefined templates
- Dynamic data insertion
- Proper ZPL formatting
- Labelary preview URLs

### Integration Gap
The visual designer creates canvas data but doesn't convert it to actual ZPL. The ZPLGenerator class exists but needs to be connected to the designer's output.

## Known Issues

1. **ZPL Generation**: Canvas elements not converted to ZPL
2. **Font Limitations**: Limited to Zebra printer fonts
3. **Preview**: Labelary preview uses static templates
4. **Complex Graphics**: Limited support for images

## Future Enhancements

1. **Complete ZPL Integration**:
   - Connect canvas to ZPLGenerator
   - Real-time ZPL preview
   - Advanced positioning

2. **Enhanced Features**:
   - Image upload support
   - Advanced text formatting
   - Template versioning
   - Collaborative editing

3. **Printer Integration**:
   - Direct print preview
   - Printer-specific adjustments
   - Multi-format export

## Testing

### Manual Testing
1. Create a label with all element types
2. Save and verify database storage
3. Load template and verify restoration
4. Test print via MCP integration

### Test Scenarios
- Text with various fonts/sizes
- All barcode types
- QR codes with different data
- Shape combinations
- Dynamic field replacement