# MCP Printing System Documentation

## Overview
The MCP (Model Context Protocol) Printing System is a modern replacement for traditional Network Print Server (NPS) integration. It provides enterprise-grade printing capabilities with real-time monitoring, automatic failover, and comprehensive job management.

## Current Implementation Status

### ✅ Frontend Implementation (100% Complete)
- **Location**: `src/hooks/useMCPPrinting.ts`
- **Features**:
  - Complete API client for MCP server communication
  - WebSocket integration for real-time updates
  - Printer discovery and health monitoring
  - Job queue management
  - 4-tier failsafe architecture
  - Automatic retry logic

### ⚠️ Backend Implementation (Intentionally Disabled)
- **Status**: Backend server exists but ENV disabled to prevent console spam during development
- **Location**: Should be in `mcp-server/` directory
- **Note**: Frontend configured to connect to `http://localhost:3001/api/mcp`

## Architecture

### Frontend Components
```
useMCPPrinting Hook (425 lines)
├── Printer Discovery
│   ├── discoverPrinters()
│   ├── getPrinterHealth()
│   └── Real-time status updates
├── Print Job Management
│   ├── sendPrintJob()
│   ├── getJobStatus()
│   └── cancelPrintJob()
├── Template Management
│   ├── getTemplates()
│   └── Template selection logic
└── WebSocket Client
    ├── Connection management
    ├── Real-time job updates
    └── Auto-reconnection
```

### Integration Points
1. **CT Number Modal** - Print after CT assignment
2. **Quick Print Modal** - Warehouse operation labels
3. **Orders Page** - Print buttons for all orders
4. **Label Designer** - Test print functionality
5. **Settings Page** - MCP configuration and monitoring

## Configuration

### Environment Variables
```bash
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_API_KEY=your-secure-api-key-here
```

### Settings Interface
- Located in `src/components/admin/MCPSettings.tsx`
- Features:
  - Printer management
  - Job queue monitoring
  - Template administration
  - Connection status

## Usage Examples

### Basic Print Job
```typescript
const { sendPrintJob, isConnected } = useMCPPrinting();

if (isConnected) {
  const result = await sendPrintJob({
    printerId: 'zebra-warehouse-1',
    jobType: 'zpl',
    content: zplContent,
    metadata: {
      orderUID: 'A001',
      userId: currentUser.id
    }
  });
}
```

### Printer Discovery
```typescript
const { discoverPrinters, printers } = useMCPPrinting();

await discoverPrinters();
// Returns array of discovered printers with health status
```

## Failsafe Architecture

The system implements a 4-tier failsafe:
1. **Primary**: MCP server printing
2. **Fallback 1**: Static printer configuration
3. **Fallback 2**: ZPL file download
4. **Fallback 3**: Error notification to user

## Known Issues & TODOs

### To Enable MCP Backend:
1. Implement Express.js server in `mcp-server/`
2. Set up SQLite database for job queue
3. Implement printer discovery via Bonjour/mDNS
4. Configure WebSocket server
5. Enable ENV variable to activate

### Current Workarounds:
- System falls back to file download when MCP unavailable
- Static printer configuration used as backup
- Manual print via downloaded ZPL files

## Testing

When MCP backend is enabled:
1. Start MCP server: `cd mcp-server && npm start`
2. Verify connection in Settings → MCP Printing
3. Test print from any integrated interface
4. Monitor job queue in admin panel

## Future Enhancements
- PDF printing support
- Multi-location printer management
- Print job scheduling
- Advanced queue priorities
- Printer maintenance alerts