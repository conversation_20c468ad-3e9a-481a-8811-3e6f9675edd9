# Untested Features Documentation

## Overview
This document lists features that have been built but require testing or configuration before they can be considered production-ready. Each feature includes testing procedures and configuration requirements.

## 1. MCP Printing Backend Server

### Status
- **Frontend**: ✅ Complete and ready
- **Backend**: ❌ Disabled to prevent console spam
- **Configuration**: ⚠️ Needs environment setup

### Testing Requirements
1. **Enable MCP Server**:
   ```bash
   cd mcp-server
   npm install
   npm start
   ```

2. **Configure Environment**:
   ```bash
   VITE_MCP_API_URL=http://localhost:3001/api/mcp
   VITE_MCP_API_KEY=your-secure-api-key
   ```

3. **Test Printer Discovery**:
   - Connect Zebra printer to network
   - Navigate to Settings → MCP Printing
   - Click "Discover Printers"
   - Verify printer appears with health status

4. **Test Print Job**:
   - Create order with CT numbers
   - Click Print button
   - Select discovered printer
   - Verify label prints correctly

### Known Issues
- Console spam when no printers connected
- WebSocket reconnection attempts flood logs
- Need proper error boundaries

---

## 2. Label Designer ZPL Generation

### Status
- **Visual Designer**: ✅ Complete
- **ZPL Generation**: ⚠️ Not connected to canvas
- **Templates**: ✅ Predefined templates work

### Testing Requirements
1. **Connect ZPL Generator**:
   ```javascript
   // In LabelDesigner.tsx, replace placeholder with:
   import { ZPLGenerator } from '../utils/zplGenerator';
   
   const generateZPL = () => {
     const generator = new ZPLGenerator(canvas);
     return generator.generate();
   };
   ```

2. **Test Canvas to ZPL**:
   - Create label with text, barcode, shapes
   - Click "Generate ZPL"
   - Copy ZPL to Labelary.com
   - Verify preview matches canvas

3. **Test Dynamic Fields**:
   - Add {{orderUID}} to label
   - Print from order context
   - Verify field replacement

### Integration Gap
- Canvas serialization to ZPL needs completion
- Font mapping to Zebra fonts required
- Position calculations need calibration

---

## 3. WhatsApp N8N Integration

### Status
- **Frontend**: ✅ Complete
- **Webhook URLs**: ❌ Not configured
- **Evolution API**: ⚠️ Needs setup

### Configuration Requirements
1. **N8N Setup**:
   ```bash
   # Install N8N
   npm install -g n8n
   
   # Start N8N
   n8n start
   ```

2. **Configure Webhooks**:
   ```bash
   VITE_N8N_WEBHOOK_URL=http://your-n8n-instance.com/webhook/xxx
   VITE_EVOLUTION_API_URL=http://your-evolution-api.com
   ```

3. **Evolution API Setup**:
   - Install Evolution API
   - Configure WhatsApp instance
   - Get API credentials
   - Update settings in app

### Testing Procedures
1. **Test Approval Workflow**:
   - Create duplicate CT number
   - Trigger approval request
   - Verify WhatsApp message sent
   - Test approval/rejection flow

2. **Test QC Rejection Alert**:
   - Reject item at QC stage
   - Verify alert sent to director group
   - Check message formatting

3. **Test Procurement Query**:
   - Use procurement snippet generator
   - Send to vendor via WhatsApp
   - Verify images and text format

---

## 4. Physical Printer Configuration

### Status
- **Software**: ✅ Ready
- **Hardware**: ❌ Not configured
- **Network**: ⚠️ Needs setup

### Setup Requirements
1. **Zebra Printer Setup**:
   - Connect printer to network
   - Note IP address
   - Configure in Settings → Printers
   - Set as default for location

2. **Label Stock Configuration**:
   - Load appropriate label size
   - Calibrate printer
   - Test with sample print

3. **Network Configuration**:
   - Ensure printer on same subnet
   - Open required ports (9100)
   - Configure firewall if needed

### Testing Matrix
| Label Type | Size | Template | Test Status |
|------------|------|----------|-------------|
| CT Label | 4"×2" | CT Default | ⏳ Pending |
| Box Label | 4"×6" | HP External | ⏳ Pending |
| Warning | 2"×1" | Fragile | ⏳ Pending |

---

## 5. Template Delete Functionality

### Status
- **UI Button**: ✅ Exists
- **Handler**: ❌ Not implemented
- **Confirmation**: ⚠️ Needs dialog

### Implementation Required
```typescript
const handleDeleteTemplate = async (templateId: string) => {
  if (!confirm('Delete this template?')) return;
  
  const { error } = await supabase
    .from('label_templates')
    .delete()
    .eq('id', templateId);
    
  if (!error) {
    refetch();
    toast.success('Template deleted');
  }
};
```

### Testing Steps
1. Create test template
2. Try to delete it
3. Verify confirmation dialog
4. Check database removal
5. Verify UI updates

---

## 6. Inter-Location Transfers

### Status
- **Database**: ✅ Tables exist
- **UI**: ⚠️ Basic implementation
- **Workflow**: ❌ Not fully tested

### Testing Requirements
1. **Create Transfer**:
   - Select items at SB location
   - Create transfer to NP
   - Print transfer slip
   - Verify database records

2. **Receive Transfer**:
   - Login as NP user
   - View incoming transfers
   - Mark as received
   - Verify quantity updates

3. **Transfer Rejection**:
   - Reject transfer item
   - Verify return workflow
   - Check quantity restoration

---

## 7. Email Integration

### Status
- **Templates**: ✅ HTML generation ready
- **SMTP**: ❌ Not configured
- **Scheduling**: ⚠️ Needs cron setup

### Configuration
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=app-specific-password
```

### Testing
1. Configure SMTP settings
2. Test welcome email on user creation
3. Test daily summary generation
4. Verify email formatting

---

## 8. Performance Under Load

### Status
- **Single User**: ✅ Tested
- **Concurrent Users**: ❌ Not tested
- **Large Dataset**: ⚠️ Limited testing

### Load Testing Plan
1. **User Simulation**:
   - 30 concurrent users
   - Mixed operations
   - Monitor response times

2. **Data Volume**:
   - 10,000+ orders
   - 100,000+ CT numbers
   - Complex queries

3. **Real-time Stress**:
   - Multiple subscriptions
   - Rapid updates
   - Connection stability

### Metrics to Monitor
- Page load times
- API response times
- WebSocket stability
- Memory usage
- Database query performance

---

## Testing Priority Matrix

| Priority | Feature | Business Impact | Complexity |
|----------|---------|----------------|------------|
| 🔴 High | MCP Printing | Critical for operations | Medium |
| 🔴 High | WhatsApp Integration | Director communications | High |
| 🟡 Medium | Label ZPL Generation | Improves flexibility | Medium |
| 🟡 Medium | Physical Printers | Required for go-live | Low |
| 🟢 Low | Template Delete | Nice to have | Low |
| 🟢 Low | Email Integration | Future enhancement | Medium |

---

## Pre-Production Checklist

### Essential (Must Complete)
- [ ] Enable MCP backend server
- [ ] Configure at least one Zebra printer
- [ ] Setup WhatsApp integration
- [ ] Complete ZPL generation
- [ ] Test with 5+ concurrent users

### Recommended
- [ ] Implement template deletion
- [ ] Configure email notifications
- [ ] Setup monitoring/logging
- [ ] Create backup procedures
- [ ] Document printer troubleshooting

### Optional
- [ ] Load testing with 30 users
- [ ] Advanced email templates
- [ ] Automated testing suite
- [ ] Performance optimization

---

## Conclusion
The system is functionally complete but requires configuration and testing of external integrations (printers, WhatsApp, email) before production deployment. Most untested features are related to hardware/external service configuration rather than core functionality gaps.