# Order Management Module - Complete Documentation

## Overview
The Order Management module is the core of the Mini-ERP system, providing comprehensive order lifecycle management from creation through delivery. This module integrates CT number tracking, quantity management, and multi-modal interfaces for various operational roles.

## Module Status: 85% Complete

### ✅ Fully Implemented
- Order CRUD operations with real-time updates
- CT number assignment and validation system
- Quantity tracking with 14-state progression
- Professional workflow dashboards (Kitting, QC, Invoicing)
- Dual layout system (cards and rows)
- Advanced filtering and search

### ⚠️ Gaps
- CSV import functionality (in requirements, not implemented)
- CT auto-progress bug for new orders
- Multi-PO invoice warning system
- Individual field click-to-copy

## Architecture

### Component Structure
```
Order Management System
├── Pages
│   └── Orders.tsx (1,821 lines) - Main order interface
├── Components
│   ├── orders/
│   │   ├── CTNumberModal.tsx (1,439 lines) - Unified CT workflow
│   │   ├── OrderCard.tsx - Card layout component
│   │   ├── OrderRowCard.tsx - Row layout component
│   │   ├── QuantityStateBadges.tsx - Visual quantity display
│   │   └── GlobalPendingDisplay.tsx - Customer pending summary
│   └── dashboards/
│       ├── KittingDashboard.tsx (1,140 lines)
│       ├── QCDashboard.tsx (1,227 lines)
│       └── InvoicingDashboard.tsx (524 lines)
├── Hooks
│   ├── useOrders.ts - Order data management
│   ├── useCTNumbers.ts - CT operations
│   ├── useQuantityTracking.ts - Quantity state management
│   ├── useKittingQueue.ts (406 lines) - Kitting workflow
│   ├── useQCQueue.ts (610 lines) - QC workflow
│   └── useInvoicing.ts (524 lines) - Invoicing workflow
└── Services
    ├── orderService.ts - Business logic
    └── quantityService.ts - State transitions
```

### Database Schema
```sql
-- Main order table
CREATE TABLE order_lines (
  id UUID PRIMARY KEY,
  uid VARCHAR(10) UNIQUE NOT NULL, -- A001, A002, etc.
  customer_name VARCHAR(255),
  part_number VARCHAR(100),
  part_description TEXT,
  po_number VARCHAR(100),
  quantity INTEGER NOT NULL,
  status VARCHAR(50),
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
);

-- CT number tracking
CREATE TABLE ct_numbers (
  id UUID PRIMARY KEY,
  order_line_id UUID REFERENCES order_lines(id),
  ct_number VARCHAR(14) NOT NULL, -- 14 alphanumeric
  status VARCHAR(50),
  created_by UUID,
  created_at TIMESTAMPTZ
);

-- Quantity state tracking
CREATE TABLE order_line_quantities (
  id UUID PRIMARY KEY,
  order_line_id UUID REFERENCES order_lines(id),
  -- 14 quantity state columns (SHORT names)
  pending_procurement INTEGER DEFAULT 0,
  requested_from_stock INTEGER DEFAULT 0,
  -- ... other states
);

-- Audit trail
CREATE TABLE quantity_logs (
  id UUID PRIMARY KEY,
  order_line_id UUID,
  from_state VARCHAR(50),
  to_state VARCHAR(50),
  quantity INTEGER,
  reason TEXT,
  created_by UUID,
  created_at TIMESTAMPTZ
);
```

## Core Features

### 1. Order Creation & Management

#### Manual Order Creation
- Form-based entry with validation
- Real-time duplicate detection
- Automatic UID generation (A001, A002, etc.)
- Immediate quantity tracking initialization

#### CSV Import (⚠️ Not Implemented)
**Requirements exist but feature missing:**
- 17-column template system
- Batch validation and processing
- Progress tracking and error handling
- Google Sheets integration via N8N

### 2. CT Number System

#### Unified Modal Architecture
The CT system uses a single modal with mode-based operation:

```typescript
interface CTNumberModalProps {
  mode?: 'assignment' | 'print';
  orderLine: OrderLineWithDetails;
}
```

#### Assignment Mode Features
- 14-character alphanumeric validation
- Real-time duplicate detection
- Approval workflow for duplicates
- Batch CT assignment
- Automatic quantity progression options

#### Print Mode Features  
- Checkbox selection for partial printing
- Smart template recommendations
- MCP printer integration
- Reprint capabilities for damaged labels

#### Known Issues
- **CT Auto-Progress Bug**: New orders in `pending_procurement` state fail to auto-progress
- **Location**: `CTNumberModal.tsx` lines 510-642
- **Impact**: Users must manually progress quantities

### 3. Quantity Tracking System

#### 14-State Progressive Workflow
```
TotalOrderQuantity
├── PendingProcurementArrangement
├── RequestedFromStock
├── AwaitingKittingPacking
├── InKittingPacking
├── KittedPacked_AwaitingScreeningQC
├── InScreeningQC
├── ScreeningQCPassed_ReadyForInvoice
├── Invoiced
└── ShippedDelivered
```

#### Hold States
- `OnHoldAtKittingPacking` - Issues during kitting
- `OnHoldAtScreeningQC` - QC problems
- `ScreeningQCRejected` - Returns to procurement

#### Implementation Details
- **Database**: Uses SHORT column names
- **Frontend**: Uses LONG descriptive names
- **Mapping**: `transition_quantity()` function handles translation
- **Real-time**: WebSocket subscriptions for live updates

### 4. Workflow Dashboards

#### Kitting Dashboard
**Purpose**: SB staff daily operations
- Real-time queue with priority sorting
- Task assignment workflow
- Progress tracking with time estimates
- Hold/complete/reject actions

#### QC Dashboard
**Purpose**: Quality control operations
- 4 QC types (Visual, Functional, Documentation, Final)
- Hold management with reasons
- Pass/fail workflows
- Re-work identification

#### Invoicing Dashboard
**Purpose**: Accountant processing
- Ready-to-invoice queue
- Batch processing capabilities
- Multi-PO warning (⚠️ not implemented)
- Customer grouping and filtering

## Integration Points

### 1. WhatsApp Integration
- QC rejection notifications
- Approval request messages
- Procurement snippets
- Status: Frontend complete, webhooks pending

### 2. MCP Printing System
- Label generation from CT data
- Template selection
- Printer management
- Status: Frontend complete, backend disabled

### 3. Real-time Updates
- Supabase subscriptions
- Cross-client synchronization
- Optimistic UI updates
- WebSocket stability enhancements

## User Workflows

### Order Creation Workflow
1. Navigate to Orders page
2. Click "Create Order" button
3. Fill order details form
4. System generates UID
5. Order saved with quantity initialization

### CT Assignment Workflow
1. Click # button on order card
2. Modal opens in assignment mode
3. Scan/enter CT numbers
4. System validates uniqueness
5. Choose workflow option (save/print/progress)
6. System executes selected workflow

### Kitting Workflow (SB Staff)
1. Access Kitting Dashboard
2. View prioritized queue
3. Click "Assign to Me"
4. Start task and view details
5. Complete/hold/reject with reasons
6. Automatic quantity transitions

### QC Workflow
1. Access QC Dashboard
2. Select item from queue
3. Perform QC checks
4. Capture photos if needed
5. Pass/fail/hold decision
6. WhatsApp alerts for rejections

### Invoicing Workflow
1. Access Invoicing Dashboard
2. View ready items
3. Select for batch processing
4. Create invoice
5. Update status to invoiced

## Business Rules

### UID Generation
- Format: `{PREFIX}{NUMBER}` (e.g., A001)
- Sequential numbering
- Prefix configurable per location
- No gaps in sequence

### CT Number Rules
- 14 characters exactly
- Alphanumeric only
- ALL UPPERCASE
- Must be unique (with override option)
- Approval required for duplicates

### Quantity Rules
- Conservation: Total never exceeds original
- Validation: Sufficient quantity for transitions
- Audit: All changes logged with user/reason
- Permissions: Role-based transition rights

### Workflow Rules
- Hold states are resolvable
- Rejections return to procurement
- Skip states allowed with permissions
- Auto-progress configurable

## Performance Considerations

### Optimization Implemented
- Pagination for large order lists
- Debounced search inputs
- Lazy loading of heavy components
- Memoized calculations
- Virtual scrolling for CT lists

### Scaling Metrics
- Tested with 1,000+ orders
- 10,000+ CT numbers handled
- 20-30 concurrent users supported
- <100ms real-time update latency

## Configuration

### Environment Variables
```bash
VITE_UID_PREFIX=A              # UID generation prefix
VITE_LOCATION=SB               # Location identifier
VITE_CT_LENGTH=14              # CT number length
VITE_AUTO_PROGRESS=true        # Enable auto-progress
```

### Feature Flags
- `enableCSVImport`: false (not implemented)
- `enableAutoProgress`: true (has bug)
- `enableBulkOperations`: false
- `enablePrintHistory`: false

## Testing Scenarios

### Order Creation Tests
1. Create order with all fields
2. Verify UID generation
3. Check quantity initialization
4. Test real-time updates

### CT Assignment Tests
1. Assign new CTs
2. Try duplicate CT (should warn)
3. Test approval workflow
4. Verify quantity transitions

### Workflow Dashboard Tests
1. Task assignment flow
2. Status progression
3. Hold/reject scenarios
4. Batch operations

### Integration Tests
1. WhatsApp notification triggers
2. Print job creation
3. Real-time sync across clients
4. Audit trail generation

## Known Issues & Gaps

### Critical Issues
1. **CT Auto-Progress Bug**
   - Status: High priority
   - Fix time: 1-2 hours
   - Impact: Manual workaround required

2. **Multi-PO Warning Missing**
   - Status: Compliance requirement
   - Fix time: 2-3 hours
   - Impact: HP/Lenovo requirement

### Medium Priority
1. **CSV Import Not Implemented**
   - Requirements documented
   - UI components missing
   - Backend logic needed

2. **Click-to-Copy Missing**
   - Individual field copying
   - Accounting integration need
   - Fix time: 3-4 hours

### Low Priority
1. Print history tracking
2. Bulk CT assignment
3. Advanced filtering options
4. Export capabilities

## Future Enhancements

### Phase 4 Roadmap
1. Complete CSV import with validation
2. Implement print history tracking
3. Add bulk operations interface
4. Create analytics dashboard

### Phase 5 Considerations
1. Mobile app for warehouse staff
2. Barcode scanning improvements
3. Advanced reporting suite
4. API for external integrations

---
**Module Version**: 2.0
**Last Updated**: June 12, 2025
**Next Review**: After CT bug fix implementation