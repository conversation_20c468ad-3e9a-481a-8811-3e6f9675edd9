# Order UI Components - Complete Implementation Guide

## Overview
The Order UI components provide a comprehensive interface for displaying and interacting with orders. The system supports dual layouts (cards and rows), real-time updates, and role-specific actions.

## Component Architecture

### Layout System
```
Orders Page
├── Layout Toggle (Card/Row view)
├── Filters & Search Bar
├── Order Display Area
│   ├── OrderCard (Card View)
│   └── OrderRowCard (Row View)
├── Modals
│   ├── CTNumberModal
│   ├── UpdateStatusModal
│   ├── ViewHistoryModal
│   └── ProcurementQueryModal
└── Global Components
    ├── GlobalPendingDisplay
    └── QuantityStateBadges
```

## Main Components

### 1. OrderCard Component
```typescript
// Location: src/components/orders/OrderCard.tsx

interface OrderCardProps {
  order: OrderLineWithDetails
  onEdit: (order: OrderLineWithDetails) => void
  onDelete: (orderId: string) => void
  onCTAssign: (order: OrderLineWithDetails) => void
  onPrint: (order: OrderLineWithDetails) => void
  onUpdateStatus: (order: OrderLineWithDetails) => void
  onViewHistory: (order: OrderLineWithDetails) => void
}

const OrderCard: React.FC<OrderCardProps> = ({ order, ...actions }) => {
  const quantities = order.order_line_quantities?.[0] || {}
  const ctCount = order.ct_numbers?.length || 0
  const totalQuantity = order.quantity
  
  return (
    <Card className="hover:shadow-lg transition-shadow">
      {/* Header Section */}
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Badge variant="outline">{order.uid}</Badge>
              <span className="text-base">{order.customer_name}</span>
            </CardTitle>
            <CardDescription>
              {order.part_number} - {order.part_description}
            </CardDescription>
          </div>
          
          {/* Priority/Status Indicators */}
          <div className="flex gap-2">
            {order.priority === 'urgent' && (
              <Badge variant="destructive">Urgent</Badge>
            )}
            {isPastETA(order.eta_updated) && (
              <Badge variant="warning">Overdue</Badge>
            )}
          </div>
        </div>
      </CardHeader>
      
      {/* Quantity Progress Section */}
      <CardContent className="space-y-4">
        {/* Visual Progress Bar */}
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>Progress</span>
            <span>{getCompletionPercentage(quantities, totalQuantity)}%</span>
          </div>
          <QuantityProgressBar 
            quantities={quantities} 
            totalQuantity={totalQuantity} 
          />
        </div>
        
        {/* State Badges */}
        <QuantityStateBadges quantities={quantities} />
        
        {/* Order Details */}
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <span className="text-muted-foreground">PO:</span>
            <span className="ml-1 font-medium">{order.po_number}</span>
          </div>
          <div>
            <span className="text-muted-foreground">Qty:</span>
            <span className="ml-1 font-medium">{totalQuantity}</span>
          </div>
          <div>
            <span className="text-muted-foreground">ETA:</span>
            <span className="ml-1 font-medium">
              {formatDate(order.eta_updated || order.eta_original)}
            </span>
          </div>
          <div>
            <span className="text-muted-foreground">CT:</span>
            <span className="ml-1 font-medium">{ctCount}/{totalQuantity}</span>
          </div>
        </div>
      </CardContent>
      
      {/* Action Buttons */}
      <CardFooter className="pt-3">
        <div className="grid grid-cols-4 gap-2 w-full">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => actions.onCTAssign(order)}
                className="relative"
              >
                <Hash className="h-4 w-4" />
                {ctCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {ctCount}
                  </span>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>Manage CT Numbers</TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => actions.onPrint(order)}
                disabled={ctCount === 0}
              >
                <Printer className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Print Labels</TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => actions.onUpdateStatus(order)}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Update Status</TooltipContent>
          </Tooltip>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="outline">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => actions.onEdit(order)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Order
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.onViewHistory(order)}>
                <Clock className="h-4 w-4 mr-2" />
                View History
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => actions.onDelete(order.id)}
                className="text-destructive"
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete Order
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  )
}
```

### 2. OrderRowCard Component
```typescript
// Location: src/components/orders/OrderRowCard.tsx

interface OrderRowCardProps {
  order: OrderLineWithDetails
  columns: ColumnConfig[]
  actions: OrderActions
}

const OrderRowCard: React.FC<OrderRowCardProps> = ({ order, columns, actions }) => {
  return (
    <div className="flex items-center p-4 border rounded-lg hover:bg-accent/50 transition-colors">
      {/* Checkbox for bulk selection */}
      <Checkbox 
        className="mr-4"
        checked={order.selected}
        onCheckedChange={(checked) => actions.onSelect(order.id, checked)}
      />
      
      {/* Dynamic columns based on configuration */}
      {columns.map(column => (
        <div
          key={column.key}
          className={cn(
            'flex-shrink-0 px-2',
            column.className
          )}
          style={{ width: column.width }}
        >
          {renderColumnContent(order, column)}
        </div>
      ))}
      
      {/* Compact action buttons */}
      <div className="ml-auto flex gap-1">
        <Button
          size="icon"
          variant="ghost"
          onClick={() => actions.onCTAssign(order)}
        >
          <Hash className="h-4 w-4" />
        </Button>
        
        <Button
          size="icon"
          variant="ghost"
          onClick={() => actions.onPrint(order)}
          disabled={!order.ct_numbers?.length}
        >
          <Printer className="h-4 w-4" />
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* Same menu items as card view */}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}

// Column content renderer
const renderColumnContent = (order: OrderLineWithDetails, column: ColumnConfig) => {
  switch (column.key) {
    case 'uid':
      return <Badge variant="outline">{order.uid}</Badge>
      
    case 'customer':
      return <span className="font-medium">{order.customer_name}</span>
      
    case 'part':
      return (
        <div>
          <div className="font-mono text-sm">{order.part_number}</div>
          <div className="text-xs text-muted-foreground truncate">
            {order.part_description}
          </div>
        </div>
      )
      
    case 'quantity':
      return (
        <div className="text-center">
          <div className="font-medium">{order.quantity}</div>
          <div className="text-xs text-muted-foreground">
            CT: {order.ct_numbers?.length || 0}
          </div>
        </div>
      )
      
    case 'status':
      return <QuantityStateMini quantities={order.order_line_quantities?.[0]} />
      
    case 'eta':
      return (
        <div className={cn(
          'text-sm',
          isPastETA(order.eta_updated) && 'text-destructive'
        )}>
          {formatDate(order.eta_updated || order.eta_original)}
        </div>
      )
      
    default:
      return order[column.key]
  }
}
```

### 3. UpdateStatusModal Component
```typescript
// Location: src/components/orders/UpdateStatusModal.tsx

const UpdateStatusModal = ({ order, isOpen, onClose, onUpdate }) => {
  const [selectedStage, setSelectedStage] = useState(order.status)
  const [notes, setNotes] = useState('')
  const [loading, setLoading] = useState(false)
  
  const stages = [
    { id: 'new', label: 'New Order', icon: Plus },
    { id: 'processing', label: 'Processing', icon: Clock },
    { id: 'procurement', label: 'Procurement', icon: Package },
    { id: 'kitting', label: 'Kitting/Packing', icon: Box },
    { id: 'qc', label: 'Quality Control', icon: CheckCircle },
    { id: 'ready', label: 'Ready to Ship', icon: Truck },
    { id: 'shipped', label: 'Shipped', icon: Send }
  ]
  
  const handleUpdate = async () => {
    setLoading(true)
    
    try {
      // Update order status
      const { error } = await supabase
        .from('order_lines')
        .update({
          status: selectedStage,
          updated_at: new Date()
        })
        .eq('id', order.id)
        
      if (!error) {
        // Log status change
        await supabase
          .from('order_status_logs')
          .insert({
            order_line_id: order.id,
            old_status: order.status,
            new_status: selectedStage,
            notes,
            created_by: auth.uid()
          })
          
        toast.success('Status updated successfully')
        onUpdate(selectedStage)
        onClose()
      }
    } catch (error) {
      toast.error('Failed to update status')
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Update Order Status</DialogTitle>
          <DialogDescription>
            Order {order.uid} - {order.customer_name}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Visual stage progression */}
          <div className="relative">
            <div className="absolute top-5 left-8 right-8 h-0.5 bg-muted" />
            <div className="flex justify-between relative">
              {stages.map((stage, index) => {
                const Icon = stage.icon
                const isActive = stage.id === selectedStage
                const isPast = stages.findIndex(s => s.id === order.status) >= index
                
                return (
                  <button
                    key={stage.id}
                    onClick={() => setSelectedStage(stage.id)}
                    className="relative flex flex-col items-center group"
                  >
                    <div className={cn(
                      'w-10 h-10 rounded-full flex items-center justify-center transition-colors',
                      isActive && 'bg-primary text-primary-foreground',
                      !isActive && isPast && 'bg-primary/20 text-primary',
                      !isActive && !isPast && 'bg-muted text-muted-foreground'
                    )}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <span className={cn(
                      'text-xs mt-2 whitespace-nowrap',
                      isActive && 'font-medium'
                    )}>
                      {stage.label}
                    </span>
                  </button>
                )
              })}
            </div>
          </div>
          
          {/* Impact warning */}
          {getStatusImpact(order.status, selectedStage) && (
            <Alert variant="warning">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Impact</AlertTitle>
              <AlertDescription>
                {getStatusImpact(order.status, selectedStage)}
              </AlertDescription>
            </Alert>
          )}
          
          {/* Notes field */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any relevant notes about this status change..."
              rows={3}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleUpdate} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### 4. ViewHistoryModal Component
```typescript
// Location: src/components/orders/ViewHistoryModal.tsx

const ViewHistoryModal = ({ order, isOpen, onClose }) => {
  const [filter, setFilter] = useState<'all' | 'status' | 'quantity' | 'ct'>('all')
  const { history, loading } = useOrderHistory(order.id, filter)
  
  const activityIcons = {
    created: Plus,
    status_change: RefreshCw,
    quantity_transition: TrendingUp,
    ct_assigned: Hash,
    ct_printed: Printer,
    hold_created: PauseCircle,
    note_added: MessageSquare
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Order History</DialogTitle>
          <DialogDescription>
            Complete activity log for order {order.uid}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Filter tabs */}
          <Tabs value={filter} onValueChange={setFilter}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Activity</TabsTrigger>
              <TabsTrigger value="status">Status Changes</TabsTrigger>
              <TabsTrigger value="quantity">Quantities</TabsTrigger>
              <TabsTrigger value="ct">CT Numbers</TabsTrigger>
            </TabsList>
          </Tabs>
          
          {/* Timeline */}
          <ScrollArea className="h-[400px] pr-4">
            {loading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border" />
                
                {/* History items */}
                <div className="space-y-4">
                  {history.map((item, index) => {
                    const Icon = activityIcons[item.type] || Clock
                    
                    return (
                      <div key={item.id} className="relative flex gap-4">
                        {/* Timeline dot */}
                        <div className={cn(
                          'w-8 h-8 rounded-full flex items-center justify-center',
                          'bg-background border-2',
                          index === 0 ? 'border-primary' : 'border-border'
                        )}>
                          <Icon className="h-4 w-4" />
                        </div>
                        
                        {/* Content */}
                        <div className="flex-1 pb-4">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{item.title}</span>
                            <span className="text-xs text-muted-foreground">
                              {formatRelativeTime(item.created_at)}
                            </span>
                          </div>
                          
                          <p className="text-sm text-muted-foreground">
                            {item.description}
                          </p>
                          
                          {item.metadata && (
                            <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                              {JSON.stringify(item.metadata, null, 2)}
                            </div>
                          )}
                          
                          <div className="mt-1 text-xs text-muted-foreground">
                            by {item.user_name}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </ScrollArea>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={() => exportHistory(history)}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
```

### 5. Filter & Search Components
```typescript
// Location: src/components/orders/OrderFilters.tsx

const OrderFilters = ({ filters, onFilterChange }) => {
  const { customers } = useCustomers()
  const { statuses } = useOrderStatuses()
  
  return (
    <div className="flex flex-wrap gap-4 mb-6">
      {/* Search input */}
      <div className="flex-1 min-w-[300px]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by UID, part number, or customer..."
            value={filters.search}
            onChange={(e) => onFilterChange({ search: e.target.value })}
            className="pl-9"
          />
        </div>
      </div>
      
      {/* Status filter */}
      <Select
        value={filters.status}
        onValueChange={(value) => onFilterChange({ status: value })}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="All Statuses" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Statuses</SelectItem>
          {statuses.map(status => (
            <SelectItem key={status.id} value={status.id}>
              {status.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Customer filter */}
      <Select
        value={filters.customer}
        onValueChange={(value) => onFilterChange({ customer: value })}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="All Customers" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Customers</SelectItem>
          {customers.map(customer => (
            <SelectItem key={customer.id} value={customer.name}>
              {customer.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* Date range picker */}
      <DateRangePicker
        value={filters.dateRange}
        onChange={(range) => onFilterChange({ dateRange: range })}
      />
      
      {/* Clear filters */}
      {hasActiveFilters(filters) && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onFilterChange(defaultFilters)}
        >
          <X className="mr-2 h-4 w-4" />
          Clear Filters
        </Button>
      )}
    </div>
  )
}
```

## Responsive Design

### Mobile Optimization
```typescript
// Responsive card layout
const OrderCardMobile = ({ order }) => {
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        {/* Compact header */}
        <div className="flex justify-between items-start mb-3">
          <div>
            <div className="font-medium">{order.uid}</div>
            <div className="text-sm text-muted-foreground">
              {order.customer_name}
            </div>
          </div>
          <Badge variant={getStatusVariant(order.status)}>
            {order.status}
          </Badge>
        </div>
        
        {/* Key details */}
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Part:</span>
            <span className="font-mono">{order.part_number}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Qty:</span>
            <span>{order.quantity}</span>
          </div>
        </div>
        
        {/* Compact actions */}
        <div className="flex gap-2 mt-4">
          <Button size="sm" className="flex-1">
            <Hash className="h-4 w-4 mr-1" />
            CT
          </Button>
          <Button size="sm" className="flex-1">
            <Printer className="h-4 w-4 mr-1" />
            Print
          </Button>
          <Button size="sm" variant="outline">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Responsive layout wrapper
const OrdersLayout = ({ children }) => {
  const isMobile = useMediaQuery('(max-width: 768px)')
  
  return (
    <div className={cn(
      'container mx-auto',
      isMobile ? 'px-4' : 'px-6'
    )}>
      {children}
    </div>
  )
}
```

## Performance Optimizations

### Virtual Scrolling
```typescript
// For large order lists
const VirtualOrderList = ({ orders, viewMode }) => {
  const rowVirtualizer = useVirtualizer({
    count: orders.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => viewMode === 'card' ? 250 : 80,
    overscan: 5
  })
  
  return (
    <div ref={parentRef} className="h-[600px] overflow-auto">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map(virtualItem => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            {viewMode === 'card' ? (
              <OrderCard order={orders[virtualItem.index]} />
            ) : (
              <OrderRowCard order={orders[virtualItem.index]} />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
```

### Memoization
```typescript
// Memoized order components
const MemoizedOrderCard = React.memo(OrderCard, (prev, next) => {
  return (
    prev.order.id === next.order.id &&
    prev.order.updated_at === next.order.updated_at &&
    prev.order.order_line_quantities?.[0]?.updated_at === 
      next.order.order_line_quantities?.[0]?.updated_at
  )
})

// Memoized calculations
const orderMetrics = useMemo(() => {
  return calculateOrderMetrics(orders)
}, [orders])
```

## Accessibility

### ARIA Labels & Keyboard Navigation
```typescript
const AccessibleOrderCard = ({ order }) => {
  return (
    <Card
      role="article"
      aria-label={`Order ${order.uid} for ${order.customer_name}`}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          handleCardClick(order)
        }
      }}
    >
      {/* Content with proper ARIA labels */}
    </Card>
  )
}
```

---
**Last Updated**: June 12, 2025
**Component Library**: Shadcn/ui + Custom Components
**Next Enhancement**: Advanced bulk operations UI