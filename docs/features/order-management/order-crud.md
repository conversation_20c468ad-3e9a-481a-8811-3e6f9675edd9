# Order CRUD Operations - Complete Implementation Guide

## Overview
The order CRUD (Create, Read, Update, Delete) system provides comprehensive order management with real-time synchronization, role-based permissions, and complete audit trails.

## Implementation Status: 95% Complete

### ✅ Implemented
- Create orders with UID generation
- Read with pagination and filtering
- Update with real-time sync
- Soft delete with recovery
- Advanced search and filters
- Dual layout system

### ⚠️ Missing
- CSV bulk import
- Batch update operations
- Export functionality

## Technical Architecture

### Frontend Components

#### Orders.tsx - Main Interface
```typescript
// Location: src/pages/Orders.tsx (1,821 lines)

const Orders = () => {
  // State management
  const [view, setView] = useState<'card' | 'row'>('card')
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: 'all',
    customer: 'all',
    dateRange: null
  })
  
  // Data fetching with real-time subscriptions
  const { orders, loading, error, refetch } = useOrders(filters)
  
  // CRUD operations
  const { createOrder } = useCreateOrder()
  const { updateOrder } = useUpdateOrder()
  const { deleteOrder } = useDeleteOrder()
}
```

#### Order Display Components
```typescript
// OrderCard.tsx - Card layout
<OrderCard 
  order={order}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onCTAssign={handleCTAssign}
  onPrint={handlePrint}
/>

// OrderRowCard.tsx - Compact row layout
<OrderRowCard
  order={order}
  columns={visibleColumns}
  actions={rowActions}
/>
```

### Backend Integration

#### useOrders Hook
```typescript
// Location: src/hooks/useOrders.ts

export const useOrders = (filters: FilterState) => {
  const [orders, setOrders] = useState<OrderLineWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    // Initial fetch
    fetchOrders()
    
    // Set up real-time subscription
    const subscription = supabase
      .channel('orders_channel')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'order_lines' },
        handleRealtimeUpdate
      )
      .subscribe()
      
    return () => {
      subscription.unsubscribe()
    }
  }, [filters])
  
  const fetchOrders = async () => {
    let query = supabase
      .from('order_lines')
      .select(`
        *,
        customer:customers(*),
        product:products(*),
        order_line_quantities(*),
        ct_numbers(*)
      `)
      
    // Apply filters
    if (filters.search) {
      query = query.or(`
        uid.ilike.%${filters.search}%,
        part_number.ilike.%${filters.search}%,
        customer_name.ilike.%${filters.search}%
      `)
    }
    
    const { data, error } = await query
    if (!error) setOrders(data)
  }
}
```

### Database Schema

#### Order Lines Table
```sql
CREATE TABLE order_lines (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  uid VARCHAR(10) UNIQUE NOT NULL,
  customer_id UUID REFERENCES customers(id),
  customer_name VARCHAR(255) NOT NULL,
  product_id UUID REFERENCES products(id),
  part_number VARCHAR(100) NOT NULL,
  part_description TEXT,
  po_number VARCHAR(100),
  po_line_number VARCHAR(50),
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  price DECIMAL(10,2),
  status VARCHAR(50) DEFAULT 'new',
  eta_original DATE,
  eta_updated DATE,
  notes TEXT,
  priority VARCHAR(20) DEFAULT 'normal',
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_by UUID REFERENCES users(id),
  updated_at TIMESTAMPTZ DEFAULT now(),
  deleted_at TIMESTAMPTZ, -- Soft delete
  
  -- Indexes for performance
  INDEX idx_uid ON order_lines(uid),
  INDEX idx_customer ON order_lines(customer_name),
  INDEX idx_part ON order_lines(part_number),
  INDEX idx_status ON order_lines(status),
  INDEX idx_created ON order_lines(created_at DESC)
);

-- UID generation function
CREATE FUNCTION generate_next_uid() RETURNS VARCHAR AS $$
DECLARE
  prefix VARCHAR;
  next_num INTEGER;
  new_uid VARCHAR;
BEGIN
  -- Get prefix from settings (default 'A')
  SELECT value INTO prefix FROM system_settings 
  WHERE key = 'uid_prefix' LIMIT 1;
  IF prefix IS NULL THEN prefix := 'A'; END IF;
  
  -- Get next number
  SELECT COALESCE(MAX(CAST(SUBSTRING(uid FROM 2) AS INTEGER)), 0) + 1
  INTO next_num
  FROM order_lines
  WHERE uid LIKE prefix || '%';
  
  -- Format UID (A001, A002, etc.)
  new_uid := prefix || LPAD(next_num::TEXT, 3, '0');
  
  RETURN new_uid;
END;
$$ LANGUAGE plpgsql;

-- Trigger for auto UID generation
CREATE TRIGGER set_order_uid
  BEFORE INSERT ON order_lines
  FOR EACH ROW
  WHEN (NEW.uid IS NULL)
  EXECUTE FUNCTION generate_next_uid();
```

## CRUD Operations

### Create Operation

#### UI Flow
1. User clicks "Create Order" button
2. Modal form appears with fields
3. Real-time validation as user types
4. Save triggers backend creation
5. UI updates immediately with new order

#### Implementation
```typescript
// CreateOrderModal.tsx
const CreateOrderModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState<OrderFormData>({
    customer_name: '',
    part_number: '',
    part_description: '',
    po_number: '',
    quantity: 1,
    eta_original: null
  })
  
  const { createOrder, loading } = useCreateOrder()
  
  const handleSubmit = async () => {
    // Validation
    if (!validateForm(formData)) return
    
    // Create order
    const { data, error } = await createOrder(formData)
    
    if (!error) {
      toast.success(`Order ${data.uid} created successfully`)
      onClose()
    }
  }
}

// useCreateOrder hook
export const useCreateOrder = () => {
  const createOrder = async (orderData: OrderFormData) => {
    // Initialize quantities
    const quantities = {
      total_order_quantity: orderData.quantity,
      pending_procurement: orderData.quantity,
      // ... other states initialized to 0
    }
    
    // Create order with transaction
    const { data, error } = await supabase.rpc('create_order_with_quantities', {
      order_data: orderData,
      quantity_data: quantities
    })
    
    return { data, error }
  }
  
  return { createOrder }
}
```

### Read Operations

#### List View Features
- **Pagination**: 50 orders per page
- **Sorting**: By date, UID, customer, status
- **Filtering**: Multiple filter combinations
- **Search**: Across UID, part number, customer
- **Real-time**: Auto-updates on changes

#### Implementation
```typescript
// Pagination implementation
const usePaginatedOrders = (page: number, pageSize: number = 50) => {
  const [orders, setOrders] = useState([])
  const [totalCount, setTotalCount] = useState(0)
  
  const fetchPage = async () => {
    const from = page * pageSize
    const to = from + pageSize - 1
    
    const { data, error, count } = await supabase
      .from('order_lines')
      .select('*', { count: 'exact' })
      .range(from, to)
      .order('created_at', { ascending: false })
      
    if (!error) {
      setOrders(data)
      setTotalCount(count)
    }
  }
}

// Advanced filtering
const applyFilters = (query: SupabaseQuery, filters: FilterState) => {
  // Status filter
  if (filters.status !== 'all') {
    query = query.eq('status', filters.status)
  }
  
  // Date range filter
  if (filters.dateRange) {
    query = query
      .gte('created_at', filters.dateRange.start)
      .lte('created_at', filters.dateRange.end)
  }
  
  // Customer filter
  if (filters.customer !== 'all') {
    query = query.eq('customer_name', filters.customer)
  }
  
  return query
}
```

### Update Operations

#### Inline Editing
- Quick edit for single fields
- Full edit modal for comprehensive updates
- Real-time validation
- Optimistic updates with rollback

#### Implementation
```typescript
// Quick edit implementation
const handleQuickEdit = async (orderId: string, field: string, value: any) => {
  // Optimistic update
  setOrders(prev => prev.map(order => 
    order.id === orderId ? { ...order, [field]: value } : order
  ))
  
  // Backend update
  const { error } = await supabase
    .from('order_lines')
    .update({ [field]: value, updated_at: new Date() })
    .eq('id', orderId)
    
  if (error) {
    // Rollback on error
    refetch()
    toast.error('Update failed')
  }
}

// Full edit modal
const EditOrderModal = ({ order, onSave }) => {
  const [formData, setFormData] = useState(order)
  
  const handleSave = async () => {
    const updates = {
      ...formData,
      updated_at: new Date(),
      updated_by: currentUser.id
    }
    
    const { error } = await supabase
      .from('order_lines')
      .update(updates)
      .eq('id', order.id)
      
    if (!error) {
      onSave(updates)
      toast.success('Order updated')
    }
  }
}
```

### Delete Operations

#### Soft Delete Implementation
```typescript
// Soft delete with recovery option
const handleDelete = async (orderId: string) => {
  const confirmed = await confirm('Delete this order?')
  if (!confirmed) return
  
  const { error } = await supabase
    .from('order_lines')
    .update({ 
      deleted_at: new Date(),
      status: 'deleted'
    })
    .eq('id', orderId)
    
  if (!error) {
    toast.success('Order deleted', {
      action: {
        label: 'Undo',
        onClick: () => recoverOrder(orderId)
      }
    })
  }
}

// Recovery function
const recoverOrder = async (orderId: string) => {
  const { error } = await supabase
    .from('order_lines')
    .update({ 
      deleted_at: null,
      status: 'active'
    })
    .eq('id', orderId)
}
```

## Advanced Features

### Bulk Operations (⚠️ Planned)
```typescript
// Future implementation
const BulkOperations = ({ selectedOrders }) => {
  const handleBulkUpdate = async (updates: Partial<Order>) => {
    const ids = selectedOrders.map(o => o.id)
    
    const { error } = await supabase
      .from('order_lines')
      .update(updates)
      .in('id', ids)
  }
  
  const handleBulkDelete = async () => {
    // Similar bulk delete logic
  }
}
```

### Export Functionality (⚠️ Planned)
```typescript
// Future implementation
const exportToExcel = async (orders: Order[]) => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Orders')
  
  // Add headers
  worksheet.columns = [
    { header: 'UID', key: 'uid' },
    { header: 'Customer', key: 'customer_name' },
    { header: 'Part Number', key: 'part_number' },
    // ... other columns
  ]
  
  // Add data
  orders.forEach(order => {
    worksheet.addRow(order)
  })
  
  // Download
  const buffer = await workbook.xlsx.writeBuffer()
  downloadFile(buffer, 'orders.xlsx')
}
```

## Performance Optimizations

### Query Optimization
```sql
-- Composite indexes for common queries
CREATE INDEX idx_order_search ON order_lines(uid, part_number, customer_name);
CREATE INDEX idx_order_filter ON order_lines(status, created_at DESC);

-- Materialized view for dashboard stats
CREATE MATERIALIZED VIEW order_stats AS
SELECT 
  COUNT(*) as total_orders,
  COUNT(DISTINCT customer_name) as unique_customers,
  SUM(quantity) as total_quantity,
  AVG(quantity) as avg_quantity
FROM order_lines
WHERE deleted_at IS NULL;
```

### Frontend Optimization
```typescript
// Memoized calculations
const orderStats = useMemo(() => {
  return orders.reduce((acc, order) => {
    acc.total += order.quantity
    acc.byStatus[order.status] = (acc.byStatus[order.status] || 0) + 1
    return acc
  }, { total: 0, byStatus: {} })
}, [orders])

// Virtualized list for large datasets
<VirtualList
  height={600}
  itemCount={orders.length}
  itemSize={80}
  renderItem={({ index, style }) => (
    <div style={style}>
      <OrderRowCard order={orders[index]} />
    </div>
  )}
/>
```

## Security & Permissions

### Row Level Security
```sql
-- Read permission for all authenticated users
CREATE POLICY order_read ON order_lines
  FOR SELECT
  TO authenticated
  USING (true);

-- Create permission for staff and above
CREATE POLICY order_create ON order_lines
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM user_roles 
      WHERE role_id IN ('staff', 'manager', 'admin')
    )
  );

-- Update permission based on role
CREATE POLICY order_update ON order_lines
  FOR UPDATE
  TO authenticated
  USING (
    auth.uid() = created_by OR
    auth.uid() IN (
      SELECT user_id FROM user_roles 
      WHERE role_id IN ('manager', 'admin')
    )
  );
```

## Audit Trail

### Implementation
```typescript
// Automatic audit logging
CREATE TRIGGER audit_order_changes
  AFTER INSERT OR UPDATE OR DELETE ON order_lines
  FOR EACH ROW
  EXECUTE FUNCTION log_order_change();

CREATE FUNCTION log_order_change() RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (
    table_name,
    record_id,
    action,
    old_data,
    new_data,
    user_id,
    created_at
  ) VALUES (
    'order_lines',
    COALESCE(NEW.id, OLD.id),
    TG_OP,
    row_to_json(OLD),
    row_to_json(NEW),
    auth.uid(),
    now()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---
**Last Updated**: June 12, 2025
**Next Review**: After CSV import implementation