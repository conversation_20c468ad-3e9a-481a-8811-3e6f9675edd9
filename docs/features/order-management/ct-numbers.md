# CT Numbers System - Complete Implementation Guide

## Overview
The CT (Container Tracking) Number system provides unique serial number tracking for order items with validation, duplicate detection, and integrated printing workflows. The system uses a unified modal architecture that reduced code complexity by 24.4%.

## Implementation Status: 95% Complete

### ✅ Implemented
- 14-character validation system
- Duplicate detection with warnings
- Unified modal for assignment/printing
- Checkbox selection for partial printing
- Smart template recommendations
- Real-time validation

### ⚠️ Issues
- Auto-progress bug for new orders in `pending_procurement` state
- Approval workflow for duplicates partially implemented

## Architecture

### Unified Modal System
```typescript
// Location: src/components/orders/CTNumberModal.tsx (1,439 lines)

interface CTNumberModalProps {
  isOpen: boolean
  onClose: () => void
  orderLine: OrderLineWithDetails
  onSave: (ctNumbers: string[]) => void
  mode?: 'assignment' | 'print'  // Context-aware operation
}

const CTNumberModal: React.FC<CTNumberModalProps> = ({
  mode = 'assignment',
  ...props
}) => {
  // Mode determines behavior
  const isAssignmentMode = mode === 'assignment'
  const isPrintMode = mode === 'print'
}
```

### Entry Points
```typescript
// Location: src/pages/Orders.tsx

// CT Assignment Button (# icon)
const handleManageCT = (order: OrderLineWithDetails) => {
  setSelectedOrder(order)
  setCTModalOpen(true)
  setCTModalMode('assignment')  // New CT assignment
}

// Print Button (🖨️ icon)
const handlePrintLabel = (order: OrderLineWithDetails) => {
  setSelectedOrder(order)
  setCTModalOpen(true)
  setCTModalMode('print')  // Print existing CTs
}
```

## Database Schema

### CT Numbers Table
```sql
CREATE TABLE ct_numbers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_line_id UUID REFERENCES order_lines(id) ON DELETE CASCADE,
  ct_number VARCHAR(14) NOT NULL,
  status VARCHAR(50) DEFAULT 'active',
  assigned_quantity INTEGER DEFAULT 1,
  printed_count INTEGER DEFAULT 0,
  last_printed_at TIMESTAMPTZ,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  
  -- Constraints
  CONSTRAINT ct_format CHECK (ct_number ~ '^[A-Z0-9]{14}$'),
  CONSTRAINT ct_unique UNIQUE (ct_number),
  
  -- Indexes
  INDEX idx_ct_order ON ct_numbers(order_line_id),
  INDEX idx_ct_number ON ct_numbers(ct_number),
  INDEX idx_ct_status ON ct_numbers(status)
);

-- Duplicate tracking table
CREATE TABLE ct_duplicates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ct_number VARCHAR(14) NOT NULL,
  original_order_id UUID,
  duplicate_order_id UUID,
  approval_status VARCHAR(50) DEFAULT 'pending',
  approved_by UUID REFERENCES users(id),
  justification TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

## Core Features

### 1. CT Validation System

#### Format Rules
- **Length**: Exactly 14 characters
- **Characters**: Alphanumeric only (A-Z, 0-9)
- **Case**: ALL UPPERCASE (auto-conversion)
- **Uniqueness**: Must be unique across system

#### Implementation
```typescript
// CT validation function
const validateCTNumber = (ct: string): ValidationResult => {
  // Auto-uppercase
  const normalized = ct.toUpperCase().trim()
  
  // Length check
  if (normalized.length !== 14) {
    return {
      valid: false,
      error: 'CT number must be exactly 14 characters'
    }
  }
  
  // Format check
  if (!/^[A-Z0-9]{14}$/.test(normalized)) {
    return {
      valid: false,
      error: 'CT number must contain only letters and numbers'
    }
  }
  
  return { valid: true, normalized }
}

// Real-time validation hook
const useCTValidation = (ctNumber: string) => {
  const [validation, setValidation] = useState<ValidationResult>()
  const [checking, setChecking] = useState(false)
  
  useEffect(() => {
    const debounced = debounce(async () => {
      // Format validation
      const result = validateCTNumber(ctNumber)
      if (!result.valid) {
        setValidation(result)
        return
      }
      
      // Duplicate check
      setChecking(true)
      const { data } = await supabase
        .from('ct_numbers')
        .select('id, order_line_id')
        .eq('ct_number', result.normalized)
        .single()
        
      if (data) {
        result.isDuplicate = true
        result.existingOrderId = data.order_line_id
      }
      
      setValidation(result)
      setChecking(false)
    }, 300)
    
    debounced()
  }, [ctNumber])
  
  return { validation, checking }
}
```

### 2. Duplicate Detection & Management

#### Detection System
```typescript
// Comprehensive duplicate check
const checkForDuplicates = async (ctNumbers: string[]) => {
  const { data: existingCTs } = await supabase
    .from('ct_numbers')
    .select(`
      ct_number,
      order_line_id,
      order_lines!inner(
        uid,
        customer_name,
        part_number
      )
    `)
    .in('ct_number', ctNumbers)
    
  const duplicates = existingCTs?.map(ct => ({
    ctNumber: ct.ct_number,
    existingOrder: {
      uid: ct.order_lines.uid,
      customer: ct.order_lines.customer_name,
      partNumber: ct.order_lines.part_number
    }
  }))
  
  return duplicates || []
}
```

#### Warning Display
```typescript
// Duplicate warning component
const DuplicateWarning = ({ duplicates, onProceed, onCancel }) => {
  return (
    <Alert variant="destructive">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Duplicate CT Numbers Detected!</AlertTitle>
      <AlertDescription>
        <div className="space-y-2 mt-2">
          {duplicates.map(dup => (
            <div key={dup.ctNumber} className="text-sm">
              <strong>{dup.ctNumber}</strong> already assigned to:
              <br />
              Order: {dup.existingOrder.uid} | 
              Customer: {dup.existingOrder.customer} |
              Part: {dup.existingOrder.partNumber}
            </div>
          ))}
        </div>
        
        <div className="mt-4 space-x-2">
          <Button variant="destructive" onClick={onCancel}>
            Cancel
          </Button>
          {hasOverridePermission && (
            <Button onClick={onProceed}>
              Proceed with Justification
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  )
}
```

### 3. Assignment Workflow

#### Tab Interface
```
┌─────────────────────────────────────┐
│  Order: A001 | HP Laptop | Qty: 10  │
├─────────────────────────────────────┤
│ ┌─────┐                             │
│ │ Add │ ──> Scanner/Manual Entry   │
│ ├─────┤                             │
│ │List │ ──> View Assigned CTs       │
│ ├─────┤                             │
│ │Print│ ──> Print Selected CTs      │
│ └─────┘                             │
└─────────────────────────────────────┘
```

#### Add Tab Implementation
```typescript
// Scanner integration
const { scanResult, scanning } = useBarcodeScanner({
  onScan: (value) => {
    const validation = validateCTNumber(value)
    if (validation.valid) {
      addCTNumber(validation.normalized)
    }
  }
})

// Manual entry with Enter key support
<Input
  placeholder="Enter CT number"
  value={manualInput}
  onChange={(e) => setManualInput(e.target.value)}
  onKeyPress={(e) => {
    if (e.key === 'Enter') {
      handleAddCT(manualInput)
    }
  }}
/>

// Batch paste support
const handlePaste = (e: ClipboardEvent) => {
  const pastedText = e.clipboardData.getData('text')
  const lines = pastedText.split(/[\n,\s]+/)
  const validCTs = lines
    .map(line => validateCTNumber(line))
    .filter(result => result.valid)
    .map(result => result.normalized)
    
  addMultipleCTs(validCTs)
}
```

#### Status Indicators
```typescript
// Visual CT status badges
const CTStatusBadge = ({ status }) => {
  const configs = {
    new: { icon: '🆕', color: 'green', label: 'New' },
    existing: { icon: '📄', color: 'blue', label: 'Existing' },
    pending: { icon: '⏳', color: 'yellow', label: 'Pending' },
    duplicate: { icon: '⚠️', color: 'red', label: 'Duplicate' }
  }
  
  const config = configs[status]
  
  return (
    <Badge variant={config.color}>
      {config.icon} {config.label}
    </Badge>
  )
}
```

### 4. Printing Workflow

#### Print Mode Features
```typescript
// Checkbox selection interface
const PrintTab = ({ assignedCTs, onPrint }) => {
  const [selectedCTs, setSelectedCTs] = useState<Set<string>>(new Set())
  
  // Smart defaults - select only new CTs
  useEffect(() => {
    const newCTs = assignedCTs
      .filter(ct => ct.isNew)
      .map(ct => ct.number)
    setSelectedCTs(new Set(newCTs))
  }, [assignedCTs])
  
  return (
    <div>
      {/* Selection controls */}
      <div className="flex gap-2 mb-4">
        <Button onClick={() => selectAll()}>Select All</Button>
        <Button onClick={() => selectNone()}>Select None</Button>
        <span className="ml-auto">
          {selectedCTs.size} of {assignedCTs.length} selected
        </span>
      </div>
      
      {/* CT grid with checkboxes */}
      <div className="grid grid-cols-2 gap-2">
        {assignedCTs.map(ct => (
          <label key={ct.number} className="flex items-center gap-2 p-2 border rounded">
            <Checkbox
              checked={selectedCTs.has(ct.number)}
              onCheckedChange={(checked) => {
                const newSelected = new Set(selectedCTs)
                if (checked) {
                  newSelected.add(ct.number)
                } else {
                  newSelected.delete(ct.number)
                }
                setSelectedCTs(newSelected)
              }}
            />
            <span className="font-mono">{ct.number}</span>
            <CTStatusBadge status={ct.status} />
          </label>
        ))}
      </div>
    </div>
  )
}
```

#### Template Selection
```typescript
// Smart template recommendations
const getRecommendedTemplates = (context: PrintContext) => {
  const templates = useTemplates()
  
  // If printing CTs, recommend CT templates
  if (context.hasCTNumbers) {
    return templates.filter(t => 
      t.category === 'ct_labels' || 
      t.name.toLowerCase().includes('ct')
    )
  }
  
  // Otherwise recommend quick print templates
  return templates.filter(t => 
    t.category === 'shipping' || 
    t.category === 'warning'
  )
}

// Template display with recommendations
<Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
  <SelectTrigger>
    <SelectValue placeholder="Choose template" />
  </SelectTrigger>
  <SelectContent>
    {recommendedTemplates.length > 0 && (
      <>
        <SelectGroup>
          <SelectLabel>⭐ Recommended</SelectLabel>
          {recommendedTemplates.map(template => (
            <SelectItem key={template.id} value={template.id}>
              {template.name}
            </SelectItem>
          ))}
        </SelectGroup>
        <SelectSeparator />
      </>
    )}
    <SelectGroup>
      <SelectLabel>All Templates</SelectLabel>
      {allTemplates.map(template => (
        <SelectItem key={template.id} value={template.id}>
          {template.name}
        </SelectItem>
      ))}
    </SelectGroup>
  </SelectContent>
</Select>
```

### 5. Workflow Options

#### Post-Assignment Actions
```typescript
// Workflow option selection
const WorkflowOptions = ({ onSelect }) => {
  const [selected, setSelected] = useState('save_only')
  
  const options = [
    {
      id: 'save_only',
      title: 'Save Only',
      description: 'Save CT numbers and close',
      icon: <Save className="h-4 w-4" />
    },
    {
      id: 'auto_progress',
      title: 'Auto Progress',
      description: 'Save, print, and move to kitting',
      icon: <FastForward className="h-4 w-4" />
    },
    {
      id: 'manual_progress',
      title: 'Manual Progress',
      description: 'Save and print, then choose next step',
      icon: <Play className="h-4 w-4" />
    }
  ]
  
  return (
    <RadioGroup value={selected} onValueChange={setSelected}>
      {options.map(option => (
        <div key={option.id} className="flex items-start space-x-2 p-3 border rounded">
          <RadioGroupItem value={option.id} />
          <div className="flex-1">
            <div className="flex items-center gap-2">
              {option.icon}
              <span className="font-medium">{option.title}</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {option.description}
            </p>
          </div>
        </div>
      ))}
    </RadioGroup>
  )
}
```

## Integration with Quantity System

### Automatic Progression
```typescript
// Auto-progress implementation (with bug)
const performAutomaticTransition = async (orderLineId: string) => {
  // BUG: This fails for orders in pending_procurement state
  // Fix needed in lines 510-642 of CTNumberModal.tsx
  
  const { data: quantities } = await supabase
    .from('order_line_quantities')
    .select('*')
    .eq('order_line_id', orderLineId)
    .single()
    
  // Determine next state
  let fromState, toState, quantity
  
  if (quantities.pending_procurement > 0) {
    // BUG: This condition is not properly handled
    fromState = 'pending_procurement'
    toState = 'awaiting_kitting_packing'
    quantity = quantities.pending_procurement
  } else if (quantities.requested_from_stock > 0) {
    fromState = 'requested_from_stock'
    toState = 'awaiting_kitting_packing'
    quantity = quantities.requested_from_stock
  }
  
  // Perform transition
  if (fromState && toState && quantity > 0) {
    await supabase.rpc('transition_quantity', {
      p_order_line_id: orderLineId,
      p_from_state: fromState,
      p_to_state: toState,
      p_quantity: quantity,
      p_reason: 'CT assignment completed'
    })
  }
}
```

### Manual Progression
```typescript
// Manual progression interface
const ManualProgressDialog = ({ order, onComplete }) => {
  const [selectedTransition, setSelectedTransition] = useState(null)
  
  const availableTransitions = getAvailableTransitions(order.quantities)
  
  return (
    <Dialog>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Choose Next Step</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-2">
          {availableTransitions.map(transition => (
            <Button
              key={transition.id}
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                executeTransition(transition)
                onComplete()
              }}
            >
              <ArrowRight className="mr-2 h-4 w-4" />
              Move {transition.quantity} items to {transition.toState}
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}
```

## Performance Optimizations

### Batch Operations
```typescript
// Batch CT assignment
const assignMultipleCTs = async (orderLineId: string, ctNumbers: string[]) => {
  // Use transaction for atomicity
  const { data, error } = await supabase.rpc('batch_assign_cts', {
    p_order_line_id: orderLineId,
    p_ct_numbers: ctNumbers
  })
  
  return { data, error }
}

// Database function
CREATE FUNCTION batch_assign_cts(
  p_order_line_id UUID,
  p_ct_numbers TEXT[]
) RETURNS VOID AS $$
BEGIN
  -- Insert all CTs in one operation
  INSERT INTO ct_numbers (order_line_id, ct_number, created_by)
  SELECT 
    p_order_line_id,
    unnest(p_ct_numbers),
    auth.uid()
  ON CONFLICT (ct_number) DO NOTHING;
END;
$$ LANGUAGE plpgsql;
```

### Caching & Memoization
```typescript
// Cache CT lookups
const ctCache = new Map<string, CTValidationResult>()

const validateCTWithCache = async (ctNumber: string) => {
  if (ctCache.has(ctNumber)) {
    return ctCache.get(ctNumber)
  }
  
  const result = await validateCTNumber(ctNumber)
  ctCache.set(ctNumber, result)
  
  return result
}

// Memoize template recommendations
const recommendedTemplates = useMemo(() => {
  return getRecommendedTemplates({
    hasCTNumbers: assignedCTs.length > 0,
    orderType: order.product_type,
    customer: order.customer_name
  })
}, [assignedCTs.length, order.product_type, order.customer_name])
```

## Testing Scenarios

### Assignment Tests
1. Single CT assignment
2. Bulk paste of 100+ CTs
3. Duplicate detection
4. Scanner integration
5. Auto-progression (verify bug)

### Print Tests
1. Select all/none functionality
2. Partial selection printing
3. Template recommendations
4. MCP integration
5. Reprint scenarios

### Edge Cases
1. Network failure during assignment
2. Concurrent assignments to same order
3. CT format edge cases
4. Large batch performance
5. Permission-based features

## Known Issues

### 1. Auto-Progress Bug
**Location**: CTNumberModal.tsx lines 510-642
**Issue**: Orders in `pending_procurement` state fail to auto-progress
**Impact**: Users must manually progress quantities
**Fix**: Update state detection logic

### 2. Approval Workflow
**Status**: Partially implemented
**Missing**: Backend approval queue and notifications
**Impact**: Duplicates require manual coordination

---
**Last Updated**: June 12, 2025
**Priority Fix**: Auto-progress bug resolution