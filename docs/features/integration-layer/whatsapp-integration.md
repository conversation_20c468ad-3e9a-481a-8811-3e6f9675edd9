# WhatsApp Integration Architecture

## Overview

The WhatsApp integration provides enterprise-grade messaging capabilities with dual provider support, interactive approval workflows, and comprehensive message management. The system intelligently routes messages through Meta Cloud API or N8N Evolution based on priority and cost optimization.

## Architecture Components

### 1. WhatsApp Provider Manager

**File**: `/src/services/whatsapp/WhatsAppProviderManager.ts`

#### Dual Provider System

```typescript
export class WhatsAppProviderManager {
  private metaProvider: MetaCloudProvider;
  private n8nProvider: N8NEvolutionProvider;
  private activeProvider: WhatsAppProvider | null = null;
  
  async selectProvider(
    messageType: 'approval' | 'notification' | 'bulk',
    recipientCount: number = 1
  ): Promise<WhatsAppProvider> {
    // Priority-based selection
    if (messageType === 'approval') {
      // Critical approvals use Meta Cloud API
      return this.metaProvider.isConfigured() 
        ? this.metaProvider 
        : this.n8nProvider;
    }
    
    // Cost optimization for bulk messages
    if (recipientCount > 10 && this.n8nProvider.isConfigured()) {
      return this.n8nProvider;
    }
    
    // Default to Meta Cloud API
    return this.metaProvider.isConfigured()
      ? this.metaProvider
      : this.n8nProvider;
  }
}
```

#### Provider Configuration

```typescript
interface ProviderConfig {
  meta_cloud: {
    app_id: string;
    app_secret: string;
    access_token: string;
    phone_number_id: string;
    webhook_verify_token: string;
    webhook_url: string;
  };
  n8n_evolution: {
    webhook_url: string;
    api_key: string;
    instance_name: string;
    instance_url: string;
  };
}
```

### 2. Meta Cloud API Provider

**File**: `/src/services/whatsapp/providers/MetaCloudProvider.ts`

#### Interactive Message Implementation

```typescript
export class MetaCloudProvider implements WhatsAppProvider {
  async sendInteractiveMessage(
    recipient: string,
    message: InteractiveMessage
  ): Promise<MessageResult> {
    const payload = {
      messaging_product: "whatsapp",
      recipient_type: "individual",
      to: recipient,
      type: "interactive",
      interactive: {
        type: "button",
        header: {
          type: "text",
          text: message.header
        },
        body: {
          text: message.body
        },
        action: {
          buttons: message.buttons.map((btn, idx) => ({
            type: "reply",
            reply: {
              id: btn.id || `btn_${idx}`,
              title: btn.text.substring(0, 20) // Meta limit
            }
          }))
        }
      }
    };
    
    const response = await fetch(
      `https://graph.facebook.com/v17.0/${this.config.phone_number_id}/messages`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      }
    );
    
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error?.message || 'Meta API error');
    }
    
    return {
      success: true,
      messageId: result.messages[0].id,
      provider: 'meta_cloud',
      timestamp: new Date()
    };
  }
}
```

#### Webhook Handler

```typescript
// Handle button responses
export const handleMetaWebhook = async (req: Request) => {
  const { entry } = req.body;
  
  for (const item of entry) {
    const changes = item.changes || [];
    
    for (const change of changes) {
      if (change.field === 'messages') {
        const message = change.value.messages?.[0];
        
        if (message?.type === 'interactive') {
          const buttonId = message.interactive.button_reply.id;
          const phoneNumber = message.from;
          
          // Process approval response
          await processApprovalResponse({
            buttonId,
            phoneNumber,
            timestamp: new Date(message.timestamp * 1000)
          });
        }
      }
    }
  }
  
  return { status: 'ok' };
};
```

### 3. N8N Evolution Provider

**File**: `/src/services/whatsapp/providers/N8NEvolutionProvider.ts`

#### Bulk Messaging Implementation

```typescript
export class N8NEvolutionProvider implements WhatsAppProvider {
  async sendBulkMessages(
    recipients: string[],
    message: TextMessage
  ): Promise<BulkMessageResult> {
    const results = [];
    const batchSize = 50; // Evolution API limit
    
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);
      
      const payload = {
        instance: this.config.instance_name,
        messages: batch.map(recipient => ({
          number: recipient,
          text: message.text,
          delay: 1000 // 1 second between messages
        }))
      };
      
      try {
        const response = await fetch(this.config.webhook_url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': this.config.api_key
          },
          body: JSON.stringify(payload)
        });
        
        const result = await response.json();
        results.push(...result.results);
      } catch (error) {
        console.error('N8N batch error:', error);
        // Record failed batch
        results.push(...batch.map(r => ({
          recipient: r,
          success: false,
          error: error.message
        })));
      }
      
      // Rate limiting pause
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    return {
      total: recipients.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }
}
```

### 4. Approval Workflow Manager

**File**: `/src/services/whatsapp/ApprovalWorkflowManager.ts`

#### Workflow Types

```typescript
export enum WorkflowType {
  CT_DUPLICATE = 'ct_duplicate_approval',
  QC_REJECTION = 'qc_rejection_approval',
  PART_MAPPING = 'part_mapping_approval',
  TRANSFER_AUTH = 'transfer_authorization'
}

interface WorkflowConfig {
  [WorkflowType.CT_DUPLICATE]: {
    approvers: ['director'];
    priority: 'high';
    expiry: 24; // hours
    template: 'ct_duplicate_template';
  };
  [WorkflowType.QC_REJECTION]: {
    approvers: ['director', 'sb_staff'];
    priority: 'urgent';
    expiry: 4; // hours
    template: 'qc_rejection_template';
  };
  // ... other workflows
}
```

#### CT Duplicate Approval Implementation

```typescript
export class ApprovalWorkflowManager {
  async startCTDuplicateApproval(
    orderLine: OrderLineWithDetails,
    existingCT: string,
    requestedBy: User
  ): Promise<WorkflowResult> {
    // Create workflow record
    const workflow = await this.createWorkflow({
      workflow_type: WorkflowType.CT_DUPLICATE,
      context_data: {
        order_uid: orderLine.uid,
        part_number: orderLine.customer_part_number,
        existing_ct: existingCT,
        requested_by: requestedBy.email
      },
      initiated_by: requestedBy.id,
      expires_at: addHours(new Date(), 24)
    });
    
    // Get approvers
    const approvers = await this.getApprovers(WorkflowType.CT_DUPLICATE);
    
    // Send interactive messages
    for (const approver of approvers) {
      const message: InteractiveMessage = {
        header: '🔄 CT Duplicate Approval Required',
        body: `Order ${orderLine.uid} has duplicate CT ${existingCT}.\n\n` +
              `Part: ${orderLine.customer_part_number}\n` +
              `Requested by: ${requestedBy.email}\n\n` +
              `Please approve or reject the use of this duplicate CT.`,
        buttons: [
          {
            id: `approve_${workflow.id}`,
            text: '✅ Approve',
            type: 'quick_reply'
          },
          {
            id: `reject_${workflow.id}`,
            text: '❌ Reject',
            type: 'quick_reply'
          }
        ]
      };
      
      await this.sendApprovalMessage(approver, message, workflow.id);
    }
    
    return {
      workflowId: workflow.id,
      status: 'pending',
      sentTo: approvers.length
    };
  }
  
  async processApprovalResponse(
    buttonId: string,
    phoneNumber: string,
    timestamp: Date
  ): Promise<void> {
    // Parse button ID
    const [action, workflowId] = buttonId.split('_');
    
    // Get workflow
    const workflow = await this.getWorkflow(workflowId);
    if (!workflow || workflow.status !== 'pending') {
      return; // Already processed or expired
    }
    
    // Verify approver
    const approver = await this.getUserByPhone(phoneNumber);
    if (!this.canApprove(approver, workflow.workflow_type)) {
      return; // Unauthorized
    }
    
    // Update workflow
    await this.updateWorkflow(workflowId, {
      status: action === 'approve' ? 'approved' : 'rejected',
      approved_by: approver.id,
      approved_at: timestamp,
      response_data: { action, phone: phoneNumber }
    });
    
    // Execute post-approval actions
    if (action === 'approve' && workflow.workflow_type === WorkflowType.CT_DUPLICATE) {
      await this.handleCTDuplicateApproval(workflow);
    }
    
    // Notify requester
    await this.notifyRequester(workflow, action);
  }
}
```

### 5. Message Management

#### Database Schema

```sql
-- Provider configurations
CREATE TABLE whatsapp_provider_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider TEXT NOT NULL CHECK (provider IN ('meta_cloud', 'n8n_evolution')),
  config JSONB NOT NULL, -- Encrypted
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Message history
CREATE TABLE whatsapp_messages_v2 (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_phone TEXT NOT NULL,
  message_type TEXT NOT NULL,
  content JSONB NOT NULL,
  provider_used TEXT,
  status TEXT DEFAULT 'pending',
  external_id TEXT,
  metadata JSONB,
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  failed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Approval workflows
CREATE TABLE whatsapp_approval_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_type TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  context_data JSONB NOT NULL,
  initiated_by UUID REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  response_data JSONB,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- WhatsApp groups
CREATE TABLE whatsapp_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_name TEXT NOT NULL,
  group_type TEXT NOT NULL,
  phone_numbers TEXT[] NOT NULL,
  metadata JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. Integration UI Components

#### Order Card WhatsApp Button

```tsx
// In OrderCard.tsx
const OrderWhatsAppButton: React.FC<{ order: OrderLineWithDetails }> = ({ order }) => {
  const [showWhatsAppModal, setShowWhatsAppModal] = useState(false);
  const { sendOrderNotification } = useWhatsApp();
  
  const handleQuickMessage = async () => {
    const message = `Order Update: ${order.uid}\n` +
                   `Part: ${order.customer_part_number}\n` +
                   `Status: ${order.status}\n` +
                   `Quantity: ${order.quantity}`;
    
    await sendOrderNotification(order, message, 'status_update');
    toast.success('WhatsApp message sent');
  };
  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size="sm" variant="outline">
            <MessageSquare className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={handleQuickMessage}>
            Quick Status Update
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowWhatsAppModal(true)}>
            Custom Message...
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={() => startCTApproval(order)}
            disabled={!order.ct_numbers?.length}
          >
            Request CT Approval
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      {showWhatsAppModal && (
        <WhatsAppMessageModal
          order={order}
          onClose={() => setShowWhatsAppModal(false)}
        />
      )}
    </>
  );
};
```

#### Admin Configuration Interface

```tsx
// In WhatsAppSettings.tsx
const WhatsAppSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('providers');
  
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <TabsList>
        <TabsTrigger value="providers">Providers</TabsTrigger>
        <TabsTrigger value="groups">Groups</TabsTrigger>
        <TabsTrigger value="templates">Templates</TabsTrigger>
        <TabsTrigger value="logs">Message Logs</TabsTrigger>
      </TabsList>
      
      <TabsContent value="providers">
        <ProviderConfiguration />
      </TabsContent>
      
      <TabsContent value="groups">
        <WhatsAppGroupManager />
      </TabsContent>
      
      <TabsContent value="templates">
        <MessageTemplateManager />
      </TabsContent>
      
      <TabsContent value="logs">
        <MessageHistoryViewer />
      </TabsContent>
    </Tabs>
  );
};
```

## Configuration Guide

### Meta Cloud API Setup

1. **Create Facebook App**
   - Go to developers.facebook.com
   - Create new app (Business type)
   - Add WhatsApp product

2. **Configure Webhook**
   ```
   Webhook URL: https://your-domain.com/api/webhooks/whatsapp/meta
   Verify Token: [your-verify-token]
   Subscribe to: messages, message_status
   ```

3. **Get Credentials**
   - App ID from app dashboard
   - App Secret from settings
   - Access Token from WhatsApp > API Setup
   - Phone Number ID from WhatsApp > API Setup

### N8N Evolution Setup

1. **Deploy Evolution API**
   ```bash
   docker run -d \
     --name evolution-api \
     -p 8080:8080 \
     -e AUTHENTICATION_API_KEY=your-api-key \
     evolution-api/evolution-api:latest
   ```

2. **Configure N8N Workflow**
   - Create webhook trigger
   - Add Evolution API node
   - Configure message routing

3. **Connect Instance**
   - Scan QR code with WhatsApp
   - Verify connection status
   - Test message sending

## Error Handling

### Provider Failover

```typescript
const sendWithFailover = async (
  message: Message,
  providers: WhatsAppProvider[]
): Promise<MessageResult> => {
  let lastError: Error | null = null;
  
  for (const provider of providers) {
    try {
      const result = await provider.send(message);
      if (result.success) {
        return result;
      }
    } catch (error) {
      console.error(`Provider ${provider.name} failed:`, error);
      lastError = error;
      // Continue to next provider
    }
  }
  
  throw new Error(
    `All providers failed. Last error: ${lastError?.message}`
  );
};
```

### Rate Limiting

```typescript
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  async checkLimit(provider: string): Promise<boolean> {
    const now = Date.now();
    const window = 3600000; // 1 hour
    const limit = provider === 'meta_cloud' ? 1000 : 5000;
    
    const requests = this.requests.get(provider) || [];
    const validRequests = requests.filter(t => t > now - window);
    
    if (validRequests.length >= limit) {
      return false; // Rate limit exceeded
    }
    
    validRequests.push(now);
    this.requests.set(provider, validRequests);
    return true;
  }
}
```

## Monitoring & Analytics

### Message Delivery Tracking

```typescript
interface MessageMetrics {
  sent: number;
  delivered: number;
  read: number;
  failed: number;
  avgDeliveryTime: number;
  providerBreakdown: {
    meta_cloud: ProviderMetrics;
    n8n_evolution: ProviderMetrics;
  };
}

const getMessageMetrics = async (
  startDate: Date,
  endDate: Date
): Promise<MessageMetrics> => {
  const { data } = await supabase
    .from('whatsapp_messages_v2')
    .select('*')
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString());
    
  return calculateMetrics(data);
};
```

### Approval Workflow Analytics

```typescript
interface WorkflowMetrics {
  total: number;
  approved: number;
  rejected: number;
  expired: number;
  avgResponseTime: number;
  byType: Record<WorkflowType, TypeMetrics>;
}
```

## Security Considerations

### Credential Management
- All API credentials encrypted at rest
- Environment variables for sensitive data
- Regular credential rotation
- Audit logging for all API calls

### Message Security
- End-to-end encryption via WhatsApp
- No sensitive data in message logs
- Phone number hashing where possible
- RLS policies on all tables

## Testing Procedures

### Unit Tests
```typescript
describe('WhatsAppProviderManager', () => {
  it('should select Meta provider for approvals', async () => {
    const manager = new WhatsAppProviderManager();
    const provider = await manager.selectProvider('approval');
    expect(provider).toBeInstanceOf(MetaCloudProvider);
  });
  
  it('should failover when primary provider fails', async () => {
    // Test failover logic
  });
});
```

### Integration Tests
```typescript
describe('Approval Workflows', () => {
  it('should complete CT duplicate approval flow', async () => {
    // Test end-to-end workflow
  });
});
```

---
**Status**: Frontend Complete | Backend Complete | External Config Required
**Integration Points**: Order Management, CT System, QC Workflows
**Critical Dependencies**: Meta Cloud API credentials, N8N webhook setup