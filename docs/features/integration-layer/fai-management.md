# FAI Document Management System

## Overview

The First Article Inspection (FAI) document management system provides comprehensive handling of quality documentation, master reference images, and automated image extraction from Excel files. This system is critical for maintaining quality standards for HP and Lenovo operations.

## System Architecture

### Document Type Hierarchy

```typescript
enum FAIDocumentType {
  // Primary document types
  FAI_DOCUMENT = 'fai_document',        // Excel FAI files
  MASTER_IMAGE = 'master_image',        // Reference images
  FAI_IMAGE = 'fai_image',              // Extracted from FAI
  
  // Supporting types
  QUALITY_REPORT = 'quality_report',    // QC inspection reports  
  SPECIFICATION = 'specification',      // Part specifications
  APPROVAL_CERT = 'approval_cert'       // Customer approvals
}

interface FAIDocument {
  id: string;
  part_number: string;
  document_type: FAIDocumentType;
  file_name: string;
  file_url: string;
  file_size: number;
  mime_type: string;
  version: number;
  is_primary: boolean;
  metadata: {
    customer?: 'HP' | 'Lenovo';
    uploaded_by: string;
    extraction_status?: 'pending' | 'processing' | 'completed' | 'failed';
    extracted_images?: number;
    sheet_count?: number;
  };
  created_at: Date;
  updated_at: Date;
}
```

### Storage Architecture

#### Supabase Storage Structure

```
fai-documents/
├── {customer_part_number}/
│   ├── documents/
│   │   ├── FAI_20250612_123456_{original_name}.xlsx
│   │   ├── SPEC_20250612_123457_{original_name}.pdf
│   │   └── REPORT_20250612_123458_{original_name}.pdf
│   ├── master_images/
│   │   ├── MASTER_20250612_123459_{original_name}.jpg
│   │   ├── MASTER_20250612_123460_{original_name}_thumb.jpg
│   │   └── MASTER_20250612_123461_{original_name}_full.jpg
│   └── extracted/
│       ├── {fai_document_id}/
│       │   ├── sheet2_image_1.png
│       │   ├── sheet2_image_2.png
│       │   └── sheet2_image_3.png
└── _temp/
    └── processing/
```

### Database Schema

```sql
-- FAI Documents table
CREATE TABLE fai_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  part_number TEXT NOT NULL,
  document_type TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type TEXT NOT NULL,
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  uploaded_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_fai_documents_part_number (part_number),
  INDEX idx_fai_documents_type (document_type),
  INDEX idx_fai_documents_active (is_active)
);

-- Master Images table
CREATE TABLE master_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  part_number TEXT NOT NULL,
  image_name TEXT NOT NULL,
  image_path TEXT NOT NULL,
  image_url TEXT NOT NULL,
  thumbnail_url TEXT,
  file_size BIGINT NOT NULL,
  dimensions JSONB, -- {width, height}
  is_primary BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  uploaded_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT unique_primary_per_part UNIQUE (part_number, is_primary) 
    WHERE is_primary = true
);

-- FAI Extracted Images
CREATE TABLE fai_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fai_document_id UUID REFERENCES fai_documents(id) ON DELETE CASCADE,
  part_number TEXT NOT NULL,
  image_name TEXT NOT NULL,
  image_path TEXT NOT NULL,
  image_url TEXT NOT NULL,
  sheet_name TEXT,
  sheet_index INTEGER,
  extraction_metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Extraction Jobs
CREATE TABLE fai_extraction_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  fai_document_id UUID REFERENCES fai_documents(id),
  status TEXT DEFAULT 'pending',
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  extracted_count INTEGER DEFAULT 0,
  job_metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### RLS Policies

```sql
-- FAI Documents policies
CREATE POLICY "Users can view FAI documents" ON fai_documents
  FOR SELECT USING (true);

CREATE POLICY "Authorized users can upload FAI" ON fai_documents
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT id FROM users 
      WHERE role IN ('admin', 'director', 'sb_manager', 'qc_staff')
    )
  );

-- Master Images policies  
CREATE POLICY "Users can view master images" ON master_images
  FOR SELECT USING (true);

CREATE POLICY "Only admins can set primary images" ON master_images
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT id FROM users WHERE role IN ('admin', 'director')
    )
  ) WITH CHECK (is_primary = true);
```

## Core Implementation

### 1. FAI Document Upload Service

**File**: `/src/services/fai/FAIDocumentService.ts`

```typescript
export class FAIDocumentService {
  async uploadFAIDocument(
    file: File,
    partNumber: string,
    metadata?: Partial<FAIMetadata>
  ): Promise<FAIDocument> {
    // Validate file
    if (!this.isValidFAIFile(file)) {
      throw new Error('Invalid FAI file format. Expected Excel file.');
    }
    
    // Generate storage path
    const timestamp = format(new Date(), 'yyyyMMdd_HHmmss');
    const fileName = `FAI_${timestamp}_${file.name}`;
    const filePath = `${partNumber}/documents/${fileName}`;
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('fai-documents')
      .upload(filePath, file, {
        contentType: file.type,
        upsert: false
      });
      
    if (uploadError) throw uploadError;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('fai-documents')
      .getPublicUrl(filePath);
    
    // Determine version
    const version = await this.getNextVersion(partNumber, 'fai_document');
    
    // Create database record
    const { data: document, error: dbError } = await supabase
      .from('fai_documents')
      .insert({
        part_number: partNumber,
        document_type: 'fai_document',
        file_name: fileName,
        file_path: filePath,
        file_url: publicUrl,
        file_size: file.size,
        mime_type: file.type,
        version,
        metadata: {
          ...metadata,
          original_name: file.name,
          uploaded_by: (await supabase.auth.getUser()).data.user?.email,
          extraction_status: 'pending'
        },
        uploaded_by: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();
      
    if (dbError) throw dbError;
    
    // Queue for image extraction
    await this.queueImageExtraction(document.id);
    
    return document;
  }
  
  private async queueImageExtraction(documentId: string): Promise<void> {
    const { error } = await supabase
      .from('fai_extraction_jobs')
      .insert({
        fai_document_id: documentId,
        status: 'pending',
        job_metadata: {
          priority: 'normal',
          target_sheet: 'Sheet2' // HP requirement
        }
      });
      
    if (error) {
      console.error('Failed to queue extraction:', error);
    }
  }
  
  private isValidFAIFile(file: File): boolean {
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    return validTypes.includes(file.type);
  }
}
```

### 2. Master Image Management

**File**: `/src/services/fai/MasterImageService.ts`

```typescript
export class MasterImageService {
  async uploadMasterImage(
    file: File,
    partNumber: string,
    options?: UploadOptions
  ): Promise<MasterImage> {
    // Validate image
    if (!this.isValidImageFile(file)) {
      throw new Error('Invalid image format. Expected JPG, PNG, or WebP.');
    }
    
    // Process image
    const processedImages = await this.processImage(file);
    
    // Upload all versions
    const uploadResults = await Promise.all([
      this.uploadImageVersion(processedImages.original, partNumber, 'full'),
      this.uploadImageVersion(processedImages.optimized, partNumber, 'display'),
      this.uploadImageVersion(processedImages.thumbnail, partNumber, 'thumb')
    ]);
    
    // Create database record
    const { data: masterImage, error } = await supabase
      .from('master_images')
      .insert({
        part_number: partNumber,
        image_name: file.name,
        image_path: uploadResults[0].path,
        image_url: uploadResults[1].url, // Use optimized for display
        thumbnail_url: uploadResults[2].url,
        file_size: file.size,
        dimensions: processedImages.dimensions,
        is_primary: options?.setPrimary || false,
        metadata: {
          original_url: uploadResults[0].url,
          processing_info: processedImages.metadata
        },
        uploaded_by: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Handle primary designation
    if (options?.setPrimary) {
      await this.updatePrimaryImage(partNumber, masterImage.id);
    }
    
    return masterImage;
  }
  
  private async processImage(file: File): Promise<ProcessedImages> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        const img = new Image();
        img.onload = async () => {
          // Original dimensions
          const dimensions = {
            width: img.width,
            height: img.height
          };
          
          // Create canvases for different versions
          const original = await this.createImageBlob(img, img.width, img.height);
          const optimized = await this.createImageBlob(img, 1200, 1200, true);
          const thumbnail = await this.createImageBlob(img, 300, 300, true);
          
          resolve({
            original,
            optimized,
            thumbnail,
            dimensions,
            metadata: {
              quality: this.assessImageQuality(img),
              format: file.type
            }
          });
        };
        img.src = e.target?.result as string;
      };
      
      reader.readAsDataURL(file);
    });
  }
  
  private async createImageBlob(
    img: HTMLImageElement,
    maxWidth: number,
    maxHeight: number,
    maintainAspect: boolean = false
  ): Promise<Blob> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    let { width, height } = img;
    
    if (maintainAspect && (width > maxWidth || height > maxHeight)) {
      const ratio = Math.min(maxWidth / width, maxHeight / height);
      width *= ratio;
      height *= ratio;
    }
    
    canvas.width = width;
    canvas.height = height;
    ctx.drawImage(img, 0, 0, width, height);
    
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/jpeg', 0.85);
    });
  }
  
  async updatePrimaryImage(
    partNumber: string,
    imageId: string
  ): Promise<void> {
    // First, unset any existing primary
    await supabase
      .from('master_images')
      .update({ is_primary: false })
      .eq('part_number', partNumber)
      .eq('is_primary', true);
    
    // Set new primary
    await supabase
      .from('master_images')
      .update({ is_primary: true })
      .eq('id', imageId);
  }
}
```

### 3. FAI Image Extraction Service

**File**: `/src/services/fai/ImageExtractionService.ts`

```typescript
export class ImageExtractionService {
  async processExtractionJob(jobId: string): Promise<void> {
    // Get job details
    const { data: job } = await supabase
      .from('fai_extraction_jobs')
      .select(`
        *,
        fai_document:fai_documents(*)
      `)
      .eq('id', jobId)
      .single();
      
    if (!job || job.status !== 'pending') return;
    
    try {
      // Update job status
      await this.updateJobStatus(jobId, 'processing');
      
      // Download FAI document
      const fileBuffer = await this.downloadDocument(job.fai_document.file_path);
      
      // Extract images from Excel
      const extractedImages = await this.extractImagesFromExcel(
        fileBuffer,
        job.job_metadata?.target_sheet || 'Sheet2'
      );
      
      // Upload extracted images
      const uploadedImages = await this.uploadExtractedImages(
        extractedImages,
        job.fai_document_id,
        job.fai_document.part_number
      );
      
      // Update job completion
      await this.updateJobStatus(jobId, 'completed', {
        extracted_count: uploadedImages.length,
        completed_at: new Date()
      });
      
      // Update FAI document metadata
      await supabase
        .from('fai_documents')
        .update({
          metadata: {
            extraction_status: 'completed',
            extracted_images: uploadedImages.length
          }
        })
        .eq('id', job.fai_document_id);
        
    } catch (error) {
      await this.updateJobStatus(jobId, 'failed', {
        error_message: error.message
      });
      throw error;
    }
  }
  
  private async extractImagesFromExcel(
    buffer: ArrayBuffer,
    targetSheet: string
  ): Promise<ExtractedImage[]> {
    // This would use a library like xlsx or exceljs
    // Pseudo-code for the extraction process:
    const workbook = await ExcelJS.Workbook.xlsx.load(buffer);
    const worksheet = workbook.getWorksheet(targetSheet);
    const images: ExtractedImage[] = [];
    
    // Excel stores images as media objects
    for (const image of worksheet.getImages()) {
      const imageData = workbook.model.media.find(
        m => m.name === image.imageId
      );
      
      if (imageData) {
        images.push({
          name: `sheet2_image_${images.length + 1}.png`,
          data: imageData.buffer,
          metadata: {
            sheet: targetSheet,
            position: image.range,
            index: images.length
          }
        });
      }
    }
    
    return images;
  }
  
  private async uploadExtractedImages(
    images: ExtractedImage[],
    documentId: string,
    partNumber: string
  ): Promise<FAIImage[]> {
    const uploaded: FAIImage[] = [];
    
    for (const image of images) {
      const path = `${partNumber}/extracted/${documentId}/${image.name}`;
      
      // Upload to storage
      const { error: uploadError } = await supabase.storage
        .from('fai-documents')
        .upload(path, image.data, {
          contentType: 'image/png'
        });
        
      if (uploadError) {
        console.error('Failed to upload extracted image:', uploadError);
        continue;
      }
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('fai-documents')
        .getPublicUrl(path);
      
      // Create database record
      const { data: faiImage } = await supabase
        .from('fai_images')
        .insert({
          fai_document_id: documentId,
          part_number: partNumber,
          image_name: image.name,
          image_path: path,
          image_url: publicUrl,
          sheet_name: image.metadata.sheet,
          sheet_index: image.metadata.index,
          extraction_metadata: image.metadata
        })
        .select()
        .single();
        
      if (faiImage) {
        uploaded.push(faiImage);
      }
    }
    
    return uploaded;
  }
}
```

### 4. FAI Hook Implementation

**File**: `/src/hooks/useFAI.ts`

```typescript
export const useFAI = () => {
  const queryClient = useQueryClient();
  
  // Fetch FAI documents for a part
  const getFAIDocuments = (partNumber: string) => {
    return useQuery({
      queryKey: ['fai-documents', partNumber],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('fai_documents')
          .select('*')
          .eq('part_number', partNumber)
          .eq('is_active', true)
          .order('version', { ascending: false });
          
        if (error) throw error;
        return data;
      }
    });
  };
  
  // Fetch master images
  const getMasterImages = (partNumber: string) => {
    return useQuery({
      queryKey: ['master-images', partNumber],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('master_images')
          .select('*')
          .eq('part_number', partNumber)
          .order('is_primary', { ascending: false })
          .order('created_at', { ascending: false });
          
        if (error) throw error;
        return data;
      }
    });
  };
  
  // Upload FAI document mutation
  const uploadFAIDocument = useMutation({
    mutationFn: async ({
      file,
      partNumber,
      metadata
    }: {
      file: File;
      partNumber: string;
      metadata?: any;
    }) => {
      const service = new FAIDocumentService();
      return service.uploadFAIDocument(file, partNumber, metadata);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries(['fai-documents', data.part_number]);
      toast.success('FAI document uploaded successfully');
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`);
    }
  });
  
  // Get all images for a part (master + FAI)
  const getAllImagesForPart = async (partNumber: string) => {
    const [masterImages, faiImages] = await Promise.all([
      supabase
        .from('master_images')
        .select('*')
        .eq('part_number', partNumber),
      supabase
        .from('fai_images')
        .select('*')
        .eq('part_number', partNumber)
    ]);
    
    return {
      masterImages: masterImages.data || [],
      faiImages: faiImages.data || [],
      primaryImage: masterImages.data?.find(img => img.is_primary)
    };
  };
  
  return {
    getFAIDocuments,
    getMasterImages,
    uploadFAIDocument,
    getAllImagesForPart,
    // ... other methods
  };
};
```

### 5. UI Components

#### FAI Upload Component

```tsx
const FAIUploadButton: React.FC<{ partNumber: string }> = ({ partNumber }) => {
  const { uploadFAIDocument } = useFAI();
  const [uploading, setUploading] = useState(false);
  
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setUploading(true);
    try {
      await uploadFAIDocument.mutateAsync({
        file,
        partNumber,
        metadata: {
          customer: detectCustomer(partNumber)
        }
      });
    } finally {
      setUploading(false);
    }
  };
  
  return (
    <div>
      <input
        type="file"
        accept=".xlsx,.xls"
        onChange={handleFileSelect}
        disabled={uploading}
        style={{ display: 'none' }}
        id="fai-upload"
      />
      <label htmlFor="fai-upload">
        <Button
          as="span"
          disabled={uploading}
          variant="outline"
          size="sm"
        >
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload FAI
            </>
          )}
        </Button>
      </label>
    </div>
  );
};
```

#### FAI Image Viewer

```tsx
const FAIImageViewer: React.FC<{ partNumber: string }> = ({ partNumber }) => {
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const { data: images, isLoading } = useQuery({
    queryKey: ['all-images', partNumber],
    queryFn: () => getAllImagesForPart(partNumber)
  });
  
  if (isLoading) return <Skeleton className="h-32 w-full" />;
  
  return (
    <div className="space-y-4">
      {/* Primary Master Image */}
      {images?.primaryImage && (
        <div className="border rounded-lg p-4">
          <h4 className="text-sm font-medium mb-2">Primary Reference</h4>
          <img
            src={images.primaryImage.thumbnail_url}
            alt="Primary master"
            className="h-32 w-auto cursor-pointer"
            onClick={() => setSelectedImage(images.primaryImage)}
          />
        </div>
      )}
      
      {/* FAI Extracted Images */}
      {images?.faiImages.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">
            FAI Images ({images.faiImages.length})
          </h4>
          <div className="grid grid-cols-4 gap-2">
            {images.faiImages.map((img) => (
              <img
                key={img.id}
                src={img.image_url}
                alt={img.image_name}
                className="h-20 w-auto cursor-pointer border rounded"
                onClick={() => setSelectedImage(img)}
              />
            ))}
          </div>
        </div>
      )}
      
      {/* Image Modal */}
      {selectedImage && (
        <ImageViewerModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </div>
  );
};
```

## Background Processing

### Image Extraction Worker

```typescript
// This would run as a background service
class FAIExtractionWorker {
  async start() {
    // Poll for pending jobs
    setInterval(async () => {
      const { data: jobs } = await supabase
        .from('fai_extraction_jobs')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })
        .limit(1);
        
      if (jobs?.length > 0) {
        const service = new ImageExtractionService();
        await service.processExtractionJob(jobs[0].id);
      }
    }, 10000); // Check every 10 seconds
  }
}
```

## Security Considerations

1. **File Validation**
   - Strict MIME type checking
   - File size limits (50MB for FAI, 10MB for images)
   - Virus scanning integration point

2. **Access Control**
   - RLS policies for role-based access
   - Part-specific permissions
   - Audit trail for all uploads

3. **Data Protection**
   - Secure storage URLs with expiration
   - Image optimization to remove EXIF data
   - Encrypted metadata storage

## Performance Optimization

1. **Image Processing**
   - Client-side image optimization before upload
   - Progressive loading with thumbnails
   - Lazy loading for image grids

2. **Storage Optimization**
   - Automatic cleanup of old versions
   - Compression for archived documents
   - CDN integration for global access

3. **Query Optimization**
   - Indexed part number lookups
   - Paginated document lists
   - Cached primary images

---
**Status**: Frontend 90% | Backend 85% | Image Extraction Pending
**Critical Dependencies**: Excel image extraction service
**Next Steps**: Implement background extraction worker