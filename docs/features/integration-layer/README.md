# Integration Layer Module

## Overview

The Mini-ERP integration layer provides sophisticated external system connectivity through WhatsApp messaging, FAI document management, and automated approval workflows. This module enables real-time communication, document tracking, and business process automation.

## System Architecture

### Integration Components

```
┌─────────────────────────────────────────────────────────────────────┐
│                       Integration Layer Architecture                 │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌─────────────────┐  ┌──────────────────┐  ┌─────────────────┐  │
│  │    WhatsApp     │  │  FAI Documents   │  │   Approval      │  │
│  │  Integration    │  │   Management     │  │   Workflows     │  │
│  └────────┬────────┘  └────────┬─────────┘  └────────┬────────┘  │
│           │                    │                      │            │
│  ┌────────▼─────────────────────▼──────────────────────▼────────┐ │
│  │              Core Integration Services                        │ │
│  ├───────────────────────────────────────────────────────────────┤ │
│  │ • WhatsAppProviderManager (Dual Provider Architecture)       │ │
│  │ • ApprovalWorkflowManager (4 Workflow Types)                 │ │
│  │ • FAI Storage Service (Supabase Integration)                 │ │
│  │ • Notification Orchestrator (Multi-Channel)                  │ │
│  └───────────────────────────────────────────────────────────────┘ │
│                                                                     │
│  External Systems                    Internal Systems               │
│  ┌─────────────────┐                ┌─────────────────┐           │
│  │ Meta Cloud API  │                │ Order Management│           │
│  │ N8N Webhooks    │◄──────────────►│ CT System       │           │
│  │ Evolution API   │                │ QC Workflows    │           │
│  │ SMTP Server     │                │ Print System    │           │
│  └─────────────────┘                └─────────────────┘           │
└─────────────────────────────────────────────────────────────────────┘
```

### Production Status

| Component | Frontend | Backend | External Config | Overall |
|-----------|----------|---------|-----------------|----------|
| WhatsApp Integration | ✅ 100% | ✅ 95% | ⚠️ Required | 95% |
| FAI Management | ✅ 90% | ✅ 85% | ❌ Missing | 85% |
| Approval Workflows | ✅ 95% | ✅ 90% | ⚠️ Required | 90% |
| Notification System | ✅ 100% | ⚠️ 70% | ⚠️ Required | 80% |

## Key Features

### 1. WhatsApp Integration

#### Dual Provider Architecture
- **Meta Cloud API**: Primary provider for critical business communications
- **N8N Evolution**: Secondary provider for bulk messaging and cost optimization
- **Intelligent Routing**: Automatic provider selection based on priority and cost
- **Fallback Mechanisms**: Seamless failover between providers

#### Interactive Messaging
- **Button Templates**: Quick approve/reject actions directly in WhatsApp
- **24-Hour Expiration**: Automatic workflow cleanup for unanswered requests
- **Rich Media Support**: Images, documents, and formatted text
- **Group Broadcasting**: Targeted messaging to role-based groups

#### Business Workflows
1. **CT Duplicate Approval**: Instant director approval for duplicate CT usage
2. **QC Rejection Handling**: Urgent notifications with decision buttons
3. **Part Mapping Approval**: Customer part to BPI description mapping
4. **Transfer Authorization**: Inter-location transfer approvals

### 2. FAI Document Management

#### Document Types
- **FAI Documents**: Excel files with quality inspection data
- **Master Images**: High-resolution reference images for parts
- **Extracted Images**: Images automatically extracted from FAI Excel files

#### Storage Architecture
```
Supabase Storage
└── fai-documents/
    └── {part_number}/
        ├── FAI_{timestamp}_{filename}.xlsx
        ├── master_{timestamp}_{filename}.jpg
        └── extracted/
            └── sheet2_image_{n}.png
```

#### Key Features
- **Version Control**: Multiple FAI versions per part number
- **Primary Designation**: Mark master images as primary reference
- **Permission-Based Access**: Role-specific viewing and upload rights
- **Automatic Thumbnails**: Web-optimized versions for fast loading
- **Audit Trail**: Complete upload and access history

### 3. Approval Workflow Engine

#### Workflow Components
- **Initiator**: User or system triggering approval request
- **Approvers**: Role-based recipient selection
- **Actions**: Approve, reject, or request more information
- **Notifications**: Multi-channel delivery (WhatsApp, email fallback)
- **Tracking**: Real-time status updates and audit logging

#### Database Schema
```sql
-- Core workflow tracking
whatsapp_approval_workflows
├── id (UUID)
├── workflow_type (ENUM)
├── status (pending/approved/rejected/expired)
├── context_data (JSONB)
├── initiated_by (User ID)
├── approved_by (User ID)
├── created_at
└── expires_at

-- Provider configuration
whatsapp_provider_configs
├── provider (meta_cloud/n8n_evolution)
├── config (JSONB - encrypted credentials)
├── is_active
└── priority

-- Message history
whatsapp_messages_v2
├── recipient_phone
├── message_type
├── content
├── provider_used
├── status
└── metadata (JSONB)
```

## Integration Points

### Order Management Integration
- **Order Card Actions**: WhatsApp button on every order
- **Status Triggers**: Automated notifications on state changes
- **CT Assignment**: Approval workflows for duplicates
- **QC Integration**: Rejection approval workflows

### Printing System Integration
- **FAI Context**: Print labels with FAI reference data
- **Master Images**: Include on quality documentation
- **Approval Status**: Print only after approvals

### User Interface Integration
- **Admin Dashboard**: Complete provider and workflow management
- **Order Cards**: Integrated WhatsApp and FAI actions
- **Notification Center**: Unified view of all communications
- **Approval Queue**: Pending approvals dashboard

## Configuration Requirements

### WhatsApp Setup

#### Meta Cloud API
```env
VITE_META_APP_ID=your_app_id
VITE_META_APP_SECRET=your_app_secret
VITE_META_ACCESS_TOKEN=your_access_token
VITE_META_PHONE_NUMBER_ID=your_phone_number_id
VITE_META_WEBHOOK_VERIFY_TOKEN=your_verify_token
```

#### N8N Evolution API
```env
VITE_N8N_WEBHOOK_URL=https://your-n8n.com/webhook/whatsapp
VITE_N8N_API_KEY=your_n8n_api_key
VITE_EVOLUTION_INSTANCE=your_evolution_instance
```

### Background Services Required

1. **FAI Image Extraction Service**
   - Extract images from Excel Sheet2
   - Optimize and store in Supabase
   - Update extraction job status

2. **Workflow Cleanup Service**
   - Mark expired workflows
   - Clean up old messages
   - Archive completed workflows

3. **Notification Delivery Service**
   - Queue and deliver messages
   - Handle retries and failures
   - Track delivery status

## Testing Requirements

### Unit Testing
- [ ] Provider selection logic
- [ ] Workflow state transitions
- [ ] FAI upload validations
- [ ] Permission checks

### Integration Testing
- [ ] End-to-end approval workflows
- [ ] WhatsApp message delivery
- [ ] FAI image extraction
- [ ] Multi-provider failover

### System Testing
- [ ] Load testing with 20-30 users
- [ ] Provider rate limit handling
- [ ] Storage quota management
- [ ] Workflow expiration

## Performance Considerations

### Optimization Strategies
- **Message Batching**: Group notifications for efficiency
- **Provider Caching**: Cache provider configurations
- **Image Optimization**: Automatic compression and thumbnails
- **Query Optimization**: Indexed workflow lookups

### Scalability Limits
- **WhatsApp Rate Limits**: 1000 messages/hour (Meta)
- **Storage Limits**: 5GB per organization (Supabase)
- **Concurrent Workflows**: 100 active workflows
- **Image Processing**: 10 concurrent extractions

## Security Considerations

### Data Protection
- **Credential Encryption**: All API keys encrypted at rest
- **RLS Policies**: Row-level security on all tables
- **Audit Logging**: Complete trail of all actions
- **Phone Number Privacy**: Hashed storage where possible

### Access Control
- **Role-Based Permissions**: Directors, managers, staff levels
- **Workflow Approvers**: Configurable by workflow type
- **Document Access**: Part-specific permissions
- **API Rate Limiting**: Prevent abuse

## Module Structure

This module contains detailed documentation for:
1. **whatsapp-integration.md** - Complete WhatsApp architecture
2. **fai-management.md** - FAI document system details
3. **approval-workflows.md** - Workflow engine implementation
4. **notification-system.md** - Multi-channel notifications
5. **external-config.md** - Setup and configuration guide
6. **testing-guide.md** - Comprehensive testing procedures

## Future Enhancements

### Phase 1: Complete Current Implementation
1. Configure Meta Cloud API credentials
2. Setup N8N webhooks and Evolution API
3. Implement FAI image extraction service
4. Complete approval dashboard UI

### Phase 2: Enhanced Features
1. Email fallback for all workflows
2. SMS integration for critical alerts
3. Mobile app push notifications
4. Advanced analytics dashboard

### Phase 3: AI Integration
1. Smart approval routing
2. Predictive workflow triggers
3. Automated FAI analysis
4. Intelligent notification scheduling

---
**Module Status**: Core Complete | External Configuration Required
**Last Updated**: 12/06/2025, 9:15 PM IST
**Next Steps**: Configure external APIs and implement background services