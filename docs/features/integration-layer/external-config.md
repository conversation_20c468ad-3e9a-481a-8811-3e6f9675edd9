# External Integration Configuration Guide

## Overview

This guide provides step-by-step instructions for configuring all external integrations required for the Mini-ERP system. These configurations are essential for enabling WhatsApp messaging, email notifications, FAI processing, and other integration features.

## WhatsApp Integration Setup

### Meta Cloud API Configuration

#### Prerequisites
- Facebook Business Manager account
- Verified business
- WhatsApp Business phone number

#### Step 1: Create Facebook App

1. Go to [developers.facebook.com](https://developers.facebook.com)
2. Click "Create App"
3. Select "Business" type
4. Fill in app details:
   ```
   App Name: Mini-ERP WhatsApp Integration
   App Purpose: Business
   Business Manager Account: [Select your account]
   ```

#### Step 2: Add WhatsApp Product

1. In app dashboard, click "Add Product"
2. Find "WhatsApp" and click "Set Up"
3. Accept WhatsApp Business Terms

#### Step 3: Configure Phone Number

1. Go to WhatsApp > API Setup
2. Add phone number:
   ```
   Display Name: Mini-ERP Support
   Category: Business
   Business Description: Internal order management system
   ```
3. Verify phone number via SMS/Voice

#### Step 4: Generate Access Token

1. In WhatsApp > API Setup:
   - Copy "Phone number ID"
   - Generate "Permanent Access Token"
   - Copy "WhatsApp Business Account ID"

2. Configure webhook:
   ```
   Callback URL: https://your-domain.com/api/webhooks/whatsapp/meta
   Verify Token: [Generate secure random string]
   Webhook Fields: messages, message_status
   ```

#### Step 5: Update Environment Variables

```bash
# .env.local
VITE_META_APP_ID=your_app_id
VITE_META_APP_SECRET=your_app_secret
VITE_META_ACCESS_TOKEN=your_permanent_token
VITE_META_PHONE_NUMBER_ID=your_phone_number_id
VITE_META_WABA_ID=your_business_account_id
VITE_META_WEBHOOK_VERIFY_TOKEN=your_verify_token
```

### N8N Evolution API Setup

#### Step 1: Deploy Evolution API

```bash
# Using Docker
docker run -d \
  --name evolution-api \
  -p 8080:8080 \
  -e AUTHENTICATION_API_KEY=your-secure-api-key \
  -e AUTHENTICATION_TYPE=apikey \
  -e CORS_ORIGIN=* \
  -e LOG_LEVEL=info \
  -v evolution-data:/app/data \
  evolutionapi/evolution-api:latest
```

#### Step 2: Create WhatsApp Instance

```bash
# Create instance via API
curl -X POST http://localhost:8080/instance/create \
  -H "Content-Type: application/json" \
  -H "apikey: your-secure-api-key" \
  -d '{
    "instanceName": "minierp",
    "number": "************",
    "webhookUrl": "https://your-n8n.com/webhook/whatsapp"
  }'
```

#### Step 3: Connect WhatsApp

1. Access Evolution UI: `http://localhost:8080`
2. Go to Instances > minierp > QR Code
3. Scan QR code with WhatsApp mobile app
4. Verify connection status

#### Step 4: Configure N8N Workflow

1. Create new workflow in N8N
2. Add webhook trigger:
   ```json
   {
     "httpMethod": "POST",
     "path": "whatsapp",
     "responseMode": "onReceived",
     "responseData": "allEntries"
   }
   ```

3. Add Evolution API node:
   ```json
   {
     "resource": "message",
     "operation": "send",
     "instance": "minierp",
     "number": "{{$json.recipient}}",
     "text": "{{$json.message}}"
   }
   ```

#### Step 5: Update Environment Variables

```bash
# .env.local
VITE_N8N_WEBHOOK_URL=https://your-n8n.com/webhook/whatsapp
VITE_N8N_API_KEY=your-n8n-api-key
VITE_EVOLUTION_API_URL=http://localhost:8080
VITE_EVOLUTION_API_KEY=your-secure-api-key
VITE_EVOLUTION_INSTANCE=minierp
```

## Email Configuration (SMTP)

### Gmail SMTP Setup

#### Step 1: Enable 2-Factor Authentication

1. Go to Google Account settings
2. Security > 2-Step Verification
3. Enable and configure

#### Step 2: Generate App Password

1. Go to Security > App passwords
2. Select app: Mail
3. Select device: Other (Mini-ERP)
4. Copy generated password

#### Step 3: Configure SMTP Settings

```bash
# .env.local
VITE_SMTP_HOST=smtp.gmail.com
VITE_SMTP_PORT=587
VITE_SMTP_SECURE=false
VITE_SMTP_USER=<EMAIL>
VITE_SMTP_PASS=your-app-password
VITE_SMTP_FROM="Mini-ERP System <<EMAIL>>"
```

### SendGrid Setup (Alternative)

```bash
# .env.local
VITE_SENDGRID_API_KEY=your-sendgrid-api-key
VITE_SENDGRID_FROM=<EMAIL>
VITE_SENDGRID_FROM_NAME="Mini-ERP System"
```

## Supabase Configuration

### Storage Buckets

#### Create FAI Documents Bucket

```sql
-- Run in Supabase SQL Editor
INSERT INTO storage.buckets (id, name, public)
VALUES ('fai-documents', 'fai-documents', true);

-- Set up RLS policies
CREATE POLICY "Anyone can view FAI documents"
ON storage.objects FOR SELECT
USING (bucket_id = 'fai-documents');

CREATE POLICY "Authorized users can upload FAI documents"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'fai-documents' AND
  auth.uid() IN (
    SELECT id FROM users 
    WHERE role IN ('admin', 'director', 'sb_manager', 'qc_staff')
  )
);
```

### Database Functions

#### WhatsApp Group Management

```sql
-- Create function to get WhatsApp recipients
CREATE OR REPLACE FUNCTION get_whatsapp_recipients(
  group_type TEXT,
  location TEXT DEFAULT NULL
)
RETURNS TABLE (
  user_id UUID,
  name TEXT,
  phone_number TEXT,
  role TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.id,
    u.name,
    u.phone_number,
    u.role
  FROM users u
  INNER JOIN whatsapp_group_members wgm ON u.id = wgm.user_id
  INNER JOIN whatsapp_groups wg ON wgm.group_id = wg.id
  WHERE 
    wg.group_type = group_type
    AND wg.is_active = true
    AND (location IS NULL OR u.location = location)
    AND u.phone_number IS NOT NULL;
END;
$$ LANGUAGE plpgsql;
```

### Edge Functions

#### WhatsApp Webhook Handler

```typescript
// supabase/functions/whatsapp-webhook/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
)

serve(async (req) => {
  // Verify webhook
  if (req.method === 'GET') {
    const url = new URL(req.url)
    const mode = url.searchParams.get('hub.mode')
    const token = url.searchParams.get('hub.verify_token')
    const challenge = url.searchParams.get('hub.challenge')
    
    if (mode === 'subscribe' && token === Deno.env.get('META_WEBHOOK_VERIFY_TOKEN')) {
      return new Response(challenge, { status: 200 })
    }
    
    return new Response('Forbidden', { status: 403 })
  }
  
  // Process webhook
  if (req.method === 'POST') {
    const body = await req.json()
    
    // Process messages
    for (const entry of body.entry || []) {
      for (const change of entry.changes || []) {
        if (change.field === 'messages') {
          await processMessage(change.value)
        }
      }
    }
    
    return new Response('OK', { status: 200 })
  }
})

async function processMessage(value: any) {
  const message = value.messages?.[0]
  if (!message) return
  
  // Handle button responses
  if (message.type === 'interactive') {
    const buttonId = message.interactive.button_reply.id
    const [action, type, workflowId] = buttonId.split('_')
    
    // Update workflow
    await supabase
      .from('whatsapp_approval_workflows')
      .update({
        status: action === 'approve' ? 'approved' : 'rejected',
        response_data: { 
          button_id: buttonId,
          responder_phone: message.from
        },
        approved_at: new Date().toISOString()
      })
      .eq('id', workflowId)
  }
}
```

## Background Services Setup

### FAI Image Extraction Service

#### Using Node.js Worker

```javascript
// workers/fai-extraction-worker.js
const ExcelJS = require('exceljs');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function processExtractionJobs() {
  while (true) {
    try {
      // Get pending job
      const { data: job } = await supabase
        .from('fai_extraction_jobs')
        .select('*, fai_document:fai_documents(*)')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })
        .limit(1)
        .single();
        
      if (!job) {
        await sleep(10000); // Wait 10 seconds
        continue;
      }
      
      // Process job
      await processJob(job);
      
    } catch (error) {
      console.error('Worker error:', error);
      await sleep(30000); // Wait 30 seconds on error
    }
  }
}

async function processJob(job) {
  // Update status
  await updateJobStatus(job.id, 'processing');
  
  try {
    // Download file from Supabase
    const { data: fileData } = await supabase.storage
      .from('fai-documents')
      .download(job.fai_document.file_path);
      
    // Extract images from Excel
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(fileData);
    
    const worksheet = workbook.getWorksheet('Sheet2');
    const images = [];
    
    // Extract embedded images
    for (const image of worksheet.getImages()) {
      const imageData = workbook.model.media.find(
        media => media.name === image.imageId
      );
      
      if (imageData) {
        images.push({
          buffer: imageData.buffer,
          extension: imageData.extension,
          name: `sheet2_image_${images.length + 1}`
        });
      }
    }
    
    // Upload extracted images
    for (const image of images) {
      const path = `${job.fai_document.part_number}/extracted/${job.fai_document_id}/${image.name}.${image.extension}`;
      
      await supabase.storage
        .from('fai-documents')
        .upload(path, image.buffer, {
          contentType: `image/${image.extension}`
        });
        
      // Create database record
      await supabase
        .from('fai_images')
        .insert({
          fai_document_id: job.fai_document_id,
          part_number: job.fai_document.part_number,
          image_name: `${image.name}.${image.extension}`,
          image_path: path,
          sheet_name: 'Sheet2'
        });
    }
    
    // Update job status
    await updateJobStatus(job.id, 'completed', {
      extracted_count: images.length
    });
    
  } catch (error) {
    await updateJobStatus(job.id, 'failed', {
      error: error.message
    });
  }
}

// Start worker
processExtractionJobs();
```

#### Service Configuration

```bash
# PM2 ecosystem config
module.exports = {
  apps: [{
    name: 'fai-extraction-worker',
    script: './workers/fai-extraction-worker.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      SUPABASE_URL: 'your-supabase-url',
      SUPABASE_SERVICE_ROLE_KEY: 'your-service-role-key'
    }
  }]
};
```

### Notification Queue Worker

```javascript
// workers/notification-worker.js
const Queue = require('bull');
const nodemailer = require('nodemailer');

const notificationQueue = new Queue('notifications', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  }
});

// Email transporter
const emailTransporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
});

// Process notifications
notificationQueue.process(async (job) => {
  const { type, recipient, data } = job.data;
  
  switch (type) {
    case 'email':
      await sendEmail(recipient, data);
      break;
    case 'sms':
      await sendSMS(recipient, data);
      break;
    default:
      throw new Error(`Unknown notification type: ${type}`);
  }
});
```

## API Keys & Credentials Management

### Secure Storage

```typescript
// utils/credentials.ts
import CryptoJS from 'crypto-js';

const ENCRYPTION_KEY = process.env.VITE_ENCRYPTION_KEY;

export const encryptCredentials = (credentials: any): string => {
  const jsonString = JSON.stringify(credentials);
  return CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
};

export const decryptCredentials = (encrypted: string): any => {
  const decrypted = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_KEY);
  return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
};
```

### Environment Variables Template

```bash
# .env.example
# Supabase
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# WhatsApp - Meta Cloud API
VITE_META_APP_ID=
VITE_META_APP_SECRET=
VITE_META_ACCESS_TOKEN=
VITE_META_PHONE_NUMBER_ID=
VITE_META_WABA_ID=
VITE_META_WEBHOOK_VERIFY_TOKEN=

# WhatsApp - N8N Evolution
VITE_N8N_WEBHOOK_URL=
VITE_N8N_API_KEY=
VITE_EVOLUTION_API_URL=
VITE_EVOLUTION_API_KEY=
VITE_EVOLUTION_INSTANCE=

# Email
VITE_SMTP_HOST=
VITE_SMTP_PORT=
VITE_SMTP_SECURE=
VITE_SMTP_USER=
VITE_SMTP_PASS=
VITE_SMTP_FROM=

# SMS (Future)
VITE_TWILIO_ACCOUNT_SID=
VITE_TWILIO_AUTH_TOKEN=
VITE_TWILIO_PHONE_NUMBER=

# Security
VITE_ENCRYPTION_KEY=
VITE_JWT_SECRET=

# Background Services
REDIS_URL=
WORKER_CONCURRENCY=
```

## Testing External Integrations

### WhatsApp Test Suite

```typescript
// tests/whatsapp-integration.test.ts
describe('WhatsApp Integration', () => {
  it('should send test message via Meta Cloud API', async () => {
    const result = await whatsappService.sendMessage({
      to: process.env.TEST_PHONE_NUMBER,
      type: 'text',
      text: {
        body: 'Test message from Mini-ERP'
      }
    });
    
    expect(result.success).toBe(true);
    expect(result.messageId).toBeDefined();
  });
  
  it('should handle interactive button response', async () => {
    // Simulate webhook
    const webhookData = {
      entry: [{
        changes: [{
          field: 'messages',
          value: {
            messages: [{
              type: 'interactive',
              interactive: {
                button_reply: {
                  id: 'approve_ct_123',
                  title: 'Approve'
                }
              }
            }]
          }
        }]
      }]
    };
    
    await processWebhook(webhookData);
    
    // Verify workflow updated
    const workflow = await getWorkflow('123');
    expect(workflow.status).toBe('approved');
  });
});
```

### Email Test

```typescript
describe('Email Integration', () => {
  it('should send test email', async () => {
    const result = await emailService.send({
      to: '<EMAIL>',
      subject: 'Test Email',
      html: '<p>Test from Mini-ERP</p>'
    });
    
    expect(result.accepted).toContain('<EMAIL>');
  });
});
```

## Monitoring & Troubleshooting

### Health Check Endpoints

```typescript
// api/health/integrations
app.get('/api/health/integrations', async (req, res) => {
  const health = {
    whatsapp: {
      meta: await checkMetaAPI(),
      evolution: await checkEvolutionAPI()
    },
    email: await checkSMTP(),
    storage: await checkSupabaseStorage(),
    workers: {
      faiExtraction: await checkWorker('fai-extraction'),
      notifications: await checkWorker('notifications')
    }
  };
  
  res.json(health);
});
```

### Common Issues

1. **WhatsApp Token Expiry**
   - Solution: Generate permanent token or implement refresh

2. **Evolution API Disconnection**
   - Solution: Implement reconnection logic
   - Check QR code expiry

3. **SMTP Rate Limits**
   - Solution: Implement queue with rate limiting
   - Use dedicated email service

4. **Storage Quota**
   - Monitor usage in Supabase dashboard
   - Implement cleanup policies

---
**Configuration Checklist**:
- [ ] Meta Cloud API credentials
- [ ] WhatsApp webhook verified
- [ ] N8N Evolution instance connected
- [ ] SMTP configured and tested
- [ ] Supabase storage buckets created
- [ ] Background workers deployed
- [ ] Environment variables secured
- [ ] Health monitoring active