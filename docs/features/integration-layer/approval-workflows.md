# Approval Workflow Engine

## Overview

The approval workflow engine provides a comprehensive system for managing business approvals through WhatsApp interactive messaging. The system handles four critical workflow types with role-based routing, automatic expiration, and complete audit trails.

## Workflow Architecture

### Workflow Types

```typescript
enum WorkflowType {
  CT_DUPLICATE = 'ct_duplicate_approval',
  QC_REJECTION = 'qc_rejection_approval', 
  PART_MAPPING = 'part_mapping_approval',
  TRANSFER_AUTH = 'transfer_authorization'
}

interface WorkflowConfiguration {
  [WorkflowType.CT_DUPLICATE]: {
    name: 'CT Duplicate Approval';
    approvers: ['director'];
    priority: 'high';
    expiryHours: 24;
    requiresAllApprovers: false;
    notificationChannels: ['whatsapp'];
  };
  
  [WorkflowType.QC_REJECTION]: {
    name: 'QC Rejection Approval';
    approvers: ['director', 'sb_manager'];
    priority: 'urgent';
    expiryHours: 4;
    requiresAllApprovers: false;
    notificationChannels: ['whatsapp', 'email'];
  };
  
  [WorkflowType.PART_MAPPING]: {
    name: 'Part Mapping Approval';
    approvers: ['director'];
    priority: 'normal';
    expiryHours: 48;
    requiresAllApprovers: true;
    notificationChannels: ['whatsapp'];
  };
  
  [WorkflowType.TRANSFER_AUTH]: {
    name: 'Transfer Authorization';
    approvers: ['director', 'np_manager'];
    priority: 'high';
    expiryHours: 12;
    requiresAllApprovers: true;
    notificationChannels: ['whatsapp'];
  };
}
```

### Workflow State Machine

```
┌─────────────┐
│  Initiated  │
└──────┬──────┘
       │
       ↓
┌──────┬──────┐
│   Pending   │◄──────────────────────────────────┐
└──┬───┬──┬──┘                                     │
   │   │   │                                        │
   │   │   │                                        │
┌──▼─┐ │ ┌─▼──────┐                              │
│Approved│Rejected│                              │
└─────┘ │ └────────┘                              │
        │                                           │
        │   ┌─────────┐  ┌─────────────┐           │
        └──►│ Expired │  │ Cancelled  │◄──────────┘
            └─────────┘  └─────────────┘
```

## Implementation Details

### 1. CT Duplicate Approval Workflow

**File**: `/src/services/approval/workflows/CTDuplicateWorkflow.ts`

```typescript
export class CTDuplicateWorkflow implements ApprovalWorkflow {
  async initiate(context: CTDuplicateContext): Promise<WorkflowInstance> {
    const { orderLine, existingCT, newCT, requestedBy } = context;
    
    // Validate context
    if (!this.validateCTFormat(existingCT) || !this.validateCTFormat(newCT)) {
      throw new Error('Invalid CT number format');
    }
    
    // Create workflow instance
    const workflow = await this.createWorkflowInstance({
      workflow_type: WorkflowType.CT_DUPLICATE,
      context_data: {
        order_uid: orderLine.uid,
        customer: orderLine.customers?.name,
        part_number: orderLine.customer_part_number,
        existing_ct: existingCT,
        new_ct: newCT,
        quantity: orderLine.quantity,
        requested_by: requestedBy.email,
        reason: context.reason || 'Duplicate CT detected during assignment'
      },
      initiated_by: requestedBy.id,
      expires_at: this.calculateExpiry()
    });
    
    // Get approvers
    const directors = await this.getDirectors();
    
    // Send approval requests
    for (const director of directors) {
      await this.sendApprovalRequest(director, workflow);
    }
    
    // Log workflow initiation
    await this.logWorkflowEvent(workflow.id, 'initiated', {
      sent_to: directors.map(d => d.email)
    });
    
    return workflow;
  }
  
  private async sendApprovalRequest(
    approver: User,
    workflow: WorkflowInstance
  ): Promise<void> {
    const data = workflow.context_data as CTDuplicateContext;
    
    const message: InteractiveWhatsAppMessage = {
      to: approver.phone_number,
      type: 'interactive',
      interactive: {
        type: 'button',
        header: {
          type: 'text',
          text: '🔄 CT Duplicate Approval Required'
        },
        body: {
          text: this.formatApprovalMessage(data)
        },
        footer: {
          text: `Expires in ${this.getExpiryHours()} hours`
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: `approve_ct_${workflow.id}`,
                title: '✅ Approve Use'
              }
            },
            {
              type: 'reply',
              reply: {
                id: `reject_ct_${workflow.id}`,
                title: '❌ Generate New'
              }
            },
            {
              type: 'reply',
              reply: {
                id: `info_ct_${workflow.id}`,
                title: 'ℹ️ More Info'
              }
            }
          ]
        }
      }
    };
    
    await WhatsAppService.sendMessage(message);
  }
  
  private formatApprovalMessage(data: any): string {
    return [
      `*Duplicate CT Assignment Request*`,
      ``,
      `Order: ${data.order_uid}`,
      `Customer: ${data.customer}`,
      `Part: ${data.part_number}`,
      ``,
      `Existing CT: ${data.existing_ct}`,
      `Requested CT: ${data.new_ct}`,
      `Quantity: ${data.quantity}`,
      ``,
      `Requested by: ${data.requested_by}`,
      `Reason: ${data.reason}`,
      ``,
      `Please approve the use of duplicate CT or request a new one.`
    ].join('\n');
  }
  
  async processResponse(
    workflowId: string,
    action: string,
    responderId: string
  ): Promise<void> {
    const workflow = await this.getWorkflow(workflowId);
    
    // Validate workflow state
    if (workflow.status !== 'pending') {
      throw new Error('Workflow already processed');
    }
    
    // Process based on action
    switch (action) {
      case 'approve_ct':
        await this.approveWorkflow(workflow, responderId);
        break;
        
      case 'reject_ct':
        await this.rejectWorkflow(workflow, responderId);
        break;
        
      case 'info_ct':
        await this.sendAdditionalInfo(workflow, responderId);
        break;
        
      default:
        throw new Error('Invalid action');
    }
  }
  
  private async approveWorkflow(
    workflow: WorkflowInstance,
    approverId: string
  ): Promise<void> {
    // Update workflow status
    await this.updateWorkflowStatus(workflow.id, {
      status: 'approved',
      approved_by: approverId,
      approved_at: new Date(),
      response_data: {
        action: 'use_duplicate',
        approver_id: approverId
      }
    });
    
    // Execute approval actions
    const data = workflow.context_data;
    
    // Allow duplicate CT usage
    await this.allowDuplicateCT(data.new_ct, data.order_uid);
    
    // Notify requester
    await this.notifyRequester(
      workflow.initiated_by,
      'approved',
      'Duplicate CT usage has been approved. You may proceed with assignment.'
    );
    
    // Log completion
    await this.logWorkflowEvent(workflow.id, 'approved', {
      approver: approverId
    });
  }
}
```

### 2. QC Rejection Workflow

**File**: `/src/services/approval/workflows/QCRejectionWorkflow.ts`

```typescript
export class QCRejectionWorkflow implements ApprovalWorkflow {
  async initiate(context: QCRejectionContext): Promise<WorkflowInstance> {
    const { orderLine, rejectionDetails, inspector } = context;
    
    // Create urgent workflow
    const workflow = await this.createWorkflowInstance({
      workflow_type: WorkflowType.QC_REJECTION,
      context_data: {
        order_uid: orderLine.uid,
        customer: orderLine.customers?.name,
        part_number: orderLine.customer_part_number,
        quantity_rejected: rejectionDetails.quantity,
        rejection_reason: rejectionDetails.reason,
        defect_codes: rejectionDetails.defectCodes,
        inspector: inspector.name,
        inspection_date: new Date(),
        severity: this.calculateSeverity(rejectionDetails),
        images: rejectionDetails.imageUrls || []
      },
      initiated_by: inspector.id,
      expires_at: this.calculateUrgentExpiry(), // 4 hours
      priority: 'urgent'
    });
    
    // Get all approvers (directors + SB managers)
    const approvers = await this.getUrgentApprovers();
    
    // Send with high priority
    await this.sendUrgentNotifications(approvers, workflow);
    
    // Also send email for urgent items
    await this.sendEmailBackup(approvers, workflow);
    
    return workflow;
  }
  
  private async sendUrgentNotifications(
    approvers: User[],
    workflow: WorkflowInstance
  ): Promise<void> {
    const data = workflow.context_data;
    
    // Group message for urgent broadcast
    const message = {
      type: 'urgent_approval',
      header: '🚨 URGENT: QC Rejection Approval',
      body: [
        `*Critical QC Rejection Requires Immediate Attention*`,
        ``,
        `Order: ${data.order_uid}`,
        `Customer: ${data.customer}`,
        `Part: ${data.part_number}`,
        `Quantity Rejected: ${data.quantity_rejected}`,
        ``,
        `Reason: ${data.rejection_reason}`,
        `Severity: ${data.severity}`,
        `Inspector: ${data.inspector}`,
        ``,
        `⚠️ This requires immediate action to prevent delivery delays.`
      ].join('\n'),
      actions: [
        { id: `approve_qc_${workflow.id}`, text: '✅ Approve Rejection' },
        { id: `override_qc_${workflow.id}`, text: '🔄 Override & Pass' },
        { id: `investigate_qc_${workflow.id}`, text: '🔍 Investigate' }
      ],
      images: data.images // Include defect images
    };
    
    // Send to all approvers simultaneously
    await Promise.all(
      approvers.map(approver => 
        WhatsAppService.sendUrgentMessage(approver.phone_number, message)
      )
    );
  }
  
  private calculateSeverity(details: RejectionDetails): 'critical' | 'high' | 'medium' {
    // Critical if affects customer delivery
    if (details.affectsDelivery) return 'critical';
    
    // High if quantity > 50% of order
    if (details.quantity > details.orderQuantity * 0.5) return 'high';
    
    // Otherwise medium
    return 'medium';
  }
}
```

### 3. Workflow Manager

**File**: `/src/services/approval/ApprovalWorkflowManager.ts`

```typescript
export class ApprovalWorkflowManager {
  private workflows: Map<WorkflowType, ApprovalWorkflow>;
  private responseHandlers: Map<string, WorkflowResponseHandler>;
  
  constructor() {
    this.workflows = new Map([
      [WorkflowType.CT_DUPLICATE, new CTDuplicateWorkflow()],
      [WorkflowType.QC_REJECTION, new QCRejectionWorkflow()],
      [WorkflowType.PART_MAPPING, new PartMappingWorkflow()],
      [WorkflowType.TRANSFER_AUTH, new TransferAuthWorkflow()]
    ]);
    
    this.setupResponseHandlers();
    this.startExpiryMonitor();
  }
  
  async initiateWorkflow(
    type: WorkflowType,
    context: any,
    initiator: User
  ): Promise<WorkflowResult> {
    const workflow = this.workflows.get(type);
    if (!workflow) {
      throw new Error(`Unknown workflow type: ${type}`);
    }
    
    try {
      // Start workflow
      const instance = await workflow.initiate({
        ...context,
        initiator
      });
      
      // Track active workflow
      await this.trackActiveWorkflow(instance);
      
      // Set expiry timer
      this.scheduleExpiry(instance);
      
      return {
        success: true,
        workflowId: instance.id,
        expiresAt: instance.expires_at
      };
      
    } catch (error) {
      console.error(`Workflow initiation failed:`, error);
      throw error;
    }
  }
  
  async handleWhatsAppResponse(
    messageId: string,
    buttonId: string,
    phoneNumber: string
  ): Promise<void> {
    // Parse button ID to get action and workflow
    const [action, type, workflowId] = buttonId.split('_');
    
    // Get user from phone number
    const user = await this.getUserByPhone(phoneNumber);
    if (!user) {
      console.error(`Unknown phone number: ${phoneNumber}`);
      return;
    }
    
    // Get workflow instance
    const instance = await this.getWorkflowInstance(workflowId);
    if (!instance) {
      await this.sendExpiredMessage(phoneNumber);
      return;
    }
    
    // Verify user can approve
    if (!this.canApprove(user, instance)) {
      await this.sendUnauthorizedMessage(phoneNumber);
      return;
    }
    
    // Process response
    const workflow = this.workflows.get(instance.workflow_type);
    await workflow.processResponse(workflowId, action, user.id);
    
    // Send confirmation
    await this.sendConfirmation(phoneNumber, action, instance);
  }
  
  private startExpiryMonitor(): void {
    // Check for expired workflows every 5 minutes
    setInterval(async () => {
      const expired = await this.getExpiredWorkflows();
      
      for (const workflow of expired) {
        await this.expireWorkflow(workflow);
      }
    }, 5 * 60 * 1000);
  }
  
  private async expireWorkflow(workflow: WorkflowInstance): Promise<void> {
    // Update status
    await this.updateWorkflowStatus(workflow.id, {
      status: 'expired',
      expired_at: new Date()
    });
    
    // Notify initiator
    await this.notifyExpiry(workflow);
    
    // Execute expiry actions based on type
    switch (workflow.workflow_type) {
      case WorkflowType.CT_DUPLICATE:
        // Auto-generate new CT if expired
        await this.autoGenerateNewCT(workflow);
        break;
        
      case WorkflowType.QC_REJECTION:
        // Escalate to higher management
        await this.escalateToManagement(workflow);
        break;
    }
  }
  
  private async trackActiveWorkflow(instance: WorkflowInstance): Promise<void> {
    const { data, error } = await supabase
      .from('active_workflows')
      .insert({
        workflow_id: instance.id,
        workflow_type: instance.workflow_type,
        initiated_by: instance.initiated_by,
        expires_at: instance.expires_at,
        metadata: {
          context: instance.context_data,
          approvers: await this.getApproversForType(instance.workflow_type)
        }
      });
      
    if (error) {
      console.error('Failed to track workflow:', error);
    }
  }
}
```

### 4. Database Schema

```sql
-- Main workflow table
CREATE TABLE whatsapp_approval_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_type TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN (
    'pending', 'approved', 'rejected', 'expired', 'cancelled'
  )),
  context_data JSONB NOT NULL,
  initiated_by UUID REFERENCES auth.users(id),
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  response_data JSONB,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  priority TEXT DEFAULT 'normal',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow events log
CREATE TABLE workflow_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID REFERENCES whatsapp_approval_workflows(id),
  event_type TEXT NOT NULL,
  event_data JSONB,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow responses
CREATE TABLE workflow_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID REFERENCES whatsapp_approval_workflows(id),
  responder_id UUID REFERENCES auth.users(id),
  response_type TEXT NOT NULL,
  response_data JSONB,
  responded_via TEXT DEFAULT 'whatsapp',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_workflows_status ON whatsapp_approval_workflows(status);
CREATE INDEX idx_workflows_type ON whatsapp_approval_workflows(workflow_type);
CREATE INDEX idx_workflows_expires ON whatsapp_approval_workflows(expires_at);
CREATE INDEX idx_workflow_events_workflow ON workflow_events(workflow_id);
```

## UI Integration

### Approval Dashboard

```tsx
const ApprovalDashboard: React.FC = () => {
  const { data: pendingApprovals } = useQuery({
    queryKey: ['pending-approvals'],
    queryFn: fetchPendingApprovals,
    refetchInterval: 30000 // Refresh every 30 seconds
  });
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Pending Approvals"
          value={pendingApprovals?.length || 0}
          icon={Clock}
          trend={pendingApprovals?.urgent || 0}
          trendLabel="Urgent"
        />
      </div>
      
      <Tabs defaultValue="pending">
        <TabsList>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
          <TabsTrigger value="expired">Expired</TabsTrigger>
        </TabsList>
        
        <TabsContent value="pending">
          <PendingApprovalsList approvals={pendingApprovals} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

### Workflow Initiation Hook

```typescript
export const useApprovalWorkflow = () => {
  const queryClient = useQueryClient();
  
  const initiateCTDuplicateApproval = useMutation({
    mutationFn: async (params: CTDuplicateParams) => {
      const manager = new ApprovalWorkflowManager();
      return manager.initiateWorkflow(
        WorkflowType.CT_DUPLICATE,
        params,
        getCurrentUser()
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['pending-approvals']);
      toast.success('Approval request sent via WhatsApp');
    }
  });
  
  const initiateQCRejection = useMutation({
    mutationFn: async (params: QCRejectionParams) => {
      const manager = new ApprovalWorkflowManager();
      return manager.initiateWorkflow(
        WorkflowType.QC_REJECTION,
        params,
        getCurrentUser()
      );
    },
    onSuccess: () => {
      toast.error('QC Rejection approval request sent urgently');
    }
  });
  
  return {
    initiateCTDuplicateApproval,
    initiateQCRejection,
    // ... other workflows
  };
};
```

## Performance Optimization

### Workflow Caching

```typescript
class WorkflowCache {
  private cache = new Map<string, CachedWorkflow>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes
  
  get(workflowId: string): WorkflowInstance | null {
    const cached = this.cache.get(workflowId);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(workflowId);
      return null;
    }
    
    return cached.workflow;
  }
  
  set(workflow: WorkflowInstance): void {
    this.cache.set(workflow.id, {
      workflow,
      timestamp: Date.now()
    });
  }
}
```

### Batch Approval Processing

```typescript
const processBatchApprovals = async (
  workflowIds: string[],
  action: 'approve' | 'reject',
  approverId: string
): Promise<BatchResult> => {
  const results = await Promise.allSettled(
    workflowIds.map(id => 
      processApproval(id, action, approverId)
    )
  );
  
  return {
    successful: results.filter(r => r.status === 'fulfilled').length,
    failed: results.filter(r => r.status === 'rejected').length,
    details: results
  };
};
```

## Security & Compliance

### Approval Verification

```typescript
const verifyApprovalAuthority = async (
  userId: string,
  workflowType: WorkflowType
): Promise<boolean> => {
  const user = await getUserWithRole(userId);
  const config = WORKFLOW_CONFIG[workflowType];
  
  return config.approvers.includes(user.role);
};
```

### Audit Trail

```typescript
const createAuditEntry = async (
  workflow: WorkflowInstance,
  action: string,
  userId: string,
  metadata?: any
): Promise<void> => {
  await supabase.from('workflow_audit_log').insert({
    workflow_id: workflow.id,
    workflow_type: workflow.workflow_type,
    action,
    user_id: userId,
    metadata,
    ip_address: getClientIP(),
    user_agent: getUserAgent(),
    created_at: new Date()
  });
};
```

---
**Status**: Backend 90% Complete | Frontend 95% Complete
**Integration**: WhatsApp messaging, Order management, QC system
**Configuration Required**: WhatsApp provider setup, role assignments