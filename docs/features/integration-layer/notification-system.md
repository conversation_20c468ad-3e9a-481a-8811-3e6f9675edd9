# Multi-Channel Notification System

## Overview

The notification system provides a unified interface for delivering messages across multiple channels (WhatsApp, Email, SMS, In-App) with intelligent routing, fallback mechanisms, and delivery tracking.

## Architecture

### Notification Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                      Notification System                         │
├─────────────────────────────────────────────────────────────────┤
│                                                                  │
│  Trigger → Router → Channel Selection → Delivery → Tracking     │
│                                                                  │
├─────────────────────────────────────────────────────────────────┤
│  Channels:                                                       │
│  ┌─────────┐ ┌───────┐ ┌─────┐ ┌───────┐ ┌─────────────┐ │
│  │WhatsApp │ │ Email │ │ SMS │ │In-App │ │Push (Future)│ │
│  └─────────┘ └───────┘ └─────┘ └───────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Notification Types

```typescript
enum NotificationType {
  // Order Management
  ORDER_STATUS_UPDATE = 'order_status_update',
  ORDER_ASSIGNMENT = 'order_assignment',
  ORDER_COMPLETION = 'order_completion',
  
  // CT System
  CT_ASSIGNMENT_COMPLETE = 'ct_assignment_complete',
  CT_DUPLICATE_DETECTED = 'ct_duplicate_detected',
  
  // Quality Control
  QC_INSPECTION_REQUIRED = 'qc_inspection_required',
  QC_REJECTION = 'qc_rejection',
  QC_APPROVAL = 'qc_approval',
  
  // Approvals
  APPROVAL_REQUEST = 'approval_request',
  APPROVAL_REMINDER = 'approval_reminder',
  APPROVAL_RESULT = 'approval_result',
  
  // System
  SYSTEM_ALERT = 'system_alert',
  MAINTENANCE_NOTICE = 'maintenance_notice',
  NEW_DEVICE_LOGIN = 'new_device_login'
}

interface NotificationPriority {
  URGENT = 'urgent';     // Immediate delivery, all channels
  HIGH = 'high';         // Quick delivery, primary channel
  NORMAL = 'normal';     // Standard delivery
  LOW = 'low';           // Batch delivery allowed
}
```

## Core Implementation

### 1. Notification Manager

**File**: `/src/services/notifications/NotificationManager.ts`

```typescript
export class NotificationManager {
  private channels: Map<string, NotificationChannel>;
  private router: NotificationRouter;
  private queue: NotificationQueue;
  
  constructor() {
    this.channels = new Map([
      ['whatsapp', new WhatsAppChannel()],
      ['email', new EmailChannel()],
      ['sms', new SMSChannel()],
      ['in-app', new InAppChannel()]
    ]);
    
    this.router = new NotificationRouter();
    this.queue = new NotificationQueue();
  }
  
  async send(
    notification: NotificationRequest
  ): Promise<NotificationResult> {
    try {
      // Validate notification
      this.validateNotification(notification);
      
      // Determine channels based on type and user preferences
      const channels = await this.router.determineChannels(
        notification.type,
        notification.recipient,
        notification.priority
      );
      
      // Queue notification
      const job = await this.queue.add({
        notification,
        channels,
        attempts: 0,
        created_at: new Date()
      });
      
      // Process based on priority
      if (notification.priority === 'urgent') {
        return await this.processImmediate(job);
      } else {
        return await this.processQueued(job);
      }
      
    } catch (error) {
      console.error('Notification failed:', error);
      throw error;
    }
  }
  
  private async processImmediate(
    job: NotificationJob
  ): Promise<NotificationResult> {
    const results = [];
    
    // Try all channels in parallel for urgent
    const promises = job.channels.map(channelName => 
      this.sendViaChannel(channelName, job.notification)
    );
    
    const channelResults = await Promise.allSettled(promises);
    
    // Check if at least one succeeded
    const successful = channelResults.some(
      r => r.status === 'fulfilled' && r.value.success
    );
    
    if (!successful) {
      // All channels failed, try fallback
      await this.handleFailure(job);
    }
    
    return {
      success: successful,
      jobId: job.id,
      channels: this.formatResults(channelResults)
    };
  }
  
  private async sendViaChannel(
    channelName: string,
    notification: NotificationRequest
  ): Promise<ChannelResult> {
    const channel = this.channels.get(channelName);
    if (!channel) {
      throw new Error(`Unknown channel: ${channelName}`);
    }
    
    try {
      const result = await channel.send(notification);
      
      // Track delivery
      await this.trackDelivery({
        notification_id: notification.id,
        channel: channelName,
        status: result.status,
        delivered_at: result.deliveredAt,
        metadata: result.metadata
      });
      
      return result;
      
    } catch (error) {
      // Log channel failure
      await this.logChannelError(channelName, notification, error);
      throw error;
    }
  }
}
```

### 2. Notification Router

**File**: `/src/services/notifications/NotificationRouter.ts`

```typescript
export class NotificationRouter {
  private routingRules: Map<NotificationType, RoutingRule>;
  
  constructor() {
    this.routingRules = new Map([
      [NotificationType.ORDER_STATUS_UPDATE, {
        channels: ['whatsapp', 'in-app'],
        fallback: ['email'],
        conditions: []
      }],
      [NotificationType.QC_REJECTION, {
        channels: ['whatsapp', 'email'],
        fallback: ['sms'],
        conditions: [{
          field: 'severity',
          operator: 'equals',
          value: 'critical',
          action: 'add_channel',
          channel: 'sms'
        }]
      }],
      [NotificationType.APPROVAL_REQUEST, {
        channels: ['whatsapp'],
        fallback: ['email', 'sms'],
        conditions: [{
          field: 'workflow_type',
          operator: 'equals',
          value: 'urgent',
          action: 'use_all_channels'
        }]
      }]
    ]);
  }
  
  async determineChannels(
    type: NotificationType,
    recipient: NotificationRecipient,
    priority: NotificationPriority
  ): Promise<string[]> {
    // Get base routing rule
    const rule = this.routingRules.get(type);
    if (!rule) {
      return ['in-app']; // Default fallback
    }
    
    // Start with default channels
    let channels = [...rule.channels];
    
    // Apply user preferences
    const preferences = await this.getUserPreferences(recipient.userId);
    channels = this.applyPreferences(channels, preferences);
    
    // Apply conditions
    channels = this.applyConditions(channels, rule.conditions, recipient);
    
    // Apply priority overrides
    if (priority === 'urgent') {
      // Add all available channels for urgent
      channels = this.getAllAvailableChannels(recipient);
    }
    
    // Ensure at least one channel
    if (channels.length === 0) {
      channels = rule.fallback || ['in-app'];
    }
    
    return channels;
  }
  
  private async getUserPreferences(
    userId: string
  ): Promise<UserNotificationPreferences> {
    const { data } = await supabase
      .from('user_notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();
      
    return data || this.getDefaultPreferences();
  }
  
  private applyPreferences(
    channels: string[],
    preferences: UserNotificationPreferences
  ): string[] {
    // Remove disabled channels
    channels = channels.filter(ch => 
      preferences.enabled_channels.includes(ch)
    );
    
    // Apply quiet hours
    if (this.isQuietHours(preferences)) {
      channels = channels.filter(ch => 
        preferences.quiet_hours_channels.includes(ch)
      );
    }
    
    return channels;
  }
}
```

### 3. Channel Implementations

#### WhatsApp Channel

```typescript
export class WhatsAppChannel implements NotificationChannel {
  private provider: WhatsAppProviderManager;
  
  async send(
    notification: NotificationRequest
  ): Promise<ChannelResult> {
    try {
      // Format message based on notification type
      const message = this.formatMessage(notification);
      
      // Select provider
      const provider = await this.provider.selectProvider(
        notification.priority === 'urgent' ? 'approval' : 'notification'
      );
      
      // Send message
      const result = await provider.sendMessage({
        to: notification.recipient.phone_number,
        ...message
      });
      
      return {
        success: true,
        channel: 'whatsapp',
        messageId: result.messageId,
        deliveredAt: new Date(),
        status: 'sent',
        metadata: {
          provider: provider.name,
          cost: result.cost
        }
      };
      
    } catch (error) {
      return {
        success: false,
        channel: 'whatsapp',
        error: error.message,
        status: 'failed'
      };
    }
  }
  
  private formatMessage(
    notification: NotificationRequest
  ): WhatsAppMessage {
    const template = this.getTemplate(notification.type);
    
    return {
      type: notification.data.interactive ? 'interactive' : 'text',
      header: template.header,
      body: this.interpolate(template.body, notification.data),
      footer: template.footer,
      buttons: notification.data.buttons || []
    };
  }
}
```

#### Email Channel

```typescript
export class EmailChannel implements NotificationChannel {
  private transporter: EmailTransporter;
  
  async send(
    notification: NotificationRequest
  ): Promise<ChannelResult> {
    try {
      const email = this.buildEmail(notification);
      
      const result = await this.transporter.send({
        to: notification.recipient.email,
        subject: email.subject,
        html: email.html,
        text: email.text,
        attachments: email.attachments
      });
      
      return {
        success: true,
        channel: 'email',
        messageId: result.messageId,
        deliveredAt: new Date(),
        status: 'sent'
      };
      
    } catch (error) {
      return {
        success: false,
        channel: 'email',
        error: error.message,
        status: 'failed'
      };
    }
  }
  
  private buildEmail(
    notification: NotificationRequest
  ): EmailContent {
    const template = this.getEmailTemplate(notification.type);
    
    return {
      subject: this.interpolate(template.subject, notification.data),
      html: this.renderHTML(template.html, notification.data),
      text: this.renderText(template.text, notification.data),
      attachments: this.prepareAttachments(notification.data.attachments)
    };
  }
}
```

#### In-App Channel

```typescript
export class InAppChannel implements NotificationChannel {
  async send(
    notification: NotificationRequest
  ): Promise<ChannelResult> {
    try {
      // Store in database
      const { data, error } = await supabase
        .from('in_app_notifications')
        .insert({
          user_id: notification.recipient.userId,
          type: notification.type,
          title: notification.data.title,
          message: notification.data.message,
          data: notification.data,
          priority: notification.priority,
          read: false,
          action_url: notification.data.actionUrl,
          expires_at: this.calculateExpiry(notification)
        })
        .select()
        .single();
        
      if (error) throw error;
      
      // Broadcast via WebSocket for real-time updates
      await this.broadcastNotification(data);
      
      return {
        success: true,
        channel: 'in-app',
        messageId: data.id,
        deliveredAt: new Date(),
        status: 'delivered'
      };
      
    } catch (error) {
      return {
        success: false,
        channel: 'in-app',
        error: error.message,
        status: 'failed'
      };
    }
  }
  
  private async broadcastNotification(
    notification: InAppNotification
  ): Promise<void> {
    // Send via Supabase Realtime
    const channel = supabase
      .channel(`user:${notification.user_id}`)
      .send({
        type: 'broadcast',
        event: 'new_notification',
        payload: notification
      });
  }
}
```

### 4. Notification Queue

```typescript
export class NotificationQueue {
  private queue: Queue;
  
  constructor() {
    this.queue = new Queue('notifications', {
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        },
        removeOnComplete: true,
        removeOnFail: false
      }
    });
    
    this.setupWorkers();
  }
  
  async add(
    job: NotificationJob,
    options?: JobOptions
  ): Promise<Job> {
    const priority = this.getPriority(job.notification.priority);
    
    return await this.queue.add('send', job, {
      priority,
      delay: options?.delay || 0,
      ...options
    });
  }
  
  private setupWorkers(): void {
    // Process notifications
    this.queue.process('send', async (job) => {
      const { notification, channels } = job.data;
      
      // Send to all channels
      const results = await Promise.allSettled(
        channels.map(ch => this.sendToChannel(ch, notification))
      );
      
      // Update job progress
      job.progress(100);
      
      return { results };
    });
    
    // Handle failures
    this.queue.on('failed', async (job, err) => {
      console.error(`Notification job ${job.id} failed:`, err);
      
      // Retry logic
      if (job.attemptsMade < job.opts.attempts) {
        // Will retry automatically
      } else {
        // Final failure, use emergency fallback
        await this.emergencyFallback(job.data);
      }
    });
  }
}
```

### 5. Database Schema

```sql
-- User notification preferences
CREATE TABLE user_notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  enabled_channels TEXT[] DEFAULT ARRAY['whatsapp', 'email', 'in-app'],
  quiet_hours_enabled BOOLEAN DEFAULT false,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  quiet_hours_channels TEXT[] DEFAULT ARRAY['in-app'],
  notification_types JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification log
CREATE TABLE notification_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL,
  recipient_id UUID REFERENCES auth.users(id),
  channels TEXT[],
  priority TEXT,
  status TEXT,
  data JSONB,
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  error TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- In-app notifications
CREATE TABLE in_app_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority TEXT DEFAULT 'normal',
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- Channel delivery tracking
CREATE TABLE notification_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID,
  channel TEXT NOT NULL,
  status TEXT NOT NULL,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## UI Components

### Notification Center

```tsx
const NotificationCenter: React.FC = () => {
  const { data: notifications, unreadCount } = useNotifications();
  const [filter, setFilter] = useState<'all' | 'unread'>('unread');
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 h-5 w-5 p-0"
              variant="destructive"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold">Notifications</h4>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => markAllAsRead()}
            >
              Mark all read
            </Button>
          </div>
          
          <Tabs value={filter} onValueChange={setFilter}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="unread">Unread</TabsTrigger>
              <TabsTrigger value="all">All</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <ScrollArea className="h-[400px]">
            <div className="space-y-2">
              {notifications?.map((notification) => (
                <NotificationItem 
                  key={notification.id}
                  notification={notification}
                  onRead={() => markAsRead(notification.id)}
                />
              ))}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
};
```

### Notification Preferences

```tsx
const NotificationPreferences: React.FC = () => {
  const { data: preferences, update } = useNotificationPreferences();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>
          Choose how you want to receive notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Channel Selection */}
        <div>
          <Label>Notification Channels</Label>
          <div className="space-y-2 mt-2">
            {['whatsapp', 'email', 'sms', 'in-app'].map(channel => (
              <div key={channel} className="flex items-center space-x-2">
                <Checkbox
                  checked={preferences?.enabled_channels.includes(channel)}
                  onCheckedChange={(checked) => 
                    updateChannel(channel, checked)
                  }
                />
                <Label className="capitalize">{channel}</Label>
              </div>
            ))}
          </div>
        </div>
        
        {/* Quiet Hours */}
        <div>
          <div className="flex items-center space-x-2">
            <Switch
              checked={preferences?.quiet_hours_enabled}
              onCheckedChange={(checked) => 
                update({ quiet_hours_enabled: checked })
              }
            />
            <Label>Enable Quiet Hours</Label>
          </div>
          
          {preferences?.quiet_hours_enabled && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <Label>Start Time</Label>
                <Input
                  type="time"
                  value={preferences.quiet_hours_start}
                  onChange={(e) => 
                    update({ quiet_hours_start: e.target.value })
                  }
                />
              </div>
              <div>
                <Label>End Time</Label>
                <Input
                  type="time"
                  value={preferences.quiet_hours_end}
                  onChange={(e) => 
                    update({ quiet_hours_end: e.target.value })
                  }
                />
              </div>
            </div>
          )}
        </div>
        
        {/* Notification Types */}
        <div>
          <Label>Notification Types</Label>
          <div className="space-y-4 mt-4">
            {Object.entries(NOTIFICATION_TYPES).map(([key, config]) => (
              <NotificationTypePreference
                key={key}
                type={key}
                config={config}
                preferences={preferences?.notification_types[key]}
                onUpdate={(prefs) => updateType(key, prefs)}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
```

## Integration Hooks

### useNotifications Hook

```typescript
export const useNotifications = () => {
  const userId = useCurrentUser()?.id;
  
  // Real-time subscription
  useEffect(() => {
    if (!userId) return;
    
    const channel = supabase
      .channel(`notifications:${userId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'in_app_notifications',
        filter: `user_id=eq.${userId}`
      }, (payload) => {
        // Show toast for new notification
        toast({
          title: payload.new.title,
          description: payload.new.message,
          action: payload.new.action_url ? (
            <ToastAction
              altText="View"
              onClick={() => navigate(payload.new.action_url)}
            >
              View
            </ToastAction>
          ) : undefined
        });
        
        // Invalidate cache
        queryClient.invalidateQueries(['notifications']);
      })
      .subscribe();
      
    return () => {
      channel.unsubscribe();
    };
  }, [userId]);
  
  // Fetch notifications
  const { data: notifications } = useQuery({
    queryKey: ['notifications', userId],
    queryFn: () => fetchNotifications(userId),
    refetchInterval: 60000 // Refresh every minute
  });
  
  // Calculate unread count
  const unreadCount = notifications?.filter(n => !n.read).length || 0;
  
  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification
  };
};
```

## Performance & Reliability

### Batch Processing

```typescript
class NotificationBatcher {
  private batch: Map<string, NotificationRequest[]> = new Map();
  private timer: NodeJS.Timeout;
  
  add(notification: NotificationRequest): void {
    const key = `${notification.type}_${notification.priority}`;
    
    if (!this.batch.has(key)) {
      this.batch.set(key, []);
    }
    
    this.batch.get(key)!.push(notification);
    
    // Reset timer
    clearTimeout(this.timer);
    this.timer = setTimeout(() => this.flush(), 5000);
  }
  
  private async flush(): Promise<void> {
    for (const [key, notifications] of this.batch.entries()) {
      if (notifications.length > 10) {
        // Batch send for efficiency
        await this.sendBatch(notifications);
      } else {
        // Send individually
        await Promise.all(
          notifications.map(n => this.sendSingle(n))
        );
      }
    }
    
    this.batch.clear();
  }
}
```

### Circuit Breaker

```typescript
class ChannelCircuitBreaker {
  private failures = new Map<string, number>();
  private lastFailure = new Map<string, Date>();
  private state = new Map<string, 'closed' | 'open' | 'half-open'>();
  
  async execute(
    channel: string,
    operation: () => Promise<any>
  ): Promise<any> {
    const currentState = this.state.get(channel) || 'closed';
    
    if (currentState === 'open') {
      if (this.shouldAttemptReset(channel)) {
        this.state.set(channel, 'half-open');
      } else {
        throw new Error(`Channel ${channel} is unavailable`);
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess(channel);
      return result;
    } catch (error) {
      this.onFailure(channel);
      throw error;
    }
  }
  
  private onFailure(channel: string): void {
    const failures = (this.failures.get(channel) || 0) + 1;
    this.failures.set(channel, failures);
    this.lastFailure.set(channel, new Date());
    
    if (failures >= 5) {
      this.state.set(channel, 'open');
      console.error(`Circuit breaker opened for ${channel}`);
    }
  }
  
  private onSuccess(channel: string): void {
    this.failures.delete(channel);
    this.state.set(channel, 'closed');
  }
}
```

---
**Status**: Core Complete | Channels Vary
**WhatsApp**: 95% Complete
**Email**: 70% Complete (SMTP config needed)
**SMS**: 0% Complete (Provider needed)
**In-App**: 100% Complete