# Label Designer System

## Overview

The Label Designer provides a professional WYSIWYG (What You See Is What You Get) interface for creating custom label templates using Fabric.js canvas technology. It supports comprehensive design tools, dynamic data fields, and seamless integration with the printing ecosystem.

## Technical Architecture

### Core Component Structure

**Primary File**: `/src/components/labelDesigner/LabelDesigner.tsx`

```typescript
interface LabelDesignerProps {
  initialCanvasData?: any;
  labelSize?: LabelSize;
  onSave?: (canvasData: any, labelSize: LabelSize) => void;
  isEmbedded?: boolean;
  showSaveButton?: boolean;
  showTestPrint?: boolean;
}

interface LabelSize {
  width: number;
  height: number;
  unit: 'mm' | 'inch';
}
```

### Fabric.js Integration

**Version**: Fabric.js v6.7.0

**Canvas Initialization**:
```typescript
const canvas = new fabric.Canvas('label-canvas', {
  width: pixelWidth,
  height: pixelHeight,
  backgroundColor: 'white',
  selection: true,
  preserveObjectStacking: true
});

// Grid system for alignment
const gridSize = 10;
canvas.on('object:moving', (e) => {
  const obj = e.target;
  obj.set({
    left: Math.round(obj.left / gridSize) * gridSize,
    top: Math.round(obj.top / gridSize) * gridSize
  });
});
```

## Design Tools Implementation

### 1. Text Tool

**Features**:
- Font family selection (10+ fonts)
- Font size (8-72pt)
- Font weight (normal, bold)
- Text alignment (left, center, right)
- Color picker
- Dynamic field placeholders

**Implementation**:
```typescript
const addText = () => {
  const text = new fabric.Textbox('Sample Text', {
    left: canvas.width / 2,
    top: canvas.height / 2,
    fontSize: 20,
    fontFamily: 'Arial',
    fill: '#000000',
    width: 200,
    textAlign: 'left',
    originX: 'center',
    originY: 'center'
  });
  canvas.add(text);
  canvas.setActiveObject(text);
  canvas.renderAll();
};
```

**Dynamic Fields**:
```typescript
const dynamicFields = [
  '{ORDER_ID}',
  '{CUSTOMER_NAME}',
  '{PART_NUMBER}',
  '{CT_NUMBER}',
  '{QUANTITY}',
  '{DATE}',
  '{LOCATION}',
  '{USER_NAME}'
];
```

### 2. Shape Tools

**Rectangle Tool**:
```typescript
const addRectangle = () => {
  const rect = new fabric.Rect({
    left: canvas.width / 2,
    top: canvas.height / 2,
    width: 100,
    height: 60,
    fill: 'transparent',
    stroke: '#000000',
    strokeWidth: 2,
    originX: 'center',
    originY: 'center'
  });
  canvas.add(rect);
};
```

**Circle Tool**:
```typescript
const addCircle = () => {
  const circle = new fabric.Circle({
    left: canvas.width / 2,
    top: canvas.height / 2,
    radius: 30,
    fill: 'transparent',
    stroke: '#000000',
    strokeWidth: 2,
    originX: 'center',
    originY: 'center'
  });
  canvas.add(circle);
};
```

**Line Tool**:
```typescript
const addLine = () => {
  const line = new fabric.Line([50, 50, 200, 50], {
    stroke: '#000000',
    strokeWidth: 2,
    selectable: true,
    hasControls: true
  });
  canvas.add(line);
};
```

### 3. Barcode Integration

**Code 128 Barcode**:
```typescript
import JsBarcode from 'jsbarcode';

const addBarcode = (data: string, format: string = 'CODE128') => {
  const canvas = document.createElement('canvas');
  JsBarcode(canvas, data, {
    format: format,
    width: 2,
    height: 50,
    displayValue: true
  });
  
  fabric.Image.fromURL(canvas.toDataURL(), (img) => {
    img.set({
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: 'center',
      originY: 'center',
      data: data,
      barcodeFormat: format
    });
    canvas.add(img);
  });
};
```

**QR Code**:
```typescript
import QRCode from 'qrcode';

const addQRCode = async (data: string) => {
  const url = await QRCode.toDataURL(data, {
    width: 100,
    margin: 1
  });
  
  fabric.Image.fromURL(url, (img) => {
    img.set({
      left: canvas.width / 2,
      top: canvas.height / 2,
      originX: 'center',
      originY: 'center',
      qrData: data
    });
    canvas.add(img);
  });
};
```

### 4. Image Support

```typescript
const addImage = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    fabric.Image.fromURL(e.target.result, (img) => {
      const scale = Math.min(
        canvas.width * 0.5 / img.width,
        canvas.height * 0.5 / img.height
      );
      img.scale(scale);
      img.set({
        left: canvas.width / 2,
        top: canvas.height / 2,
        originX: 'center',
        originY: 'center'
      });
      canvas.add(img);
    });
  };
  reader.readAsDataURL(file);
};
```

## Advanced Features

### 1. Properties Panel

**Real-time Property Updates**:
```typescript
const updateObjectProperties = (property: string, value: any) => {
  const activeObject = canvas.getActiveObject();
  if (activeObject) {
    activeObject.set(property, value);
    canvas.renderAll();
    saveToHistory();
  }
};

// Two-way binding example
<Input
  type="number"
  value={activeObject?.left || 0}
  onChange={(e) => updateObjectProperties('left', parseInt(e.target.value))}
  onKeyDown={(e) => e.stopPropagation()}
/>
```

### 2. Undo/Redo System

```typescript
const [history, setHistory] = useState<string[]>([]);
const [currentStep, setCurrentStep] = useState<number>(-1);

const saveToHistory = () => {
  const canvasData = JSON.stringify(canvas.toJSON());
  const newHistory = history.slice(0, currentStep + 1);
  newHistory.push(canvasData);
  setHistory(newHistory);
  setCurrentStep(newHistory.length - 1);
};

const undo = () => {
  if (currentStep > 0) {
    const step = currentStep - 1;
    canvas.loadFromJSON(history[step], () => {
      canvas.renderAll();
      setCurrentStep(step);
    });
  }
};

const redo = () => {
  if (currentStep < history.length - 1) {
    const step = currentStep + 1;
    canvas.loadFromJSON(history[step], () => {
      canvas.renderAll();
      setCurrentStep(step);
    });
  }
};
```

### 3. Grid System

```typescript
const [showGrid, setShowGrid] = useState(true);
const gridSize = 10;

const drawGrid = () => {
  if (!showGrid) return;
  
  const gridCanvas = document.createElement('canvas');
  gridCanvas.width = canvas.width;
  gridCanvas.height = canvas.height;
  const ctx = gridCanvas.getContext('2d');
  
  ctx.strokeStyle = '#e0e0e0';
  ctx.lineWidth = 0.5;
  
  for (let i = 0; i < canvas.width; i += gridSize) {
    ctx.beginPath();
    ctx.moveTo(i, 0);
    ctx.lineTo(i, canvas.height);
    ctx.stroke();
  }
  
  for (let i = 0; i < canvas.height; i += gridSize) {
    ctx.beginPath();
    ctx.moveTo(0, i);
    ctx.lineTo(canvas.width, i);
    ctx.stroke();
  }
  
  canvas.setBackgroundImage(gridCanvas.toDataURL(), canvas.renderAll.bind(canvas));
};
```

### 4. Zoom Controls

```typescript
const [zoomLevel, setZoomLevel] = useState(100);

const handleZoom = (newZoom: number) => {
  const zoom = newZoom / 100;
  canvas.setZoom(zoom);
  canvas.setDimensions({
    width: originalWidth * zoom,
    height: originalHeight * zoom
  });
  setZoomLevel(newZoom);
};

// Zoom controls UI
<div className="flex items-center gap-2">
  <Button 
    size="sm" 
    variant="outline"
    onClick={() => handleZoom(Math.max(10, zoomLevel - 10))}
  >
    <ZoomOut className="h-4 w-4" />
  </Button>
  <span className="w-16 text-center">{zoomLevel}%</span>
  <Button 
    size="sm" 
    variant="outline"
    onClick={() => handleZoom(Math.min(300, zoomLevel + 10))}
  >
    <ZoomIn className="h-4 w-4" />
  </Button>
</div>
```

## Toolbar Implementation

### Enhanced Toolbar Features

```typescript
interface ToolbarState {
  selectedTool: 'select' | 'text' | 'rectangle' | 'circle' | 'line' | 'barcode' | 'qr';
  strokeWidth: number; // 1-20
  strokeColor: string;
  fillColor: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: 'normal' | 'bold';
  textAlign: 'left' | 'center' | 'right';
}

// Toolbar component structure
<div className="toolbar">
  {/* Tool Selection */}
  <ToggleGroup value={selectedTool} onValueChange={setSelectedTool}>
    <ToggleGroupItem value="select"><MousePointer2 /></ToggleGroupItem>
    <ToggleGroupItem value="text"><Type /></ToggleGroupItem>
    <ToggleGroupItem value="rectangle"><Square /></ToggleGroupItem>
    <ToggleGroupItem value="circle"><Circle /></ToggleGroupItem>
    <ToggleGroupItem value="line"><Minus /></ToggleGroupItem>
    <ToggleGroupItem value="barcode"><Barcode /></ToggleGroupItem>
    <ToggleGroupItem value="qr"><QrCode /></ToggleGroupItem>
  </ToggleGroup>
  
  {/* Style Controls */}
  <div className="style-controls">
    <Select value={strokeWidth} onValueChange={setStrokeWidth}>
      {[1,2,3,4,5,8,10,15,20].map(w => 
        <SelectItem value={w}>{w}px</SelectItem>
      )}
    </Select>
    
    <ColorPicker value={strokeColor} onChange={setStrokeColor} />
    <ColorPicker value={fillColor} onChange={setFillColor} />
  </div>
  
  {/* Text Controls */}
  {selectedTool === 'text' && (
    <div className="text-controls">
      <Select value={fontFamily} onValueChange={setFontFamily}>
        <SelectItem value="Arial">Arial</SelectItem>
        <SelectItem value="Helvetica">Helvetica</SelectItem>
        <SelectItem value="Times New Roman">Times</SelectItem>
        <SelectItem value="Courier">Courier</SelectItem>
      </Select>
      
      <Input 
        type="number" 
        value={fontSize} 
        onChange={(e) => setFontSize(e.target.value)}
        min="8" 
        max="72"
      />
    </div>
  )}
</div>
```

## Canvas Data Management

### Saving Canvas State

```typescript
const saveCanvasData = () => {
  const canvasJSON = canvas.toJSON([
    'data',
    'barcodeFormat',
    'qrData',
    'dynamicField'
  ]);
  
  const canvasData = {
    version: '1.0',
    canvas: canvasJSON,
    metadata: {
      created: new Date().toISOString(),
      labelSize: currentLabelSize,
      author: user?.email
    }
  };
  
  return canvasData;
};
```

### Loading Canvas State

```typescript
const loadCanvasData = (data: any) => {
  if (data?.canvas) {
    canvas.loadFromJSON(data.canvas, () => {
      canvas.renderAll();
      // Restore custom properties
      canvas.getObjects().forEach(obj => {
        if (obj.dynamicField) {
          // Mark as dynamic field
          obj.set('fill', '#0066cc');
        }
      });
    });
  }
};
```

## Integration with Template System

### Template Save Flow

```typescript
const saveAsTemplate = async () => {
  const canvasData = saveCanvasData();
  
  const template = {
    name: templateName,
    description: templateDescription,
    category: selectedCategory,
    template_type: templateType,
    label_size: currentLabelSize,
    canvas_data: canvasData,
    zpl_template: null, // Would be generated by backend
    is_active: true
  };
  
  await createLabelTemplate(template);
};
```

### Template Load Flow

```typescript
const loadTemplate = async (templateId: string) => {
  const template = await getLabelTemplate(templateId);
  if (template?.canvas_data) {
    setCurrentLabelSize(template.label_size);
    updateCanvasSize(template.label_size);
    loadCanvasData(template.canvas_data);
  }
};
```

## Canvas to ZPL Conversion [Partially Integrated]

### ZPL Generator Class

**File**: `/src/utils/zplGenerator.ts`

```typescript
export class ZPLGenerator {
  private dpi: number;
  private width: number;
  private height: number;
  
  constructor(labelSize: LabelSize, dpi: number = 203) {
    this.dpi = dpi;
    this.width = this.mmToPixels(labelSize.width);
    this.height = this.mmToPixels(labelSize.height);
  }
  
  generateFromCanvas(canvasData: any, dynamicData: any = {}): string {
    let zpl = '^XA\n';
    zpl += `^PW${this.width}\n`;
    
    const objects = canvasData.objects || [];
    objects.forEach(obj => {
      switch (obj.type) {
        case 'text':
        case 'textbox':
          zpl += this.generateTextZPL(obj, dynamicData);
          break;
        case 'rect':
          zpl += this.generateRectangleZPL(obj);
          break;
        case 'circle':
          zpl += this.generateCircleZPL(obj);
          break;
        case 'line':
          zpl += this.generateLineZPL(obj);
          break;
        case 'image':
          if (obj.barcodeFormat) {
            zpl += this.generateBarcodeZPL(obj, dynamicData);
          } else if (obj.qrData) {
            zpl += this.generateQRCodeZPL(obj, dynamicData);
          }
          break;
      }
    });
    
    zpl += '^XZ';
    return zpl;
  }
}
```

**Integration Gap**: The ZPL generator exists but is not fully connected to the label designer save flow. Current implementation falls back to predefined templates.

## Testing & Preview

### Labelary Preview Integration

```typescript
const previewLabel = async () => {
  const zpl = generateZPLFromCanvas();
  const labelSize = currentLabelSize;
  
  const url = `http://api.labelary.com/v1/printers/${dpi}dpi/labels/${labelSize.width}x${labelSize.height}/0/`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: zpl
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const imageUrl = URL.createObjectURL(blob);
    setPreviewImage(imageUrl);
    setShowPreviewModal(true);
  }
};
```

### Test Print Integration

```typescript
const handleTestPrint = async () => {
  const { printZebraLabel, useZebraPrinters } = useMCPPrinting();
  const printers = useZebraPrinters();
  
  if (selectedPrinter && zplContent) {
    const result = await printZebraLabel(
      selectedPrinter,
      zplContent,
      { purpose: 'test_print', template_id: currentTemplateId }
    );
    
    if (result.success) {
      toast.success('Test print sent successfully');
    }
  }
};
```

## Performance Optimizations

### Canvas Rendering
```typescript
// Debounced canvas updates
const debouncedRender = useMemo(
  () => debounce(() => canvas.renderAll(), 16),
  [canvas]
);

// Object caching
canvas.getObjects().forEach(obj => {
  obj.set({
    objectCaching: true,
    statefullCache: true
  });
});
```

### Memory Management
```typescript
// Cleanup on unmount
useEffect(() => {
  return () => {
    canvas.dispose();
    history.forEach(step => URL.revokeObjectURL(step));
  };
}, []);
```

## Known Limitations

1. **Canvas-to-ZPL Gap**: Visual designs not automatically converted to production ZPL
2. **Limited Font Support**: ZPL supports fewer fonts than canvas
3. **Complex Shapes**: Some canvas shapes don't have direct ZPL equivalents
4. **Image Handling**: Images must be converted to ZPL graphics format

## Future Enhancements

1. **Complete ZPL Integration**: Connect canvas output to ZPL generator
2. **Advanced Templates**: Conditional logic and data validation
3. **Collaborative Editing**: Multi-user template design
4. **Version Control**: Template versioning and rollback
5. **Print Preview**: Accurate WYSIWYG preview

---
**Status**: Visual Designer Complete | ZPL Integration Partial
**Usage**: Available at `/label-designer` and embedded in template manager
**Dependencies**: Fabric.js, JsBarcode, QRCode libraries