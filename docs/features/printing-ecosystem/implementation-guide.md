# Printing System Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the MCP printing system backend and completing the integration with the existing frontend infrastructure.

## Current State Assessment

### What's Already Built

✅ **Frontend Components**:
- Complete MCP client hook (`useMCPPrinting.ts`)
- All UI integrations (CT Modal, Orders, Quick Print, etc.)
- Label Designer with Fabric.js
- Template Management System
- ZPL Generation (predefined templates)
- Settings Administration Interface

❌ **What's Missing**:
- MCP Backend Server
- Physical printer connections
- Canvas-to-ZPL integration completion
- Template delete handler

## Phase 1: MCP Backend Implementation

### Step 1: Create MCP Server Directory

```bash
# Create server structure
mkdir -p mcp-server/{services,queue,database,config,logs}
cd mcp-server

# Initialize package.json
npm init -y
```

### Step 2: Install Dependencies

```bash
npm install express cors body-parser ws sqlite3 uuid \
            bonjour-service node-printer @types/node

npm install -D typescript @types/express @types/ws \
               @types/cors nodemon ts-node
```

### Step 3: Create Server Configuration

**`mcp-server/tsconfig.json`**:
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
```

**`mcp-server/package.json` scripts**:
```json
{
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "init-db": "node scripts/init-database.js"
  }
}
```

### Step 4: Implement Core Server

**`mcp-server/src/server.ts`**:
```typescript
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { initDatabase } from './database/init';
import { PrinterDiscoveryService } from './services/printer-discovery';
import { ZebraPrinterService } from './services/zebra-printer';
import { JobQueueManager } from './queue/job-manager';
import { HealthMonitor } from './services/health-monitor';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// Initialize services
const printerDiscovery = new PrinterDiscoveryService();
const zebraPrinter = new ZebraPrinterService();
const jobQueue = new JobQueueManager();
const healthMonitor = new HealthMonitor();

// WebSocket connection handling
wss.on('connection', (ws) => {
  console.log('New WebSocket connection');
  
  // Send initial printer list
  ws.send(JSON.stringify({
    type: 'printer-list',
    printers: printerDiscovery.getPrinters()
  }));
  
  // Handle messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      handleWebSocketMessage(ws, data);
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });
  
  // Heartbeat
  const interval = setInterval(() => {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: 'ping' }));
    }
  }, 30000);
  
  ws.on('close', () => {
    clearInterval(interval);
  });
});

// REST API Routes
app.post('/api/mcp/print/zebra', async (req, res) => {
  try {
    const { printer_id, zpl, metadata } = req.body;
    
    // Validate input
    if (!printer_id || !zpl) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields' 
      });
    }
    
    // Create job
    const job = await jobQueue.createJob({
      type: 'zebra',
      printer_id,
      content: zpl,
      metadata
    });
    
    // Process job
    const result = await zebraPrinter.print(printer_id, zpl);
    
    // Update job status
    await jobQueue.updateJob(job.id, {
      status: result.success ? 'completed' : 'failed',
      completed_at: new Date(),
      error: result.error
    });
    
    // Broadcast status
    broadcastJobUpdate(job.id, result.success ? 'completed' : 'failed');
    
    res.json(result);
  } catch (error) {
    console.error('Print error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

app.get('/api/mcp/printers/discover', async (req, res) => {
  try {
    await printerDiscovery.scan();
    const printers = printerDiscovery.getPrinters();
    res.json({ success: true, printers });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

app.get('/api/mcp/printers/status', async (req, res) => {
  const statuses = await healthMonitor.checkAllPrinters();
  res.json({ success: true, statuses });
});

app.get('/api/mcp/queue/status', async (req, res) => {
  const status = await jobQueue.getQueueStatus();
  res.json({ success: true, ...status });
});

app.delete('/api/mcp/queue/job/:id', async (req, res) => {
  try {
    await jobQueue.cancelJob(req.params.id);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Health check
app.get('/api/mcp/health', (req, res) => {
  res.json({
    status: 'online',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    services: {
      discovery: printerDiscovery.isRunning(),
      queue: jobQueue.isHealthy(),
      websocket: wss.clients.size
    }
  });
});

// Helper functions
function broadcastJobUpdate(jobId: string, status: string) {
  const message = JSON.stringify({
    type: 'job-status',
    jobId,
    status
  });
  
  wss.clients.forEach(client => {
    if (client.readyState === client.OPEN) {
      client.send(message);
    }
  });
}

function handleWebSocketMessage(ws: any, data: any) {
  switch (data.type) {
    case 'pong':
      // Client responded to ping
      break;
    case 'subscribe-printer':
      // Client wants updates for specific printer
      break;
    case 'get-queue':
      ws.send(JSON.stringify({
        type: 'queue-status',
        queue: jobQueue.getActiveJobs()
      }));
      break;
  }
}

// Start server
const PORT = process.env.PORT || 3001;

async function start() {
  // Initialize database
  await initDatabase();
  
  // Start services
  await printerDiscovery.start();
  healthMonitor.start();
  
  // Start server
  server.listen(PORT, () => {
    console.log(`MCP Server running on http://localhost:${PORT}`);
    console.log(`WebSocket available on ws://localhost:${PORT}`);
  });
}

start().catch(console.error);
```

### Step 5: Implement Printer Discovery

**`mcp-server/src/services/printer-discovery.ts`**:
```typescript
import { Bonjour } from 'bonjour-service';
import net from 'net';
import { Printer } from '../types';

export class PrinterDiscoveryService {
  private bonjour: any;
  private printers: Map<string, Printer> = new Map();
  private scanning = false;
  
  constructor() {
    this.bonjour = new Bonjour();
  }
  
  async start() {
    // Discover Zebra printers via Bonjour
    const browser = this.bonjour.find({ type: 'printer' });
    
    browser.on('up', (service: any) => {
      if (this.isZebraPrinter(service)) {
        this.addPrinter({
          id: service.name,
          name: service.name,
          type: 'zebra',
          ip: service.addresses[0],
          port: service.port || 9100,
          model: this.detectModel(service),
          status: 'unknown'
        });
      }
    });
    
    browser.on('down', (service: any) => {
      this.removePrinter(service.name);
    });
    
    // Also scan common ports
    this.scanNetworkPorts();
  }
  
  async scan() {
    if (this.scanning) return;
    this.scanning = true;
    
    try {
      // Scan local network for Zebra printers
      const subnet = this.getLocalSubnet();
      const promises = [];
      
      for (let i = 1; i <= 254; i++) {
        const ip = `${subnet}.${i}`;
        promises.push(this.checkZebraPrinter(ip));
      }
      
      await Promise.allSettled(promises);
    } finally {
      this.scanning = false;
    }
  }
  
  private async checkZebraPrinter(ip: string): Promise<void> {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        resolve();
      }, 1000);
      
      socket.connect(9100, ip, () => {
        clearTimeout(timeout);
        
        // Send ZPL status query
        socket.write('~HS\r\n');
        
        socket.on('data', (data) => {
          const response = data.toString();
          if (response.includes('ZEBRA')) {
            this.addPrinter({
              id: `zebra_${ip.replace(/\./g, '_')}`,
              name: `Zebra Printer at ${ip}`,
              type: 'zebra',
              ip,
              port: 9100,
              model: this.parseZebraModel(response),
              status: 'online'
            });
          }
          socket.destroy();
          resolve();
        });
      });
      
      socket.on('error', () => {
        clearTimeout(timeout);
        resolve();
      });
    });
  }
  
  private getLocalSubnet(): string {
    const interfaces = require('os').networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if ('IPv4' !== iface.family || iface.internal) continue;
        const parts = iface.address.split('.');
        return `${parts[0]}.${parts[1]}.${parts[2]}`;
      }
    }
    return '192.168.1';
  }
  
  getPrinters(): Printer[] {
    return Array.from(this.printers.values());
  }
  
  getPrinter(id: string): Printer | undefined {
    return this.printers.get(id);
  }
  
  private addPrinter(printer: Printer) {
    this.printers.set(printer.id, printer);
    this.broadcastPrinterUpdate();
  }
  
  private removePrinter(id: string) {
    this.printers.delete(id);
    this.broadcastPrinterUpdate();
  }
  
  private broadcastPrinterUpdate() {
    // Implemented in main server
  }
  
  isRunning(): boolean {
    return !this.scanning;
  }
}
```

### Step 6: Implement Zebra Printer Service

**`mcp-server/src/services/zebra-printer.ts`**:
```typescript
import net from 'net';
import { PrintResult } from '../types';

export class ZebraPrinterService {
  async print(printerId: string, zpl: string): Promise<PrintResult> {
    const printer = this.getPrinterInfo(printerId);
    if (!printer) {
      return {
        success: false,
        error: 'Printer not found',
        timestamp: new Date()
      };
    }
    
    return new Promise((resolve) => {
      const socket = new net.Socket();
      let timeout: NodeJS.Timeout;
      
      const cleanup = () => {
        clearTimeout(timeout);
        socket.destroy();
      };
      
      timeout = setTimeout(() => {
        cleanup();
        resolve({
          success: false,
          error: 'Print timeout',
          timestamp: new Date()
        });
      }, 30000);
      
      socket.connect(printer.port, printer.ip, () => {
        console.log(`Connected to printer ${printer.name}`);
        
        // Send ZPL
        socket.write(zpl, (error) => {
          if (error) {
            cleanup();
            resolve({
              success: false,
              error: error.message,
              timestamp: new Date()
            });
          } else {
            // Wait a bit for printer to process
            setTimeout(() => {
              cleanup();
              resolve({
                success: true,
                jobId: this.generateJobId(),
                timestamp: new Date()
              });
            }, 500);
          }
        });
      });
      
      socket.on('error', (error) => {
        cleanup();
        resolve({
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      });
    });
  }
  
  async getStatus(printerId: string): Promise<any> {
    const printer = this.getPrinterInfo(printerId);
    if (!printer) return null;
    
    return new Promise((resolve) => {
      const socket = new net.Socket();
      const timeout = setTimeout(() => {
        socket.destroy();
        resolve({ status: 'offline' });
      }, 5000);
      
      socket.connect(printer.port, printer.ip, () => {
        socket.write('~HS\r\n');
        
        socket.on('data', (data) => {
          clearTimeout(timeout);
          const status = this.parseStatus(data.toString());
          socket.destroy();
          resolve(status);
        });
      });
      
      socket.on('error', () => {
        clearTimeout(timeout);
        resolve({ status: 'error' });
      });
    });
  }
  
  private getPrinterInfo(printerId: string): any {
    // Get from discovery service
    return null;
  }
  
  private generateJobId(): string {
    return `JOB_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private parseStatus(response: string): any {
    // Parse Zebra status response
    return {
      status: 'ready',
      mediaOut: response.includes('MEDIA OUT'),
      ribbonOut: response.includes('RIBBON OUT'),
      headUp: response.includes('HEAD UP'),
      paused: response.includes('PAUSED')
    };
  }
}
```

### Step 7: Database Setup

**`mcp-server/src/database/init.ts`**:
```typescript
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';

export async function initDatabase() {
  const db = await open({
    filename: path.join(__dirname, '../../database/print-jobs.db'),
    driver: sqlite3.Database
  });
  
  // Create tables
  await db.exec(`
    CREATE TABLE IF NOT EXISTS print_jobs (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL CHECK(type IN ('zebra', 'generic')),
      printer_id TEXT NOT NULL,
      status TEXT DEFAULT 'pending',
      content TEXT NOT NULL,
      metadata TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      started_at DATETIME,
      completed_at DATETIME,
      error TEXT,
      retry_count INTEGER DEFAULT 0
    );
    
    CREATE TABLE IF NOT EXISTS printer_cache (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      type TEXT NOT NULL,
      ip TEXT,
      port INTEGER,
      model TEXT,
      status TEXT,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      health_data TEXT
    );
    
    CREATE INDEX idx_print_jobs_status ON print_jobs(status);
    CREATE INDEX idx_print_jobs_created ON print_jobs(created_at);
    CREATE INDEX idx_printer_cache_type ON printer_cache(type);
  `);
  
  return db;
}
```

## Phase 2: Frontend Integration Updates

### Step 1: Update Environment Variables

**`.env`**:
```bash
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_WS_URL=ws://localhost:3001
VITE_MCP_API_KEY=your-secure-api-key-here
```

### Step 2: Test MCP Connection

```bash
# Start MCP server
cd mcp-server
npm run dev

# In another terminal, start frontend
cd ..
npm run dev
```

### Step 3: Verify Integration Points

1. **Check MCP Status**: Navigate to Settings → MCP Printing
2. **Test Printer Discovery**: Click "Discover Printers"
3. **Test Print**: Use Quick Print button in navigation
4. **Monitor WebSocket**: Check browser console for connection

## Phase 3: Complete Canvas-to-ZPL Integration

### Step 1: Update Template Save Flow

**In `LabelDesigner.tsx`**:
```typescript
const handleSave = async () => {
  const canvasData = saveCanvasData();
  
  // Generate ZPL from canvas
  const generator = new ZPLGenerator(currentLabelSize);
  const zpl = generator.generateFromCanvas(canvasData, {});
  
  // Save both canvas and ZPL
  const template = {
    name: templateName,
    description: templateDescription,
    category: selectedCategory,
    template_type: templateType,
    label_size: currentLabelSize,
    canvas_data: canvasData,
    zpl_template: zpl, // Now included
    is_active: true
  };
  
  await createLabelTemplate(template);
};
```

### Step 2: Update Print Flow to Use Canvas ZPL

**In label printing functions**:
```typescript
const printFromTemplate = async (templateId: string, data: any) => {
  const template = await loadTemplate(templateId);
  
  if (template.zpl_template) {
    // Use pre-generated ZPL with dynamic data
    const zpl = replaceDynamicFields(template.zpl_template, data);
    return await printZebraLabel(selectedPrinter, zpl, metadata);
  } else if (template.canvas_data) {
    // Generate ZPL on demand
    const generator = new ZPLGenerator(template.label_size);
    const zpl = generator.generateFromCanvas(template.canvas_data, data);
    return await printZebraLabel(selectedPrinter, zpl, metadata);
  }
};
```

## Phase 4: Implement Missing Features

### Template Delete Handler

**In `TemplateManager.tsx`**:
```typescript
const handleDelete = async (templateId: string) => {
  const confirmed = await confirm({
    title: 'Delete Template',
    description: 'Are you sure you want to delete this template?',
    confirmText: 'Delete',
    cancelText: 'Cancel'
  });
  
  if (!confirmed) return;
  
  try {
    const { error } = await supabase
      .from('label_templates')
      .delete()
      .eq('id', templateId);
      
    if (error) throw error;
    
    queryClient.invalidateQueries(['label-templates']);
    toast.success('Template deleted successfully');
  } catch (error) {
    console.error('Delete error:', error);
    toast.error('Failed to delete template');
  }
};
```

## Phase 5: Testing & Deployment

### Testing Checklist

1. **Unit Tests**:
   - [ ] Test ZPL generation from canvas
   - [ ] Test printer discovery
   - [ ] Test job queue operations
   - [ ] Test failsafe mechanisms

2. **Integration Tests**:
   - [ ] Test CT assignment → print flow
   - [ ] Test quick print functionality
   - [ ] Test template management
   - [ ] Test MCP reconnection

3. **Physical Printer Tests**:
   - [ ] Connect Zebra printer to network
   - [ ] Test discovery
   - [ ] Print test labels
   - [ ] Test error scenarios

### Production Deployment

1. **Build MCP Server**:
   ```bash
   cd mcp-server
   npm run build
   ```

2. **Create Windows Service** (for production):
   ```javascript
   // install-service.js
   const Service = require('node-windows').Service;
   
   const svc = new Service({
     name: 'MCP Print Server',
     description: 'Mini-ERP MCP Printing Service',
     script: 'C:\\mcp-server\\dist\\server.js'
   });
   
   svc.on('install', () => {
     svc.start();
   });
   
   svc.install();
   ```

3. **Configure Firewall**:
   - Open port 3001 for MCP API
   - Open port 9100 for Zebra communication

4. **Monitor Logs**:
   ```bash
   tail -f mcp-server/logs/mcp-server.log
   ```

## Troubleshooting Guide

### Common Issues

1. **MCP Connection Failed**:
   - Check server is running
   - Verify firewall settings
   - Check CORS configuration

2. **Printer Not Discovered**:
   - Ensure printer on same network
   - Check printer IP settings
   - Verify port 9100 open

3. **Print Jobs Failing**:
   - Check ZPL syntax
   - Verify printer online
   - Check job queue database

4. **WebSocket Disconnections**:
   - Implement reconnection logic
   - Check proxy settings
   - Monitor network stability

### Debug Mode

**Enable debug logging**:
```typescript
// In server.ts
if (process.env.DEBUG) {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`, req.body);
    next();
  });
}
```

## Maintenance Tasks

### Regular Tasks

1. **Weekly**:
   - Check print job success rate
   - Review error logs
   - Update printer firmware

2. **Monthly**:
   - Clean job history (>30 days)
   - Update printer drivers
   - Performance optimization

3. **Quarterly**:
   - Security updates
   - Dependency updates
   - Load testing

---
**Timeline**: 1-2 weeks for full implementation
**Priority**: MCP backend is critical path
**Risk**: Printer network configuration complexity