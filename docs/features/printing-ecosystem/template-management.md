# Template Management System

## Overview

The Template Management System provides a database-driven solution for creating, organizing, and managing label templates across the organization. It integrates with the Label Designer for visual editing and the MCP printing system for output.

## Database Architecture

### Schema Design

**Table**: `label_templates`

```sql
CREATE TABLE label_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL CHECK (category IN (
        'shipping', 'warning', 'priority', 'qc', 
        'inspection', 'testing', 'returns', 'ct_labels'
    )),
    template_type TEXT NOT NULL CHECK (template_type IN (
        'quick_print', 'ct_label'
    )),
    label_size JSONB NOT NULL DEFAULT '{"width": 4, "height": 6, "unit": "inch"}',
    zpl_template TEXT,
    canvas_data JSONB,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_template_name UNIQUE (name)
);

-- Indexes for performance
CREATE INDEX idx_label_templates_category ON label_templates(category);
CREATE INDEX idx_label_templates_type ON label_templates(template_type);
CREATE INDEX idx_label_templates_active ON label_templates(is_active);
CREATE INDEX idx_label_templates_created_by ON label_templates(created_by);

-- Update trigger
CREATE TRIGGER update_label_templates_updated_at 
    BEFORE UPDATE ON label_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

### Row Level Security (RLS)

```sql
-- Enable RLS
ALTER TABLE label_templates ENABLE ROW LEVEL SECURITY;

-- Policy: All authenticated users can view active templates
CREATE POLICY "Users can view active templates" ON label_templates
    FOR SELECT
    USING (is_active = true);

-- Policy: Users can create templates
CREATE POLICY "Users can create templates" ON label_templates
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

-- Policy: Users can update their own templates
CREATE POLICY "Users can update own templates" ON label_templates
    FOR UPDATE
    USING (auth.uid() = created_by);

-- Policy: Only admins can delete templates
CREATE POLICY "Admins can delete templates" ON label_templates
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'director')
        )
    );
```

## Template Categories

### Category Definitions

```typescript
export const TEMPLATE_CATEGORIES = {
  shipping: {
    label: 'Shipping Labels',
    description: 'Labels for outbound shipments',
    icon: 'Package',
    color: 'blue'
  },
  warning: {
    label: 'Warning Labels',
    description: 'Safety and handling warnings',
    icon: 'AlertTriangle',
    color: 'yellow'
  },
  priority: {
    label: 'Priority Labels',
    description: 'Urgent and priority markers',
    icon: 'Zap',
    color: 'red'
  },
  qc: {
    label: 'QC Labels',
    description: 'Quality control status',
    icon: 'CheckCircle',
    color: 'green'
  },
  inspection: {
    label: 'Inspection Labels',
    description: 'Inspection requirements',
    icon: 'Search',
    color: 'purple'
  },
  testing: {
    label: 'Testing Labels',
    description: 'Test status and results',
    icon: 'Beaker',
    color: 'cyan'
  },
  returns: {
    label: 'Return Labels',
    description: 'Return merchandise labels',
    icon: 'RotateCcw',
    color: 'orange'
  },
  ct_labels: {
    label: 'CT Number Labels',
    description: 'Container tracking labels',
    icon: 'Hash',
    color: 'indigo'
  }
} as const;
```

## Template Manager Component

### Component Structure

**File**: `/src/components/admin/TemplateManager.tsx`

```typescript
interface TemplateManagerProps {
  onTemplateSelect?: (template: LabelTemplate) => void;
  selectionMode?: boolean;
  allowedCategories?: TemplateCategory[];
}

const TemplateManager: React.FC<TemplateManagerProps> = ({
  onTemplateSelect,
  selectionMode = false,
  allowedCategories
}) => {
  const [templates, setTemplates] = useState<LabelTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<LabelTemplate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  
  // Template operations
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['label-templates'],
    queryFn: fetchLabelTemplates
  });
  
  // Filter logic
  useEffect(() => {
    let filtered = templates;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }
    
    if (searchQuery) {
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    setFilteredTemplates(filtered);
  }, [templates, selectedCategory, searchQuery]);
};
```

### Template Grid Display

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  {filteredTemplates.map((template) => (
    <TemplateCard
      key={template.id}
      template={template}
      onClick={() => handleTemplateClick(template)}
      onEdit={() => handleEdit(template)}
      onDelete={() => handleDelete(template)}
      onToggleActive={() => handleToggleActive(template)}
      showActions={!selectionMode}
    />
  ))}
</div>
```

### Template Card Component

```tsx
const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onClick,
  onEdit,
  onDelete,
  onToggleActive,
  showActions
}) => {
  const category = TEMPLATE_CATEGORIES[template.category];
  
  return (
    <Card 
      className="cursor-pointer hover:shadow-lg transition-shadow"
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <Badge variant={template.is_active ? 'default' : 'secondary'}>
            {category.label}
          </Badge>
          {!template.is_active && (
            <Badge variant="outline">Inactive</Badge>
          )}
        </div>
        <CardTitle className="text-lg mt-2">{template.name}</CardTitle>
      </CardHeader>
      
      <CardContent>
        {template.description && (
          <p className="text-sm text-muted-foreground mb-3">
            {template.description}
          </p>
        )}
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>{template.label_size.width} x {template.label_size.height}</span>
          <span>{template.label_size.unit}</span>
        </div>
        
        {template.canvas_data && (
          <div className="mt-3">
            <CanvasPreview 
              canvasData={template.canvas_data} 
              width={150} 
              height={100}
            />
          </div>
        )}
      </CardContent>
      
      {showActions && (
        <CardFooter className="pt-3 flex justify-between">
          <Button 
            size="sm" 
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="ghost">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onToggleActive}>
                {template.is_active ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={onDelete}
                className="text-destructive"
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardFooter>
      )}
    </Card>
  );
};
```

## Template Operations

### Create Template

```typescript
const createTemplate = async (templateData: Partial<LabelTemplate>) => {
  const { data, error } = await supabase
    .from('label_templates')
    .insert({
      ...templateData,
      created_by: user.id
    })
    .select()
    .single();
    
  if (error) throw error;
  
  // Invalidate cache
  queryClient.invalidateQueries(['label-templates']);
  
  return data;
};
```

### Update Template

```typescript
const updateTemplate = async (
  templateId: string, 
  updates: Partial<LabelTemplate>
) => {
  const { data, error } = await supabase
    .from('label_templates')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', templateId)
    .select()
    .single();
    
  if (error) throw error;
  
  queryClient.invalidateQueries(['label-templates']);
  
  return data;
};
```

### Delete Template [Handler Not Implemented]

```typescript
// UI exists but handler missing
const handleDelete = async (templateId: string) => {
  // TODO: Implement delete handler
  toast.error('Delete functionality not yet implemented');
  
  /* Planned implementation:
  const { error } = await supabase
    .from('label_templates')
    .delete()
    .eq('id', templateId);
    
  if (error) {
    toast.error('Failed to delete template');
    return;
  }
  
  queryClient.invalidateQueries(['label-templates']);
  toast.success('Template deleted successfully');
  */
};
```

### Toggle Active Status

```typescript
const toggleTemplateActive = async (template: LabelTemplate) => {
  const { error } = await supabase
    .from('label_templates')
    .update({ is_active: !template.is_active })
    .eq('id', template.id);
    
  if (error) {
    toast.error('Failed to update template status');
    return;
  }
  
  queryClient.invalidateQueries(['label-templates']);
  toast.success(
    template.is_active 
      ? 'Template deactivated' 
      : 'Template activated'
  );
};
```

## Search and Filter Implementation

### Filter Controls

```tsx
<div className="flex flex-col sm:flex-row gap-4 mb-6">
  {/* Search */}
  <div className="relative flex-1">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
    <Input
      placeholder="Search templates..."
      value={searchQuery}
      onChange={(e) => setSearchQuery(e.target.value)}
      className="pl-10"
    />
  </div>
  
  {/* Category Filter */}
  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
    <SelectTrigger className="w-full sm:w-[200px]">
      <SelectValue placeholder="All Categories" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="all">All Categories</SelectItem>
      {Object.entries(TEMPLATE_CATEGORIES).map(([key, cat]) => (
        <SelectItem key={key} value={key}>
          <div className="flex items-center gap-2">
            <span>{cat.label}</span>
            <Badge variant="secondary" className="ml-auto">
              {templates.filter(t => t.category === key).length}
            </Badge>
          </div>
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
  
  {/* Active Filter */}
  <ToggleGroup type="single" value={activeFilter} onValueChange={setActiveFilter}>
    <ToggleGroupItem value="all">All</ToggleGroupItem>
    <ToggleGroupItem value="active">Active</ToggleGroupItem>
    <ToggleGroupItem value="inactive">Inactive</ToggleGroupItem>
  </ToggleGroup>
</div>
```

## Integration with Label Designer

### Edit Template Flow

```typescript
const handleEditTemplate = (template: LabelTemplate) => {
  // Navigate to label designer with template data
  navigate('/label-designer', {
    state: {
      templateId: template.id,
      templateData: template.canvas_data,
      labelSize: template.label_size,
      returnTo: '/settings?tab=templates'
    }
  });
};

// In Label Designer
const location = useLocation();
const { templateId, templateData, labelSize, returnTo } = location.state || {};

useEffect(() => {
  if (templateData) {
    loadCanvasData(templateData);
    setCurrentLabelSize(labelSize);
  }
}, [templateData]);

const handleSave = async () => {
  if (templateId) {
    // Update existing template
    await updateTemplate(templateId, {
      canvas_data: saveCanvasData(),
      label_size: currentLabelSize
    });
  } else {
    // Create new template
    await createTemplate({
      name: templateName,
      canvas_data: saveCanvasData(),
      label_size: currentLabelSize
    });
  }
  
  if (returnTo) {
    navigate(returnTo);
  }
};
```

## Predefined Templates

### Sample Templates Initialization

```typescript
const SAMPLE_TEMPLATES = [
  {
    name: 'HP External Box Label',
    description: 'Standard shipping label for HP orders',
    category: 'shipping',
    template_type: 'quick_print',
    label_size: { width: 102, height: 152, unit: 'mm' },
    zpl_template: generateHPExternalZPL()
  },
  {
    name: 'QC Hold Label',
    description: 'Quality control hold notification',
    category: 'qc',
    template_type: 'quick_print',
    label_size: { width: 51, height: 25, unit: 'mm' },
    zpl_template: generateQCHoldZPL()
  },
  {
    name: 'Default CT Label',
    description: 'Standard CT number label with barcode',
    category: 'ct_labels',
    template_type: 'ct_label',
    label_size: { width: 102, height: 152, unit: 'mm' },
    zpl_template: generateDefaultCTZPL()
  }
];

// Initialize on first run
const initializeSampleTemplates = async () => {
  const { count } = await supabase
    .from('label_templates')
    .select('*', { count: 'exact', head: true });
    
  if (count === 0) {
    const { error } = await supabase
      .from('label_templates')
      .insert(SAMPLE_TEMPLATES);
      
    if (!error) {
      console.log('Sample templates initialized');
    }
  }
};
```

## Canvas Preview Component

```tsx
const CanvasPreview: React.FC<{
  canvasData: any;
  width: number;
  height: number;
}> = ({ canvasData, width, height }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (canvasRef.current && canvasData) {
      const previewCanvas = new fabric.StaticCanvas(canvasRef.current, {
        width,
        height,
        backgroundColor: 'white'
      });
      
      // Scale canvas to fit preview
      const scale = Math.min(
        width / canvasData.width,
        height / canvasData.height
      );
      
      previewCanvas.loadFromJSON(canvasData, () => {
        previewCanvas.setZoom(scale);
        previewCanvas.renderAll();
      });
      
      return () => {
        previewCanvas.dispose();
      };
    }
  }, [canvasData, width, height]);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="border rounded shadow-sm"
    />
  );
};
```

## Performance Considerations

### Query Optimization

```typescript
// Efficient template fetching with pagination
const fetchTemplates = async (page: number, limit: number = 20) => {
  const from = page * limit;
  const to = from + limit - 1;
  
  const { data, error, count } = await supabase
    .from('label_templates')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(from, to);
    
  return { 
    templates: data || [], 
    totalCount: count || 0,
    hasMore: (count || 0) > to + 1
  };
};
```

### Canvas Data Optimization

```typescript
// Compress canvas data before storage
const compressCanvasData = (canvasData: any) => {
  // Remove unnecessary properties
  const compressed = {
    ...canvasData,
    objects: canvasData.objects.map(obj => {
      const { canvas, _element, ...rest } = obj;
      return rest;
    })
  };
  
  return compressed;
};
```

## Security Considerations

1. **RLS Policies**: Enforce access control at database level
2. **Input Validation**: Sanitize template names and descriptions
3. **Size Limits**: Enforce maximum canvas data size (1MB)
4. **Rate Limiting**: Prevent template spam creation

## Future Enhancements

1. **Template Versioning**: Track changes with rollback capability
2. **Template Sharing**: Share templates across organizations
3. **Import/Export**: Template backup and migration
4. **Approval Workflow**: Review process for production templates
5. **Usage Analytics**: Track template usage and popularity

---
**Status**: Production Ready (except delete handler)
**Database**: Fully implemented with RLS
**Integration**: Connected to Label Designer and Print System