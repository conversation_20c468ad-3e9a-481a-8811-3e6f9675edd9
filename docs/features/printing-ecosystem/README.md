# Printing Ecosystem Module

## Overview

The Mini-ERP printing ecosystem provides enterprise-grade label and document printing capabilities through a sophisticated architecture combining visual design tools, template management, and the Model Context Protocol (MCP) for cloud-to-local printer integration.

## System Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────────┐
│                          React Frontend                          │
├─────────────────────────────────────────────────────────────────┤
│  Label Designer  │  Template Manager  │  Print Integration      │
│  (Fabric.js)     │  (Database)        │  (MCP Client)          │
├─────────────────────────────────────────────────────────────────┤
│                       MCP Frontend Client                        │
│              (WebSocket + HTTP + 4-Tier Failsafe)               │
└────────────────────┬───────────────────────────────────────────┘
                     │ WebSocket/HTTP
                     ↓
┌─────────────────────────────────────────────────────────────────┐
│                    MCP Backend Server                            │
│                  [NOT YET IMPLEMENTED]                           │
│              (Express.js + SQLite + WebSocket)                  │
└────────────────────┬───────────────────────────────────────────┘
                     │ TCP/IPP
                     ↓
┌─────────────────────────────────────────────────────────────────┐
│  Zebra Printers  │  Generic Printers  │  PDF Generation        │
│  (ZPL/Port 9100) │  (IPP/CUPS)        │  (React-PDF)          │
└─────────────────────────────────────────────────────────────────┘
```

### Production Status

| Component | Status | Notes |
|-----------|--------|-------|
| Label Designer | ✅ Production Ready | Full WYSIWYG with Fabric.js |
| Template Management | ✅ Production Ready | Database-driven with RLS |
| MCP Frontend | ✅ Production Ready | Complete with failsafe |
| MCP Backend | 🔴 Not Implemented | Critical missing component |
| ZPL Generation | 🟡 Partial | Predefined templates work |
| Canvas-to-ZPL | 🟡 Partial | Converter exists, not integrated |

## Key Features

### 1. Visual Label Designer
- **Technology**: Fabric.js v6.7.0 canvas-based editor
- **Tools**: Text, shapes, barcodes (Code128, Code39, QR), images
- **Professional Features**: Undo/redo, grid system, zoom (10-300%), properties panel
- **Data Integration**: Dynamic field placeholders for order data
- **Storage**: Canvas data saved to database as JSONB

### 2. Template Management System
- **Categories**: 8 predefined (shipping, warning, priority, qc, inspection, testing, returns, ct_labels)
- **Security**: Row Level Security with role-based permissions
- **Operations**: Create, read, update (delete handler not implemented)
- **Sample Templates**: 3 preloaded for immediate use
- **Search & Filter**: Category-based filtering with search

### 3. MCP Printing Architecture
- **Frontend Client**: Complete WebSocket integration with real-time updates
- **4-Tier Failsafe**: MCP → Static Config → File Download → Error Notification
- **Printer Discovery**: Automatic via Bonjour/mDNS (when backend available)
- **Health Monitoring**: 3-point connection status indicators
- **Job Queue**: SQLite-based with status tracking

### 4. ZPL Generation System
- **Dual Architecture**: Predefined templates + Canvas converter
- **Dynamic Data**: Order information injection at print time
- **Preview**: Labelary.com integration for visual confirmation
- **Templates**: 7 professional templates ready to use

## Integration Points

### Primary Interfaces
1. **CT Number Modal** (`/src/components/orders/CTNumberModal.tsx`)
   - Immediate print after CT assignment
   - Unified workflow with print options
   - Auto-progress capability

2. **Quick Print Modal** (`/src/components/printing/QuickPrintModal.tsx`)
   - Standalone label printing
   - 7 warehouse operation templates
   - Direct MCP integration

3. **Orders Page** (`/src/pages/Orders.tsx`)
   - Print buttons on every order card
   - Smart template selection
   - Context-aware printing

4. **Label Designer Page** (`/src/pages/LabelDesignerPage.tsx`)
   - Full-page design interface
   - Test print functionality
   - Template save/load

5. **Settings Administration** (`/src/components/admin/MCPSettings.tsx`)
   - MCP configuration and monitoring
   - Printer management
   - Queue visibility

## Current Limitations & Workarounds

### Critical Gap: MCP Backend Server
**Issue**: Backend server not implemented despite frontend readiness
**Impact**: No direct network printing capability
**Workaround**: System degrades gracefully to ZPL file download

### Canvas-to-ZPL Integration
**Issue**: Visual designs not automatically converted to ZPL
**Impact**: Must use predefined templates for printing
**Workaround**: 7 professional templates cover common use cases

### Template Deletion
**Issue**: Delete button exists but handler not implemented
**Impact**: Cannot remove unwanted templates
**Workaround**: Deactivate templates via is_active flag

## Technical Implementation

### File Structure
```
src/
├── hooks/
│   ├── useMCPPrinting.ts      # MCP client integration (425 lines)
│   └── useLabelPrinting.ts    # Legacy hook (replaced by MCP)
├── components/
│   ├── labelDesigner/
│   │   └── LabelDesigner.tsx  # Fabric.js designer
│   ├── printing/
│   │   ├── QuickPrintModal.tsx
│   │   └── LabelPrintModal.tsx
│   ├── admin/
│   │   ├── MCPSettings.tsx    # Complete admin interface
│   │   └── TemplateManager.tsx
│   └── ui/
│       └── mcp-connection-status.tsx
├── utils/
│   ├── labelGeneration.ts     # Predefined ZPL templates
│   └── zplGenerator.ts        # Canvas-to-ZPL converter
└── types/
    └── labelDesigner.ts       # TypeScript definitions

supabase/
└── label_templates_schema.sql # Database schema
```

### Database Schema
```sql
CREATE TABLE label_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    template_type TEXT NOT NULL,
    label_size JSONB DEFAULT '{"width": 4, "height": 6, "unit": "inch"}',
    zpl_template TEXT,
    canvas_data JSONB,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Dependencies

### Frontend Libraries
- **Fabric.js**: v6.7.0 - Canvas manipulation
- **JsBarcode**: Barcode generation
- **QRCode**: QR code generation
- **React Query**: Data synchronization

### External Services
- **Labelary.com**: ZPL preview generation
- **Zebra Printers**: Network accessible on port 9100
- **Generic Printers**: IPP/CUPS compatible

### Environment Variables
```bash
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_API_KEY=your-secure-api-key-here
VITE_LOCATION=SB
```

## Future Roadmap

### Phase 1: Complete Core Implementation
1. **Implement MCP Backend Server**
   - Express.js REST API + WebSocket
   - SQLite job queue
   - Printer discovery service
   - Health monitoring

2. **Complete Canvas-to-ZPL Integration**
   - Connect designer output to ZPL generator
   - Test with various label sizes
   - Validate output on physical printers

3. **Implement Template Delete**
   - Add handler for delete operations
   - Include cascade logic for dependencies

### Phase 2: Enhanced Features
- AI-powered printer selection
- Predictive maintenance alerts
- Advanced job queue management
- Cross-location load balancing

### Phase 3: Enterprise Features
- Print cost tracking
- Usage analytics dashboard
- Advanced permission system
- API for third-party integration

## Module Structure

This module contains detailed documentation for:
1. **mcp-architecture.md** - Complete MCP system design
2. **label-designer.md** - Visual designer implementation
3. **template-management.md** - Database-driven templates
4. **zpl-generation.md** - Label generation systems
5. **print-integration.md** - Workflow integration points
6. **implementation-guide.md** - Setup and configuration
7. **troubleshooting.md** - Common issues and solutions

---
**Module Status**: Core Frontend Complete | Backend Implementation Required
**Last Updated**: 12/06/2025, 7:30 PM IST
**Next Steps**: Implement MCP backend server for production deployment