# MCP (Model Context Protocol) Printing Architecture

## Overview

The MCP printing system represents a paradigm shift from traditional Network Print Servers (NPS) to an AI-enhanced, cloud-to-local printing bridge with comprehensive failsafe mechanisms and real-time monitoring capabilities.

## Architecture Components

### 1. MCP Frontend Client

**Location**: `/src/hooks/useMCPPrinting.ts` (425 lines)

#### Core Features
```typescript
interface MCPClientCapabilities {
  // Real-time Communication
  webSocket: {
    url: 'ws://localhost:3001',
    reconnect: true,
    heartbeat: 30000,
    events: ['status', 'discovery', 'job-update', 'error']
  };
  
  // HTTP API
  restApi: {
    baseUrl: 'http://localhost:3001/api/mcp',
    endpoints: [
      'POST /print/zebra',
      'POST /print/generic',
      'GET /printers/discover',
      'GET /printers/status',
      'GET /queue/status',
      'DELETE /queue/job/:id'
    ]
  };
  
  // Failsafe Layers
  failsafe: {
    level1: 'MCP Server Connection',
    level2: 'Static Printer Configuration',
    level3: 'ZPL File Download',
    level4: 'Error Notification with Instructions'
  };
}
```

#### Implementation Details

**WebSocket Integration**:
```typescript
// Real-time printer discovery and status updates
const ws = new WebSocket(mcpUrl.replace('http', 'ws'));

ws.on('message', (data) => {
  const message = JSON.parse(data);
  switch (message.type) {
    case 'printer-discovered':
      updatePrinterList(message.printers);
      break;
    case 'job-status':
      updateJobStatus(message.jobId, message.status);
      break;
    case 'printer-health':
      updatePrinterHealth(message.printerId, message.health);
      break;
  }
});
```

**4-Tier Failsafe System**:
```typescript
const printWithFailsafe = async (printer, zpl, metadata) => {
  try {
    // Level 1: Try MCP Server
    return await printViaMCP(printer, zpl, metadata);
  } catch (mcpError) {
    try {
      // Level 2: Fallback to static config
      return await printViaStaticConfig(printer, zpl);
    } catch (staticError) {
      try {
        // Level 3: Download ZPL file
        return await downloadZPLFile(zpl, metadata);
      } catch (downloadError) {
        // Level 4: Show comprehensive error
        showErrorWithInstructions({
          errors: [mcpError, staticError, downloadError],
          zpl,
          metadata,
          recoverySteps: getRecoveryInstructions()
        });
      }
    }
  }
};
```

### 2. MCP Backend Server [NOT IMPLEMENTED]

**Planned Architecture**:

```
mcp-server/
├── server.js              # Express.js main server
├── websocket.js           # WebSocket server
├── services/
│   ├── printer-discovery.js   # Bonjour/mDNS discovery
│   ├── zebra-printer.js       # ZPL communication
│   ├── generic-printer.js     # IPP/CUPS integration
│   └── health-monitor.js      # Printer health checks
├── queue/
│   ├── job-manager.js         # SQLite queue management
│   └── failsafe-storage.js    # Multi-tier backup
├── database/
│   └── print-jobs.db          # SQLite database
└── config/
    └── printers.json          # Static configuration
```

**Planned Features**:
- Express.js REST API server
- WebSocket for real-time updates
- SQLite for job queue persistence
- Automatic printer discovery
- Health monitoring with ping checks
- Multi-tier failsafe storage

### 3. Connection Status Monitoring

**Component**: `/src/components/ui/mcp-connection-status.tsx`

#### 3-Point Status Indicators
```typescript
interface ConnectionStatus {
  frontend: {
    toMCP: 'connected' | 'disconnected' | 'connecting',
    indicator: '🟢' | '🔴' | '🟡'
  };
  mcp: {
    toBackend: 'online' | 'offline' | 'starting',
    indicator: '✅' | '❌' | '⚠️'
  };
  printers: {
    health: Map<printerId, {
      status: 'ready' | 'busy' | 'error' | 'offline',
      responseTime: number,
      lastSeen: Date
    }>
  };
}
```

**Visual Implementation**:
```tsx
// Compact status indicator in navigation
<div className="flex items-center gap-2 text-xs">
  <span title="Frontend to MCP">{getFrontendIndicator()}</span>
  <span className="text-muted-foreground">→</span>
  <span title="MCP Server">{getMCPIndicator()}</span>
  <span className="text-muted-foreground">→</span>
  <span title="Printers">{getPrinterIndicator()}</span>
</div>
```

## Integration Points

### 1. CT Number Modal Integration

**File**: `/src/components/orders/CTNumberModal.tsx`

```typescript
// MCP integration for immediate printing
const { printZebraLabel, useZebraPrinters, isConnected } = useMCPPrinting();

// Print with comprehensive metadata
const handlePrint = async () => {
  const metadata = {
    order_uid: orderLine.uid,
    ct_numbers: savedCTNumbers,
    template_name: selectedTemplate,
    created_by: user?.email,
    part_number: orderLine.customer_part_number,
    quantity: printQuantity,
    timestamp: new Date().toISOString()
  };
  
  const result = await printZebraLabel(
    selectedPrinter,
    zplContent,
    metadata
  );
  
  if (result.success) {
    toast.success(`Printed ${printQuantity} labels`);
    if (workflowOption === 'auto') {
      await transitionQuantity(/* ... */);
    }
  }
};
```

### 2. Quick Print Modal Integration

**File**: `/src/components/printing/QuickPrintModal.tsx`

```typescript
// Template-based quick printing
const templates = [
  { id: 'hp-external', name: 'HP ORDER - EXTERNAL BOX' },
  { id: 'fragile', name: 'FRAGILE - HANDLE WITH CARE' },
  { id: 'priority', name: 'PRIORITY SHIPMENT' },
  { id: 'qc-hold', name: 'QC HOLD - DO NOT SHIP' },
  { id: 'inspection', name: 'INCOMING INSPECTION' },
  { id: 'motherboard', name: 'MOTHERBOARD - ESD SENSITIVE' },
  { id: 'return', name: 'RETURN TO VENDOR' }
];

// MCP quick print implementation
const handleQuickPrint = async () => {
  const zpl = generateQuickPrintZPL(selectedTemplate);
  await printZebraLabel(selectedPrinter, zpl, {
    template: selectedTemplate.id,
    quantity: printQuantity,
    purpose: 'quick_print'
  });
};
```

### 3. Settings Administration

**File**: `/src/components/admin/MCPSettings.tsx` (24,846 lines)

**Features**:
- **Overview Tab**: System health dashboard
- **Printers Tab**: Discovered printers with health status
- **Queue Tab**: Active and completed print jobs
- **Templates Tab**: Template management integration
- **Configuration Tab**: MCP server settings

## Printer Discovery & Management

### Zebra Printer Discovery
```typescript
interface ZebraPrinter {
  id: string;
  name: string;
  model: string;
  ip: string;
  port: 9100;
  status: 'online' | 'offline' | 'busy' | 'error';
  capabilities: {
    zpl: true;
    resolution: 203 | 300;
    maxWidth: number;
    mediaTypes: string[];
  };
  health: {
    responseTime: number;
    lastSeen: Date;
    jobsCompleted: number;
    errors: number;
  };
}
```

### Generic Printer Discovery
```typescript
interface GenericPrinter {
  id: string;
  name: string;
  manufacturer: string;
  ip: string;
  protocol: 'IPP' | 'LPR' | 'RAW';
  status: 'ready' | 'busy' | 'offline' | 'error';
  capabilities: {
    color: boolean;
    duplex: boolean;
    paperSizes: string[];
    formats: ['PDF', 'PostScript', 'PCL'];
  };
}
```

## Job Queue Management

### SQLite Schema (Planned)
```sql
CREATE TABLE print_jobs (
  id TEXT PRIMARY KEY,
  type TEXT CHECK(type IN ('zebra', 'generic')),
  printer_id TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  content TEXT NOT NULL,
  metadata JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  started_at DATETIME,
  completed_at DATETIME,
  error TEXT,
  retry_count INTEGER DEFAULT 0
);

CREATE TABLE printer_cache (
  id TEXT PRIMARY KEY,
  type TEXT,
  details JSON,
  last_seen DATETIME,
  health_status JSON
);
```

### Job Lifecycle
```
pending → processing → completed
         └─→ failed → retrying → abandoned
```

## Error Handling & Recovery

### Comprehensive Error Types
```typescript
enum MCPErrorType {
  CONNECTION_FAILED = 'MCP_CONNECTION_FAILED',
  PRINTER_OFFLINE = 'PRINTER_OFFLINE',
  INVALID_ZPL = 'INVALID_ZPL',
  QUEUE_FULL = 'QUEUE_FULL',
  UNAUTHORIZED = 'UNAUTHORIZED',
  PRINTER_ERROR = 'PRINTER_ERROR',
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT'
}

interface MCPError {
  type: MCPErrorType;
  message: string;
  details: any;
  timestamp: Date;
  recoverySteps: string[];
  fallbackAction?: () => Promise<void>;
}
```

### Recovery Strategies
1. **Automatic Retry**: Configurable retry with exponential backoff
2. **Printer Failover**: Switch to backup printer automatically
3. **Queue Persistence**: Jobs survive server restarts
4. **Manual Recovery**: Download options always available
5. **WhatsApp Alerts**: Critical failures notify staff

## Performance Optimization

### Caching Strategy
```typescript
interface MCPCache {
  printers: {
    data: Map<string, Printer>;
    ttl: 300000; // 5 minutes
    refreshInterval: 60000; // 1 minute
  };
  templates: {
    data: Map<string, CompiledTemplate>;
    ttl: 3600000; // 1 hour
  };
  health: {
    data: Map<string, HealthStatus>;
    ttl: 30000; // 30 seconds
  };
}
```

### Connection Pooling
- WebSocket connection reuse
- HTTP connection keep-alive
- Printer connection pooling
- Database connection management

## Security Considerations

### Authentication & Authorization
```typescript
interface MCPSecurity {
  apiKey: string; // VITE_MCP_API_KEY
  cors: {
    origin: ['http://localhost:5173'],
    credentials: true
  };
  rateLimit: {
    windowMs: 60000,
    max: 100
  };
  encryption: {
    transport: 'TLS 1.2+',
    storage: 'AES-256'
  };
}
```

### Access Control
- Role-based printer access
- Template usage permissions
- Print quota management
- Audit logging

## Monitoring & Analytics

### Metrics Collection
```typescript
interface MCPMetrics {
  system: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    queueDepth: number;
  };
  printers: {
    totalJobs: number;
    successRate: number;
    averageResponseTime: number;
    errorsByType: Map<string, number>;
  };
  usage: {
    jobsByUser: Map<string, number>;
    jobsByTemplate: Map<string, number>;
    peakHours: number[];
  };
}
```

### Health Dashboard
- Real-time system status
- Printer availability matrix
- Queue depth visualization
- Error rate tracking
- Performance trends

## Future AI Enhancements

### Planned AI Capabilities
1. **Smart Printer Selection**
   - Load balancing based on queue depth
   - Capability matching for job requirements
   - Predictive availability

2. **Maintenance Prediction**
   - Supply level monitoring
   - Failure pattern recognition
   - Proactive alert generation

3. **Optimization Engine**
   - Job batching for efficiency
   - Route optimization for multi-location
   - Energy usage optimization

4. **Context Understanding**
   - Priority detection from order context
   - Customer-specific routing rules
   - Deadline-aware scheduling

## Implementation Timeline

### Phase 1: Core MCP Server (1-2 weeks)
- Express.js server setup
- WebSocket implementation
- Basic printer communication
- SQLite queue management

### Phase 2: Discovery & Health (1 week)
- Bonjour/mDNS discovery
- Health monitoring service
- Status dashboard integration

### Phase 3: Advanced Features (2 weeks)
- Failsafe implementation
- Performance optimization
- Security hardening
- Monitoring setup

### Phase 4: AI Integration (Future)
- Smart routing algorithms
- Predictive maintenance
- Context-aware printing

---
**Status**: Frontend Complete | Backend Required
**Critical Path**: MCP server implementation blocks production deployment
**Workaround**: System gracefully degrades to file download