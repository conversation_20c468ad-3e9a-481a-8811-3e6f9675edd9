# Printing System Troubleshooting Guide

## Overview

This guide provides solutions for common printing issues in the Mini-ERP system, covering MCP connectivity, printer problems, label generation errors, and integration issues.

## Quick Diagnostics

### System Health Check

1. **Check MCP Status**:
   - Look for connection indicators in navigation bar
   - Green dots = Connected, Red = Disconnected
   - Navigate to Settings → MCP Printing → Overview

2. **Verify Printer Status**:
   ```typescript
   // In browser console
   const status = await fetch('http://localhost:3001/api/mcp/printers/status')
     .then(r => r.json());
   console.log(status);
   ```

3. **Check Print Queue**:
   - Settings → MCP Printing → Queue tab
   - Look for stuck or failed jobs

## Common Issues & Solutions

### 1. MCP Connection Issues

#### Symptom: Red connection indicator, "MCP Offline" message

**Possible Causes & Solutions**:

**A. MCP Server Not Running**
```bash
# Check if server is running
netstat -an | grep 3001

# Start MCP server
cd mcp-server
npm run dev
```

**B. Incorrect URL Configuration**
```bash
# Check .env file
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_WS_URL=ws://localhost:3001

# Restart frontend after changes
npm run dev
```

**C. Firewall Blocking Connection**
```bash
# Windows
netsh advfirewall firewall add rule name="MCP Server" 
  dir=in action=allow protocol=TCP localport=3001

# Linux
sudo ufw allow 3001/tcp
```

**D. CORS Issues**
```typescript
// In mcp-server/src/server.ts
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:3000',
    // Add your production URL
  ],
  credentials: true
}));
```

### 2. Printer Discovery Issues

#### Symptom: No printers found, empty printer list

**Solutions**:

**A. Manual Printer Check**
```bash
# Ping printer IP
ping *************

# Test Zebra port
telnet ************* 9100
```

**B. Network Configuration**
```typescript
// Add static printer if discovery fails
const staticPrinters = [
  {
    id: 'zebra_static_1',
    name: 'Warehouse Zebra ZT410',
    ip: '*************',
    port: 9100,
    model: 'ZT410',
    status: 'unknown'
  }
];
```

**C. Subnet Scanning Issues**
```typescript
// In printer-discovery.ts, adjust subnet
private getLocalSubnet(): string {
  // Manually set if auto-detection fails
  return process.env.PRINTER_SUBNET || '192.168.1';
}
```

### 3. Print Job Failures

#### Symptom: "Print failed" error, jobs stuck in queue

**A. ZPL Syntax Errors**
```typescript
// Validate ZPL before sending
const validateZPL = (zpl: string) => {
  const errors = [];
  
  if (!zpl.startsWith('^XA')) {
    errors.push('Missing ^XA start command');
  }
  if (!zpl.endsWith('^XZ')) {
    errors.push('Missing ^XZ end command');
  }
  if (zpl.length > 60000) {
    errors.push('ZPL too large (>60KB)');
  }
  
  return { valid: errors.length === 0, errors };
};
```

**B. Printer Communication Timeout**
```typescript
// Increase timeout in zebra-printer.ts
timeout = setTimeout(() => {
  cleanup();
  resolve({
    success: false,
    error: 'Print timeout - printer may be busy',
    timestamp: new Date()
  });
}, 60000); // Increased to 60 seconds
```

**C. Network Congestion**
```typescript
// Implement retry logic
const printWithRetry = async (printer, zpl, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    const result = await zebraPrinter.print(printer, zpl);
    if (result.success) return result;
    
    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
  }
  
  throw new Error('Print failed after retries');
};
```

### 4. Label Generation Issues

#### Symptom: Blank labels, incorrect formatting, missing data

**A. Dynamic Field Issues**
```typescript
// Debug dynamic field replacement
const debugDynamicFields = (template: string, data: any) => {
  console.log('Template:', template);
  console.log('Data:', data);
  
  const result = template.replace(/{([^}]+)}/g, (match, key) => {
    const value = data[key];
    console.log(`Replacing ${match} with ${value}`);
    return value || match;
  });
  
  console.log('Result:', result);
  return result;
};
```

**B. Canvas-to-ZPL Conversion**
```typescript
// Test ZPL generation
const testZPLGeneration = () => {
  const generator = new ZPLGenerator({
    width: 102,
    height: 152,
    unit: 'mm'
  });
  
  const testCanvas = {
    objects: [
      {
        type: 'text',
        text: 'TEST LABEL',
        left: 50,
        top: 50,
        fontSize: 30
      }
    ]
  };
  
  const zpl = generator.generateFromCanvas(testCanvas);
  console.log('Generated ZPL:', zpl);
  
  // Preview in Labelary
  window.open(
    `http://labelary.com/viewer.html?zpl=${encodeURIComponent(zpl)}`
  );
};
```

**C. Font Size Issues**
```typescript
// Adjust font scaling
private calculateZPLFontSize(canvasFontSize: number): number {
  // Adjust scaling factor based on DPI
  const scaleFactor = this.dpi / 96;
  
  // Add minimum/maximum limits
  const zplSize = Math.round(canvasFontSize * scaleFactor * 0.8);
  return Math.max(10, Math.min(200, zplSize));
}
```

### 5. WebSocket Connection Issues

#### Symptom: Intermittent disconnections, no real-time updates

**A. Implement Reconnection Logic**
```typescript
// In useMCPPrinting.ts
const connectWebSocket = () => {
  ws.current = new WebSocket(wsUrl);
  
  ws.current.onclose = () => {
    setIsConnected(false);
    
    // Reconnect after delay
    reconnectTimeout.current = setTimeout(() => {
      if (reconnectAttempts.current < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts.current++;
        connectWebSocket();
      }
    }, RECONNECT_DELAY * reconnectAttempts.current);
  };
  
  ws.current.onopen = () => {
    setIsConnected(true);
    reconnectAttempts.current = 0;
  };
};
```

**B. Heartbeat Implementation**
```typescript
// Keep connection alive
const startHeartbeat = () => {
  heartbeatInterval.current = setInterval(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify({ type: 'ping' }));
    }
  }, 30000);
};
```

### 6. Template Management Issues

#### Symptom: Templates not saving, canvas data corruption

**A. Canvas Data Size**
```typescript
// Compress canvas data before saving
const compressCanvasData = (data: any) => {
  // Remove redundant properties
  const compressed = JSON.parse(JSON.stringify(data));
  
  compressed.objects?.forEach((obj: any) => {
    delete obj.canvas;
    delete obj._element;
    delete obj.cacheCanvas;
  });
  
  return compressed;
};

// Check size before save
const canvasDataSize = new Blob([JSON.stringify(canvasData)]).size;
if (canvasDataSize > 1048576) { // 1MB
  toast.error('Template too complex. Please simplify design.');
  return;
}
```

**B. Template Loading Issues**
```typescript
// Safe template loading
const loadTemplateSafely = async (templateId: string) => {
  try {
    const template = await getTemplate(templateId);
    
    if (!template.canvas_data) {
      throw new Error('Template has no canvas data');
    }
    
    // Validate canvas data
    if (typeof template.canvas_data === 'string') {
      template.canvas_data = JSON.parse(template.canvas_data);
    }
    
    return template;
  } catch (error) {
    console.error('Template load error:', error);
    toast.error('Failed to load template');
    return null;
  }
};
```

### 7. Performance Issues

#### Symptom: Slow printing, UI freezes, high memory usage

**A. Batch Processing Optimization**
```typescript
// Process prints in chunks
const batchPrint = async (jobs: PrintJob[], batchSize = 5) => {
  const results = [];
  
  for (let i = 0; i < jobs.length; i += batchSize) {
    const batch = jobs.slice(i, i + batchSize);
    
    // Show progress
    const progress = Math.round((i / jobs.length) * 100);
    updateProgress(progress);
    
    // Process batch
    const batchResults = await Promise.allSettled(
      batch.map(job => processJob(job))
    );
    
    results.push(...batchResults);
    
    // Prevent UI blocking
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return results;
};
```

**B. Memory Leak Prevention**
```typescript
// Clean up resources
useEffect(() => {
  return () => {
    // Close WebSocket
    ws.current?.close();
    
    // Clear intervals
    clearInterval(heartbeatInterval.current);
    clearTimeout(reconnectTimeout.current);
    
    // Clear cache
    printerCache.current.clear();
    
    // Revoke object URLs
    previewUrls.forEach(url => URL.revokeObjectURL(url));
  };
}, []);
```

## Advanced Debugging

### Enable Debug Mode

**Frontend Debug**:
```typescript
// In main.tsx
if (import.meta.env.DEV) {
  window.DEBUG_MCP = true;
  
  // Log all MCP operations
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    if (args[0]?.includes('/mcp/')) {
      console.log('MCP Request:', args);
    }
    const response = await originalFetch(...args);
    if (args[0]?.includes('/mcp/')) {
      const clone = response.clone();
      console.log('MCP Response:', await clone.json());
    }
    return response;
  };
}
```

**Backend Debug**:
```typescript
// In server.ts
if (process.env.DEBUG === 'true') {
  // Log all operations
  app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
    console.log('Body:', req.body);
    next();
  });
  
  // Log WebSocket messages
  wss.on('connection', (ws) => {
    ws.on('message', (data) => {
      console.log('WS Message:', data.toString());
    });
  });
}
```

### Network Analysis

**Capture ZPL Traffic**:
```bash
# Using tcpdump (Linux/Mac)
sudo tcpdump -i any -A -s 0 'port 9100'

# Using Wireshark (Windows)
# Filter: tcp.port == 9100
```

**Test Direct Printer Connection**:
```bash
# Send test ZPL directly
echo "^XA^FO50,50^A0N,30,30^FDTest Print^FS^XZ" | 
  nc ************* 9100
```

## Error Recovery Procedures

### 1. Clear Stuck Print Queue

```typescript
// Emergency queue clear
const clearStuckJobs = async () => {
  const response = await fetch('/api/mcp/queue/clear-stuck', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY
    },
    body: JSON.stringify({
      olderThan: 3600000, // 1 hour
      status: ['pending', 'processing']
    })
  });
  
  const result = await response.json();
  console.log(`Cleared ${result.count} stuck jobs`);
};
```

### 2. Reset Printer Connection

```typescript
// Force printer reconnection
const resetPrinter = async (printerId: string) => {
  // Remove from cache
  await fetch(`/api/mcp/printers/${printerId}/reset`, {
    method: 'POST'
  });
  
  // Force rediscovery
  await fetch('/api/mcp/printers/discover', {
    method: 'POST',
    body: JSON.stringify({ force: true })
  });
};
```

### 3. Fallback Print Methods

```typescript
// Multiple fallback levels
const printWithFallbacks = async (data: PrintData) => {
  const methods = [
    () => printViaMCP(data),
    () => printDirectTCP(data),
    () => generateAndDownloadZPL(data),
    () => generatePDFLabel(data)
  ];
  
  for (const method of methods) {
    try {
      const result = await method();
      if (result.success) return result;
    } catch (error) {
      console.warn(`Method failed:`, error);
    }
  }
  
  throw new Error('All print methods failed');
};
```

## Preventive Maintenance

### Daily Checks
1. Monitor print success rate in dashboard
2. Check for error patterns in logs
3. Verify printer paper/ribbon levels

### Weekly Tasks
1. Clear old print jobs from queue
2. Update printer firmware if available
3. Test failover scenarios

### Monthly Tasks
1. Review and optimize slow queries
2. Archive old print logs
3. Update MCP server dependencies

## Common Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "MCP_CONNECTION_FAILED" | Server not running | Start MCP server |
| "PRINTER_OFFLINE" | Network issue | Check printer connectivity |
| "INVALID_ZPL" | Syntax error | Validate ZPL format |
| "QUEUE_FULL" | Too many jobs | Clear old jobs |
| "TEMPLATE_TOO_LARGE" | Complex design | Simplify template |
| "WEBSOCKET_CLOSED" | Connection lost | Check network stability |

## Contact Support

If issues persist after trying these solutions:

1. Collect diagnostic information:
   ```bash
   npm run diagnostics > diagnostics.log
   ```

2. Include in support request:
   - Error messages and screenshots
   - Browser console logs
   - MCP server logs
   - Network configuration
   - Printer models and firmware versions

---
**Last Updated**: 12/06/2025, 8:15 PM IST
**Version**: 1.0
**Applies To**: MCP Printing System v1.x