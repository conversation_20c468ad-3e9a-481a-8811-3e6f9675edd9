# Print Integration Points

## Overview

The printing system is deeply integrated throughout the Mini-ERP application, providing seamless access to label and document printing from multiple workflows. This document details all integration points and their implementation.

## Integration Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         Print Integration Points                              │
├───────────────┬─────────────┬─────────────┬──────────────┬──────────────────┤
│ CT Assignment │ Orders Page │ Quick Print │ Label        │ Print Navigation │
│ Modal         │             │ Modal/Page  │ Designer     │ Menu             │
├───────────────┼─────────────┼─────────────┼──────────────┼──────────────────┤
│ Immediate     │ Context     │ Standalone  │ Test Print   │ • Template Mgr   │
│ Print After   │ Aware       │ Generic     │ Template     │ • Quick Print    │
│ Assignment    │ Printing    │ Labels      │ Testing      │ • Print Queue    │
│               │             │             │              │ • MCP Settings   │
└───────────────┴─────────────┴─────────────┴──────────────┴──────────────────┘
                                    ↓
                           ┌──────────────────────┐
                           │    useMCPPrinting     │
                           │  (Central Print Hook) │
                           └──────────────────────┘
```

## 1. CT Assignment Modal Integration

### Location: `/src/components/orders/CTNumberModal.tsx`

#### Implementation Details

**Print Section UI**:
```tsx
{showPrintSection && savedCTNumbers.length > 0 && (
  <div className="space-y-4 border-t pt-4 mt-4">
    <h4 className="text-sm font-medium">Print CT Labels</h4>
    
    {/* Template Selection */}
    <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
      <SelectTrigger>
        <SelectValue placeholder="Select label template" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="default-ct-label">Default CT Label (102x152mm)</SelectItem>
        <SelectItem value="compact-ct-label">Compact CT Label (51x25mm)</SelectItem>
        <SelectItem value="hp-ct-label">HP CT Label (102x152mm)</SelectItem>
      </SelectContent>
    </Select>
    
    {/* Printer Selection with MCP Status */}
    <div className="space-y-2">
      <Select 
        value={selectedPrinter?.id || ''} 
        onValueChange={handlePrinterChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Select printer">
            {selectedPrinter && (
              <span className="flex items-center gap-2">
                {getPrinterStatusIcon(selectedPrinter.status)}
                {selectedPrinter.name}
              </span>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {printers.map(printer => (
            <SelectItem key={printer.id} value={printer.id}>
              <span className="flex items-center gap-2">
                {getPrinterStatusIcon(printer.status)}
                {printer.name}
                {printer.isDefault && (
                  <Badge variant="secondary" className="ml-2">Default</Badge>
                )}
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* MCP Connection Status */}
      <MCPConnectionStatus className="text-xs" />
    </div>
    
    {/* Quantity Control */}
    <div className="flex items-center gap-2">
      <Label>Quantity:</Label>
      <Input
        type="number"
        value={printQuantity}
        onChange={(e) => setPrintQuantity(Math.min(
          parseInt(e.target.value) || 1,
          savedCTNumbers.length
        ))}
        min="1"
        max={savedCTNumbers.length}
        className="w-20"
      />
      <span className="text-sm text-muted-foreground">
        of {savedCTNumbers.length} labels
      </span>
    </div>
    
    {/* Print Actions */}
    <div className="flex gap-2">
      <Button 
        onClick={handlePrint}
        disabled={!selectedPrinter || printLoading}
        className="flex-1"
      >
        {printLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Printing...
          </>
        ) : (
          <>
            <Printer className="mr-2 h-4 w-4" />
            {workflowOption === 'auto' ? 'Print & Auto Progress' : 'Print Labels'}
          </>
        )}
      </Button>
      
      <Button
        variant="outline"
        onClick={handlePreview}
        disabled={!selectedTemplate}
      >
        <Eye className="mr-2 h-4 w-4" />
        Preview
      </Button>
    </div>
  </div>
)}
```

**Print Handler**:
```typescript
const handlePrint = async () => {
  if (!selectedPrinter || savedCTNumbers.length === 0) return;
  
  setPrintLoading(true);
  try {
    // Generate ZPL for each CT
    const printJobs = savedCTNumbers.slice(0, printQuantity).map(ct => ({
      template: selectedTemplate,
      data: {
        orderLine,
        ctNumber: ct,
        printDate: new Date(),
        user: user?.email,
        location
      }
    }));
    
    // Execute print jobs
    for (const job of printJobs) {
      const zpl = generateLabelWithData(job.template, job.data.orderLine, job.data);
      
      const metadata = {
        order_uid: orderLine.uid,
        ct_number: job.data.ctNumber,
        template_name: job.template,
        created_by: user?.email || 'unknown',
        part_number: orderLine.customer_part_number
      };
      
      const result = await printZebraLabel(selectedPrinter, zpl, metadata);
      
      if (!result.success) {
        throw new Error(result.error || 'Print failed');
      }
    }
    
    toast.success(`Successfully printed ${printQuantity} CT labels`);
    
    // Handle workflow progression
    if (workflowOption === 'auto') {
      await handleAutoProgress();
    }
    
    // Close modal after successful print
    if (workflowOption !== 'manual') {
      onClose();
    }
  } catch (error) {
    console.error('Print error:', error);
    toast.error('Failed to print labels. ZPL file downloaded as fallback.');
    
    // Fallback: Download ZPL
    const zpl = generateLabelWithData(selectedTemplate, orderLine, {
      ctNumber: savedCTNumbers[0],
      printDate: new Date()
    });
    downloadZPL(zpl, `CT_${savedCTNumbers[0]}.zpl`);
  } finally {
    setPrintLoading(false);
  }
};
```

**Workflow Integration**:
```typescript
const handleAutoProgress = async () => {
  try {
    const result = await transitionQuantity({
      orderLineId: orderLine.id,
      fromState: 'awaiting_kitting_packing',
      toState: 'in_kitting_packing',
      quantity: savedCTNumbers.length,
      reason: `Automatic transition after CT assignment and label printing`,
      ctNumbers: savedCTNumbers,
      metadata: { 
        trigger: 'ct_assignment_completion',
        printed: true,
        template: selectedTemplate
      }
    });
    
    if (result.success) {
      toast.success(`Moved ${savedCTNumbers.length} units to "In Kitting"`);
    }
  } catch (error) {
    console.error('Auto progress error:', error);
    toast.error('Failed to progress workflow automatically');
  }
};
```

## 2. Orders Page Integration

### Location: `/src/pages/Orders.tsx`

#### Print Button Implementation

**Order Card Print Button**:
```tsx
// In OrderCard component
<Button
  size="sm"
  variant="outline"
  onClick={() => onPrintLabel?.(orderLine)}
  className="flex items-center gap-2"
>
  <Printer className="h-4 w-4" />
  Print
</Button>

// In Orders.tsx
const handlePrintLabel = (order: OrderLineWithDetails) => {
  setLabelPrintOrderId(order.id);
};

// Label Print Modal
{labelPrintOrderId && (
  <LabelPrintModal
    orderId={labelPrintOrderId}
    onClose={() => setLabelPrintOrderId(null)}
  />
)}
```

#### LabelPrintModal Component

**Location**: `/src/components/printing/LabelPrintModal.tsx`

```tsx
const LabelPrintModal: React.FC<Props> = ({ orderId, onClose }) => {
  const { data: orderLine } = useOrderLine(orderId);
  const { data: ctNumbers } = useOrderCTNumbers(orderId);
  const { printZebraLabel, useZebraPrinters, isConnected } = useMCPPrinting();
  
  const [printMode, setPrintMode] = useState<'template' | 'quick'>('template');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [selectedPrinter, setSelectedPrinter] = useState<Printer | null>(null);
  
  // Smart template selection based on order state
  useEffect(() => {
    if (ctNumbers && ctNumbers.length > 0) {
      setPrintMode('template');
      setSelectedTemplate('default-ct-label');
    } else {
      setPrintMode('quick');
      setSelectedTemplate('hp-external');
    }
  }, [ctNumbers]);
  
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Print Label</DialogTitle>
          <DialogDescription>
            {orderLine && (
              <div className="mt-2 space-y-1 text-sm">
                <p>Order: {orderLine.uid}</p>
                <p>Part: {orderLine.customer_part_number}</p>
                <p>Customer: {orderLine.customers?.name}</p>
                {ctNumbers && ctNumbers.length > 0 && (
                  <p className="text-green-600">
                    {ctNumbers.length} CT numbers assigned
                  </p>
                )}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Mode Toggle */}
          <Tabs value={printMode} onValueChange={setPrintMode}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger 
                value="template" 
                disabled={!ctNumbers || ctNumbers.length === 0}
              >
                CT Labels
              </TabsTrigger>
              <TabsTrigger value="quick">Quick Print</TabsTrigger>
            </TabsList>
            
            <TabsContent value="template">
              {/* CT label templates */}
            </TabsContent>
            
            <TabsContent value="quick">
              {/* Quick print templates */}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
```

## 3. Quick Print Modal

### Location: `/src/components/printing/QuickPrintModal.tsx`

#### Standalone Generic Printing

```tsx
const QuickPrintModal: React.FC<Props> = ({ onClose }) => {
  const { printZebraLabel, useZebraPrinters, isConnected } = useMCPPrinting();
  const printers = useZebraPrinters();
  
  const quickPrintTemplates = [
    { id: 'hp-external', name: 'HP ORDER - EXTERNAL BOX', icon: Package },
    { id: 'fragile', name: 'FRAGILE - HANDLE WITH CARE', icon: AlertTriangle },
    { id: 'priority', name: 'PRIORITY SHIPMENT', icon: Zap },
    { id: 'qc-hold', name: 'QC HOLD - DO NOT SHIP', icon: XCircle },
    { id: 'inspection', name: 'PARTS INSPECTION REQUIRED', icon: Search },
    { id: 'motherboard', name: 'MOTHERBOARD - STATIC SENSITIVE', icon: Cpu },
    { id: 'return', name: 'RETURN TO VENDOR', icon: RotateCcw }
  ];
  
  const handleQuickPrint = async () => {
    if (!selectedTemplate || !selectedPrinter) return;
    
    try {
      // Generate generic ZPL
      const template = labelTemplates.find(t => t.id === selectedTemplate);
      if (!template) throw new Error('Template not found');
      
      const zpl = template.generateZPL({});
      
      // Print multiple copies
      for (let i = 0; i < printQuantity; i++) {
        await printZebraLabel(selectedPrinter, zpl, {
          template: selectedTemplate,
          copy: i + 1,
          total: printQuantity,
          purpose: 'quick_print',
          timestamp: new Date().toISOString()
        });
      }
      
      toast.success(`Printed ${printQuantity} ${template.name} labels`);
      onClose();
    } catch (error) {
      console.error('Quick print error:', error);
      toast.error('Failed to print. Downloading ZPL as fallback.');
      downloadZPL(zpl, `${selectedTemplate}.zpl`);
    }
  };
  
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Quick Print Labels</DialogTitle>
          <DialogDescription>
            Select a template and quantity for immediate printing
          </DialogDescription>
        </DialogHeader>
        
        {/* Template Grid */}
        <div className="grid grid-cols-2 gap-3">
          {quickPrintTemplates.map(template => {
            const Icon = template.icon;
            return (
              <Card
                key={template.id}
                className={cn(
                  "cursor-pointer transition-all",
                  selectedTemplate === template.id && "ring-2 ring-primary"
                )}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <CardContent className="p-4">
                  <Icon className="h-8 w-8 mb-2 text-muted-foreground" />
                  <p className="text-sm font-medium">{template.name}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {/* Print Controls */}
        <div className="space-y-4">
          <PrinterSelector
            selectedPrinter={selectedPrinter}
            onPrinterChange={setSelectedPrinter}
            printers={printers}
          />
          
          <QuantitySelector
            quantity={printQuantity}
            onQuantityChange={setPrintQuantity}
            max={100}
          />
          
          <Button
            onClick={handleQuickPrint}
            disabled={!selectedTemplate || !selectedPrinter}
            className="w-full"
          >
            <Printer className="mr-2 h-4 w-4" />
            Print {printQuantity} Label{printQuantity > 1 ? 's' : ''}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
```

## 4. Label Designer Integration

### Location: `/src/pages/LabelDesignerPage.tsx`

#### Test Print Functionality

```tsx
const LabelDesignerPage: React.FC = () => {
  const { printZebraLabel, useZebraPrinters } = useMCPPrinting();
  const [showTestPrintDialog, setShowTestPrintDialog] = useState(false);
  
  const handleTestPrint = async () => {
    try {
      // Get current canvas data
      const canvasData = labelDesignerRef.current?.getCanvasData();
      if (!canvasData) throw new Error('No design to print');
      
      // Generate ZPL from canvas
      const generator = new ZPLGenerator(currentLabelSize);
      const zpl = generator.generateFromCanvas(canvasData, {
        ORDER_ID: 'TEST001',
        CUSTOMER_NAME: 'Test Customer',
        PART_NUMBER: 'TEST-PART-123',
        CT_NUMBER: 'CT12345678901234',
        QUANTITY: '10',
        DATE: format(new Date(), 'dd/MM/yyyy'),
        LOCATION: 'SB',
        USER_NAME: user?.email || 'Designer'
      });
      
      // Show printer selection dialog
      setTestPrintZPL(zpl);
      setShowTestPrintDialog(true);
    } catch (error) {
      console.error('Test print generation error:', error);
      toast.error('Failed to generate test print');
    }
  };
  
  return (
    <div className="h-screen flex flex-col">
      {/* Designer Header */}
      <div className="border-b p-4 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Label Designer</h1>
        <div className="flex gap-2">
          <Button onClick={handleTestPrint} variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Test Print
          </Button>
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Template
          </Button>
        </div>
      </div>
      
      {/* Label Designer Component */}
      <LabelDesigner
        ref={labelDesignerRef}
        initialCanvasData={templateData}
        labelSize={currentLabelSize}
        onSave={handleCanvasSave}
        isEmbedded={false}
        showSaveButton={false}
        showTestPrint={false}
      />
      
      {/* Test Print Dialog */}
      <TestPrintDialog
        open={showTestPrintDialog}
        onClose={() => setShowTestPrintDialog(false)}
        zpl={testPrintZPL}
        labelSize={currentLabelSize}
      />
    </div>
  );
};
```

## 5. Settings Administration

### Location: `/src/components/admin/MCPSettings.tsx`

#### MCP Configuration Interface

```tsx
const MCPSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="printers">Printers</TabsTrigger>
          <TabsTrigger value="queue">Print Queue</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <MCPOverview />
        </TabsContent>
        
        <TabsContent value="printers">
          <PrinterManagement />
        </TabsContent>
        
        <TabsContent value="queue">
          <PrintQueueManager />
        </TabsContent>
        
        <TabsContent value="templates">
          <TemplateManager />
        </TabsContent>
        
        <TabsContent value="config">
          <MCPConfiguration />
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

## Common Components

### PrinterSelector Component

```tsx
const PrinterSelector: React.FC<PrinterSelectorProps> = ({
  selectedPrinter,
  onPrinterChange,
  printers,
  showStatus = true,
  allowOffline = false
}) => {
  const filteredPrinters = allowOffline 
    ? printers 
    : printers.filter(p => p.status === 'online');
    
  return (
    <div className="space-y-2">
      <Label>Select Printer</Label>
      <Select
        value={selectedPrinter?.id || ''}
        onValueChange={(id) => {
          const printer = printers.find(p => p.id === id);
          if (printer) onPrinterChange(printer);
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder="Choose a printer">
            {selectedPrinter && (
              <span className="flex items-center gap-2">
                {showStatus && getPrinterStatusIcon(selectedPrinter.status)}
                {selectedPrinter.name}
              </span>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {filteredPrinters.map(printer => (
            <SelectItem key={printer.id} value={printer.id}>
              <span className="flex items-center gap-2">
                {showStatus && getPrinterStatusIcon(printer.status)}
                {printer.name}
                <span className="text-xs text-muted-foreground ml-2">
                  {printer.ip}
                </span>
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {selectedPrinter && showStatus && (
        <p className="text-xs text-muted-foreground">
          Status: {selectedPrinter.status} • 
          Response: {selectedPrinter.responseTime}ms
        </p>
      )}
    </div>
  );
};
```

### MCPConnectionStatus Component

```tsx
const MCPConnectionStatus: React.FC<{ className?: string }> = ({ className }) => {
  const { isConnected, connectionStatus } = useMCPPrinting();
  
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-muted-foreground">MCP:</span>
      <span title="Frontend to MCP Server">
        {connectionStatus.frontend === 'connected' ? '🟢' : '🔴'}
      </span>
      <span className="text-muted-foreground">→</span>
      <span title="MCP Server Status">
        {connectionStatus.server === 'online' ? '✅' : '❌'}
      </span>
      <span className="text-muted-foreground">→</span>
      <span title="Printer Connectivity">
        {connectionStatus.printers > 0 ? '🟢' : '🟡'}
      </span>
      {connectionStatus.printers > 0 && (
        <span className="text-xs text-muted-foreground">
          ({connectionStatus.printers} online)
        </span>
      )}
    </div>
  );
};
```

## Global Print Access

### Navigation Bar Integration

```tsx
// In MainLayout.tsx
<nav className="flex items-center justify-between p-4 border-b">
  <div className="flex items-center gap-4">
    {/* Logo and main nav */}
  </div>
  
  <div className="flex items-center gap-4">
    {/* Quick Actions */}
    <Button
      variant="outline"
      size="sm"
      onClick={() => setShowQuickPrint(true)}
    >
      <Printer className="h-4 w-4" />
    </Button>
    
    {/* MCP Status */}
    <MCPConnectionStatus className="text-xs" />
    
    {/* User menu */}
  </div>
</nav>
```

### Keyboard Shortcuts

```typescript
// Global keyboard shortcuts
useEffect(() => {
  const handleKeyPress = (e: KeyboardEvent) => {
    // Ctrl/Cmd + P for quick print
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
      e.preventDefault();
      setShowQuickPrint(true);
    }
    
    // Ctrl/Cmd + Shift + P for print queue
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
      e.preventDefault();
      navigate('/settings?tab=mcp&view=queue');
    }
  };
  
  window.addEventListener('keydown', handleKeyPress);
  return () => window.removeEventListener('keydown', handleKeyPress);
}, []);
```

## Performance Considerations

### Printer Status Caching

```typescript
// In useMCPPrinting hook
const printerCache = useRef<Map<string, CachedPrinter>>(new Map());

const updatePrinterCache = (printer: Printer) => {
  printerCache.current.set(printer.id, {
    ...printer,
    lastUpdated: Date.now()
  });
};

const getCachedPrinter = (id: string): Printer | null => {
  const cached = printerCache.current.get(id);
  if (!cached) return null;
  
  // Cache valid for 30 seconds
  if (Date.now() - cached.lastUpdated > 30000) {
    printerCache.current.delete(id);
    return null;
  }
  
  return cached;
};
```

### Batch Print Operations

```typescript
const batchPrint = async (jobs: PrintJob[]) => {
  const results = [];
  const batchSize = 5; // Process 5 jobs concurrently
  
  for (let i = 0; i < jobs.length; i += batchSize) {
    const batch = jobs.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(
      batch.map(job => printZebraLabel(
        job.printer,
        job.zpl,
        job.metadata
      ))
    );
    results.push(...batchResults);
  }
  
  return results;
};
```

## Error Handling Patterns

### Graceful Degradation

```typescript
const printWithFallback = async (
  printer: Printer,
  zpl: string,
  metadata: any
) => {
  try {
    // Try MCP printing
    if (isConnected) {
      return await printZebraLabel(printer, zpl, metadata);
    }
  } catch (error) {
    console.warn('MCP print failed, trying direct connection', error);
  }
  
  try {
    // Try direct printer connection
    return await printDirectToZebra(printer.ip, zpl);
  } catch (error) {
    console.warn('Direct print failed, downloading ZPL', error);
  }
  
  // Final fallback: download ZPL
  downloadZPL(zpl, `label_${Date.now()}.zpl`);
  return { 
    success: false, 
    error: 'All print methods failed. ZPL downloaded.' 
  };
};
```

## Future Integration Points

1. **Mobile App Integration**: QR code for mobile printing
2. **Bulk Operations**: Print entire batches from order lists
3. **Scheduled Printing**: Queue labels for specific times
4. **Print Analytics**: Track usage and optimize workflows
5. **Third-party Integration**: API endpoints for external systems

---
**Status**: All Major Workflows Integrated
**Coverage**: CT Assignment, Orders, Quick Print, Designer, Settings
**Next Steps**: Mobile integration and analytics dashboard