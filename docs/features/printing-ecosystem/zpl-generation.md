# ZPL Generation Systems

## Overview

The Mini-ERP system implements two parallel ZPL (Zebra Programming Language) generation systems: a production-ready predefined template system and an advanced canvas-to-ZPL converter. This dual architecture provides immediate printing capability while supporting future visual design integration.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                     ZPL Generation Systems                       │
├──────────────────────────────┬──────────────────────────────────┤
│   Production System (Active) │   Advanced System (Partial)      │
├──────────────────────────────┼──────────────────────────────────┤
│  labelGeneration.ts          │  zplGenerator.ts                 │
│  - 7 Predefined Templates    │  - Canvas Object Processing      │
│  - Dynamic Data Injection    │  - Shape Conversion Logic        │
│  - Direct Integration        │  - Coordinate Transformation     │
│  - Currently Used            │  - Not Fully Integrated          │
└──────────────────────────────┴──────────────────────────────────┘
```

## Production ZPL System

### File: `/src/utils/labelGeneration.ts`

#### Template Structure

```typescript
export interface LabelTemplate {
  id: string;
  name: string;
  generateZPL: (data: any) => string;
  size: { width: number; height: number; unit: 'mm' | 'inch' };
  category: 'ct_label' | 'quick_print';
}

export const labelTemplates: LabelTemplate[] = [
  {
    id: 'default-ct-label',
    name: 'Default CT Label',
    size: { width: 102, height: 152, unit: 'mm' },
    category: 'ct_label',
    generateZPL: (data) => generateDefaultCTLabel(data)
  },
  {
    id: 'hp-external',
    name: 'HP ORDER - EXTERNAL BOX',
    size: { width: 102, height: 152, unit: 'mm' },
    category: 'quick_print',
    generateZPL: () => generateHPExternalLabel()
  },
  // ... 5 more templates
];
```

#### Default CT Label Template

```typescript
const generateDefaultCTLabel = (data: {
  orderLine: OrderLineWithDetails;
  ctNumber: string;
  printDate: Date;
}) => {
  const { orderLine, ctNumber } = data;
  const date = format(data.printDate, 'dd/MM/yyyy');
  
  return `^XA
^FO50,50^A0N,40,40^FD${orderLine.customers?.name || 'Unknown Customer'}^FS
^FO50,100^A0N,30,30^FDUID: ${orderLine.uid}^FS
^FO50,150^A0N,30,30^FDPart: ${orderLine.customer_part_number || 'N/A'}^FS
^FO50,200^A0N,25,25^FD${orderLine.bpi_description || 'No Description'}^FS
^FO50,250^GB700,3,3^FS
^FO50,280^A0N,50,50^FDCT: ${ctNumber}^FS
^FO50,350^BY3,3,100^BCN,,Y,N^FD${ctNumber}^FS
^FO50,500^A0N,25,25^FDQty: ${orderLine.quantity}^FS
^FO400,500^A0N,25,25^FDDate: ${date}^FS
^FO50,550^BQN,2,10^FDQA,${ctNumber}|${orderLine.uid}|${orderLine.customer_part_number}^FS
^XZ`;
};
```

#### Quick Print Templates

```typescript
// HP External Box Label
const generateHPExternalLabel = () => {
  return `^XA
^FO200,100^A0N,80,80^FDHP ORDER^FS
^FO150,250^A0N,60,60^FDEXTERNAL BOX^FS
^FO50,400^GB700,3,3^FS
^FO100,450^A0N,40,40^FDHandle with Care - Computer Parts^FS
^FO150,550^A0N,35,35^FDShip To: ________________^FS
^FO150,650^A0N,35,35^FDDate: ___________________^FS
^XZ`;
};

// Fragile Label
const generateFragileLabel = () => {
  return `^XA
^FO50,50^GB700,700,3^FS
^FO200,200^A0N,100,100^FDFRAGILE^FS
^FO100,400^A0N,50,50^FDHANDLE WITH CARE^FS
^FO250,550^A0N,40,40^FD⚠ CAUTION ⚠^FS
^XZ`;
};

// QC Hold Label
const generateQCHoldLabel = () => {
  return `^XA
^FO50,50^GB700,400,8^FS
^FO150,150^A0N,70,70^FDQC HOLD^FS
^FO100,300^A0N,40,40^FDDO NOT SHIP^FS
^FO50,500^A0N,30,30^FDInspection Required: _________^FS
^FO50,600^A0N,30,30^FDInspector: _________________^FS
^FO50,700^A0N,30,30^FDDate: _____________________^FS
^XZ`;
};
```

#### Dynamic Data Integration

```typescript
export const generateLabelWithData = (
  templateId: string,
  orderData: OrderLineWithDetails,
  additionalData?: any
) => {
  const template = labelTemplates.find(t => t.id === templateId);
  if (!template) {
    throw new Error(`Template ${templateId} not found`);
  }
  
  // Prepare data context
  const context = {
    orderLine: orderData,
    ctNumber: additionalData?.ctNumber || '',
    printDate: new Date(),
    quantity: additionalData?.quantity || 1,
    user: additionalData?.user || 'System',
    location: additionalData?.location || 'SB'
  };
  
  // Generate ZPL with context
  return template.generateZPL(context);
};
```

## Advanced Canvas-to-ZPL System

### File: `/src/utils/zplGenerator.ts`

#### ZPLGenerator Class

```typescript
export class ZPLGenerator {
  private dpi: number;
  private labelWidth: number;
  private labelHeight: number;
  private originX: number = 0;
  private originY: number = 0;

  constructor(labelSize: LabelSize, dpi: number = 203) {
    this.dpi = dpi;
    this.labelWidth = this.convertToPixels(labelSize.width, labelSize.unit);
    this.labelHeight = this.convertToPixels(labelSize.height, labelSize.unit);
  }

  private convertToPixels(value: number, unit: 'mm' | 'inch'): number {
    if (unit === 'mm') {
      return Math.round((value / 25.4) * this.dpi);
    }
    return Math.round(value * this.dpi);
  }

  private mmToPixels(mm: number): number {
    return Math.round((mm / 25.4) * this.dpi);
  }

  generateFromCanvas(canvasData: any, dynamicData: any = {}): string {
    let zpl = '^XA\n';
    zpl += `^PW${this.labelWidth}\n`;
    zpl += `^LL${this.labelHeight}\n`;
    
    const objects = canvasData.objects || [];
    
    // Process objects in order
    objects.forEach((obj: any) => {
      try {
        switch (obj.type) {
          case 'text':
          case 'textbox':
            zpl += this.generateTextZPL(obj, dynamicData);
            break;
          case 'rect':
            zpl += this.generateRectangleZPL(obj);
            break;
          case 'circle':
            zpl += this.generateCircleZPL(obj);
            break;
          case 'line':
            zpl += this.generateLineZPL(obj);
            break;
          case 'image':
            if (obj.barcodeFormat) {
              zpl += this.generateBarcodeZPL(obj, dynamicData);
            } else if (obj.qrData) {
              zpl += this.generateQRCodeZPL(obj, dynamicData);
            }
            break;
        }
      } catch (error) {
        console.error(`Error processing ${obj.type}:`, error);
      }
    });
    
    zpl += '^XZ';
    return zpl;
  }
}
```

#### Text Processing

```typescript
private generateTextZPL(obj: any, dynamicData: any): string {
  const x = Math.round(obj.left);
  const y = Math.round(obj.top);
  const text = this.processTextContent(obj.text, dynamicData);
  const fontSize = this.calculateZPLFontSize(obj.fontSize);
  
  // Handle text alignment
  let alignment = '';
  switch (obj.textAlign) {
    case 'center': alignment = ',1'; break;
    case 'right': alignment = ',2'; break;
    default: alignment = ',0'; break;
  }
  
  // Handle rotation
  let rotation = 'N';
  if (obj.angle) {
    if (obj.angle >= 315 || obj.angle < 45) rotation = 'N';
    else if (obj.angle >= 45 && obj.angle < 135) rotation = 'R';
    else if (obj.angle >= 135 && obj.angle < 225) rotation = 'I';
    else rotation = 'B';
  }
  
  return `^FO${x},${y}^A0${rotation},${fontSize},${fontSize}^FD${text}^FS\n`;
}

private processTextContent(text: string, dynamicData: any): string {
  // Replace dynamic placeholders
  return text.replace(/\{([^}]+)\}/g, (match, key) => {
    const keys = key.split('.');
    let value = dynamicData;
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value !== undefined ? String(value) : match;
  });
}

private calculateZPLFontSize(canvasFontSize: number): number {
  // Convert canvas font size to ZPL font size
  // ZPL font sizes are in dots, canvas in pixels
  const scaleFactor = this.dpi / 96; // 96 DPI is typical screen resolution
  return Math.round(canvasFontSize * scaleFactor * 0.8);
}
```

#### Shape Processing

```typescript
private generateRectangleZPL(obj: any): string {
  const x = Math.round(obj.left);
  const y = Math.round(obj.top);
  const width = Math.round(obj.width * obj.scaleX);
  const height = Math.round(obj.height * obj.scaleY);
  const thickness = Math.round(obj.strokeWidth) || 1;
  
  if (obj.fill && obj.fill !== 'transparent') {
    // Filled rectangle
    return `^FO${x},${y}^GB${width},${height},${thickness},B^FS\n`;
  } else {
    // Outline only
    return `^FO${x},${y}^GB${width},${height},${thickness}^FS\n`;
  }
}

private generateCircleZPL(obj: any): string {
  // ZPL doesn't have native circle support, approximate with rounded rectangle
  const x = Math.round(obj.left - obj.radius);
  const y = Math.round(obj.top - obj.radius);
  const diameter = Math.round(obj.radius * 2 * obj.scaleX);
  const thickness = Math.round(obj.strokeWidth) || 1;
  
  // Use graphic circle (GC) command if available in printer firmware
  return `^FO${x},${y}^GC${diameter},${thickness},B^FS\n`;
}

private generateLineZPL(obj: any): string {
  const x1 = Math.round(obj.x1);
  const y1 = Math.round(obj.y1);
  const x2 = Math.round(obj.x2);
  const y2 = Math.round(obj.y2);
  const thickness = Math.round(obj.strokeWidth) || 1;
  
  if (x1 === x2) {
    // Vertical line
    const height = Math.abs(y2 - y1);
    return `^FO${x1},${Math.min(y1, y2)}^GB${thickness},${height},${thickness}^FS\n`;
  } else if (y1 === y2) {
    // Horizontal line
    const width = Math.abs(x2 - x1);
    return `^FO${Math.min(x1, x2)},${y1}^GB${width},${thickness},${thickness}^FS\n`;
  } else {
    // Diagonal line - use graphic diagonal (GD) if supported
    return `^FO${x1},${y1}^GD${x2},${y2},${thickness},${thickness}^FS\n`;
  }
}
```

#### Barcode Processing

```typescript
private generateBarcodeZPL(obj: any, dynamicData: any): string {
  const x = Math.round(obj.left);
  const y = Math.round(obj.top);
  const data = this.processTextContent(obj.data || '123456789', dynamicData);
  const format = obj.barcodeFormat || 'CODE128';
  
  switch (format) {
    case 'CODE128':
      return this.generateCode128(x, y, data, obj);
    case 'CODE39':
      return this.generateCode39(x, y, data, obj);
    case 'EAN13':
      return this.generateEAN13(x, y, data, obj);
    default:
      return this.generateCode128(x, y, data, obj);
  }
}

private generateCode128(x: number, y: number, data: string, options: any): string {
  const height = Math.round(options.height * options.scaleY) || 100;
  const width = Math.round(options.width * options.scaleX / data.length) || 2;
  const showText = options.displayValue !== false ? 'Y' : 'N';
  
  return `^FO${x},${y}^BY${width},3,${height}^BCN,,${showText},N^FD${data}^FS\n`;
}

private generateQRCodeZPL(obj: any, dynamicData: any): string {
  const x = Math.round(obj.left);
  const y = Math.round(obj.top);
  const data = this.processTextContent(obj.qrData || 'QR', dynamicData);
  const size = Math.round(Math.min(obj.width, obj.height) * obj.scaleX / 50) || 4;
  
  return `^FO${x},${y}^BQN,2,${size}^FDQA,${data}^FS\n`;
}
```

## Integration Architecture

### Current Integration Flow

```typescript
// In CTNumberModal.tsx
const generateAndPrintLabel = async () => {
  // Use production system
  const zpl = generateLabelWithData(
    selectedTemplate,
    orderLine,
    {
      ctNumber: savedCTNumbers[0],
      quantity: printQuantity,
      user: user?.email,
      location: location
    }
  );
  
  // Send to MCP for printing
  await printZebraLabel(selectedPrinter, zpl, metadata);
};
```

### Planned Canvas Integration

```typescript
// Future integration
const generateFromDesigner = async (templateId: string) => {
  // Load template from database
  const template = await loadTemplate(templateId);
  
  if (template.canvas_data) {
    // Use advanced system
    const generator = new ZPLGenerator(template.label_size);
    const zpl = generator.generateFromCanvas(
      template.canvas_data,
      dynamicData
    );
    
    // Cache generated ZPL
    await updateTemplate(templateId, { zpl_template: zpl });
    
    return zpl;
  } else if (template.zpl_template) {
    // Use cached ZPL
    return template.zpl_template;
  } else {
    // Fallback to production system
    return generateLabelWithData('default', orderData);
  }
};
```

## ZPL Command Reference

### Essential Commands Used

| Command | Description | Example |
|---------|-------------|------|
| ^XA | Start format | ^XA |
| ^XZ | End format | ^XZ |
| ^FO | Field origin | ^FO50,100 |
| ^FD | Field data | ^FDHello World^FS |
| ^FS | Field separator | ^FS |
| ^A0 | Scalable font | ^A0N,40,40 |
| ^GB | Graphic box | ^GB700,3,3^FS |
| ^BC | Code 128 barcode | ^BCN,,Y,N |
| ^BQ | QR code | ^BQN,2,10 |
| ^BY | Barcode defaults | ^BY3,3,100 |
| ^PW | Print width | ^PW800 |
| ^LL | Label length | ^LL1200 |

### Font Rotation Codes
- N = Normal (0°)
- R = Rotate 90°
- I = Inverted (180°)
- B = Bottom up (270°)

## Labelary Integration

### Preview Generation

```typescript
export const generateLabelPreview = async (
  zpl: string,
  labelSize: LabelSize,
  dpi: number = 203
): Promise<string> => {
  const width = labelSize.unit === 'inch' 
    ? labelSize.width 
    : labelSize.width / 25.4;
  const height = labelSize.unit === 'inch' 
    ? labelSize.height 
    : labelSize.height / 25.4;
    
  const url = `http://api.labelary.com/v1/printers/${dpi}dpi/labels/${width}x${height}/0/`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'image/png'
      },
      body: zpl
    });
    
    if (response.ok) {
      const blob = await response.blob();
      return URL.createObjectURL(blob);
    }
    
    throw new Error('Preview generation failed');
  } catch (error) {
    console.error('Labelary preview error:', error);
    return '';
  }
};
```

## Performance Optimization

### ZPL Caching

```typescript
const zplCache = new Map<string, string>();

export const getCachedZPL = (
  templateId: string,
  dataHash: string
): string | null => {
  const cacheKey = `${templateId}-${dataHash}`;
  return zplCache.get(cacheKey) || null;
};

export const setCachedZPL = (
  templateId: string,
  dataHash: string,
  zpl: string
): void => {
  const cacheKey = `${templateId}-${dataHash}`;
  zplCache.set(cacheKey, zpl);
  
  // Limit cache size
  if (zplCache.size > 100) {
    const firstKey = zplCache.keys().next().value;
    zplCache.delete(firstKey);
  }
};
```

### Batch ZPL Generation

```typescript
export const generateBatchZPL = (
  items: Array<{ template: string; data: any }>
): string => {
  let batchZPL = '';
  
  items.forEach((item, index) => {
    const zpl = generateLabelWithData(item.template, item.data);
    batchZPL += zpl;
    
    // Add form feed between labels except last
    if (index < items.length - 1) {
      batchZPL += '^XA^MMT^PW800^LL200^XZ'; // Minimal separator
    }
  });
  
  return batchZPL;
};
```

## Error Handling

### ZPL Validation

```typescript
export const validateZPL = (zpl: string): ValidationResult => {
  const errors: string[] = [];
  
  // Check basic structure
  if (!zpl.startsWith('^XA')) {
    errors.push('ZPL must start with ^XA');
  }
  if (!zpl.endsWith('^XZ')) {
    errors.push('ZPL must end with ^XZ');
  }
  
  // Check for common issues
  const lines = zpl.split('\n');
  lines.forEach((line, index) => {
    // Check field closure
    if (line.includes('^FD') && !line.includes('^FS')) {
      errors.push(`Line ${index + 1}: Field data missing ^FS terminator`);
    }
    
    // Check coordinate bounds
    const coordMatch = line.match(/\^FO(\d+),(\d+)/);
    if (coordMatch) {
      const x = parseInt(coordMatch[1]);
      const y = parseInt(coordMatch[2]);
      if (x > 800 || y > 1200) {
        errors.push(`Line ${index + 1}: Coordinates out of bounds`);
      }
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
};
```

## Testing Utilities

### Mock ZPL Generator

```typescript
export const generateMockZPL = (): string => {
  return `^XA
^FO50,50^A0N,30,30^FDTEST LABEL - ${new Date().toISOString()}^FS
^FO50,100^GB700,3,3^FS
^FO50,150^BY2,3,100^BCN,,Y,N^FD123456789^FS
^XZ`;
};
```

### ZPL Comparison

```typescript
export const compareZPL = (zpl1: string, zpl2: string): boolean => {
  // Normalize whitespace and line endings
  const normalize = (zpl: string) => 
    zpl.replace(/\s+/g, ' ').replace(/\r\n/g, '\n').trim();
    
  return normalize(zpl1) === normalize(zpl2);
};
```

## Future Enhancements

1. **Complete Canvas Integration**: Connect visual designer to ZPL generator
2. **Advanced Graphics**: Support for images and complex shapes
3. **Template Optimization**: Minimize ZPL size for faster printing
4. **Error Recovery**: Automatic ZPL correction for common issues
5. **Multi-Language Support**: Unicode text handling

---
**Status**: Production System Active | Canvas System Partial
**Current Usage**: Predefined templates for all printing
**Future Goal**: Seamless visual-to-ZPL conversion