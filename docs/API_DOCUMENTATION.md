# Mini-ERP API Documentation

## 📚 Overview

This document provides comprehensive API documentation for the Mini-ERP Order Management QC App. The system uses Supabase as the primary backend with additional MCP (Model Context Protocol) services for printing and advanced functionality.

## 🏗️ Architecture

### Backend Services
1. **Supabase PostgreSQL**: Primary database and real-time subscriptions
2. **Supabase Auth**: Authentication and authorization
3. **Supabase Storage**: File storage for FAI documents and images
4. **MCP Printing Server**: Local printing management (Express.js + SQLite)
5. **N8N Webhooks**: WhatsApp integration and workflow automation

### Authentication
All API endpoints require authentication via Supabase Auth tokens unless otherwise specified.

```javascript
// Authentication header
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📊 Database API (Supabase)

### Base URL
```
https://qeozkzbjvvkgsvtitbny.supabase.co/rest/v1/
```

### Orders API

#### Get Orders
```http
GET /order_lines?select=*,customers(name),product_categories(name)&order=created_at.desc
```

**Parameters:**
- `limit`: Number of records (default: 50)
- `offset`: Pagination offset
- `customer_id`: Filter by customer UUID
- `current_status`: Filter by order status

**Example Response:**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "uid": "A001",
    "customer_id": "456e7890-e89b-12d3-a456-************",
    "part_number": "HP-PART-001",
    "description": "HP Laptop Motherboard",
    "total_order_quantity": 10,
    "unit_price": 299.99,
    "current_status": "pending_procurement_arrangement",
    "created_at": "2025-01-10T10:00:00Z",
    "customers": {
      "name": "HP Inc."
    },
    "product_categories": {
      "name": "Motherboards"
    }
  }
]
```

#### Create Order
```http
POST /order_lines
Content-Type: application/json
```

**Request Body:**
```json
{
  "uid": "A002",
  "customer_id": "456e7890-e89b-12d3-a456-************",
  "part_number": "HP-PART-002",
  "description": "HP Laptop Screen",
  "total_order_quantity": 5,
  "unit_price": 149.99,
  "product_category_id": "789e0123-e89b-12d3-a456-************",
  "current_status": "pending_procurement_arrangement"
}
```

#### Update Order
```http
PATCH /order_lines?id=eq.123e4567-e89b-12d3-a456-************
Content-Type: application/json
```

**Request Body:**
```json
{
  "current_status": "awaiting_kitting_packing",
  "updated_at": "2025-01-10T11:00:00Z"
}
```

### CT Numbers API

#### Get CT Numbers
```http
GET /ct_numbers?select=*,order_lines(uid,part_number)&order=assigned_date.desc
```

#### Assign CT Number
```http
POST /ct_numbers
Content-Type: application/json
```

**Request Body:**
```json
{
  "ct_number": "ABC123DEF45678",
  "order_line_id": "123e4567-e89b-12d3-a456-************",
  "assigned_date": "2025-01-10T12:00:00Z"
}
```

#### Validate CT Number
```http
GET /ct_numbers?ct_number=eq.ABC123DEF45678
```

### Quantities API

#### Get Order Quantities
```http
GET /order_line_quantities?order_line_id=eq.123e4567-e89b-12d3-a456-************
```

#### Update Quantities
```http
POST /order_line_quantities
Content-Type: application/json
```

**Request Body:**
```json
{
  "order_line_id": "123e4567-e89b-12d3-a456-************",
  "status": "in_kitting_packing",
  "quantity": 3,
  "updated_at": "2025-01-10T13:00:00Z"
}
```

### Users & Authentication API

#### Get Current User
```http
GET /auth/v1/user
```

#### Get Users (Admin Only)
```http
GET /users?select=*,roles(name)&order=created_at.desc
```

#### Update User Role
```http
PATCH /users?id=eq.user_id
Content-Type: application/json
```

**Request Body:**
```json
{
  "role_id": "role_uuid",
  "updated_at": "2025-01-10T14:00:00Z"
}
```

### Real-time Subscriptions

#### Subscribe to Order Changes
```javascript
const subscription = supabase
  .channel('order_changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'order_lines'
    },
    (payload) => {
      console.log('Order change:', payload)
    }
  )
  .subscribe()
```

#### Subscribe to CT Number Changes
```javascript
const subscription = supabase
  .channel('ct_changes')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'ct_numbers'
    },
    (payload) => {
      console.log('New CT assignment:', payload)
    }
  )
  .subscribe()
```

## 🖨️ MCP Printing API

### Base URL
```
http://localhost:3001/api/mcp
```

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-10T15:00:00Z",
  "printers": 5,
  "queue_size": 2
}
```

### Printer Discovery

#### Get Available Printers
```http
GET /printers
```

**Response:**
```json
[
  {
    "id": "zebra_001",
    "name": "Zebra ZT230",
    "ip_address": "************0",
    "type": "zebra",
    "status": "online",
    "last_seen": "2025-01-10T15:00:00Z"
  }
]
```

#### Discover New Printers
```http
POST /printers/discover
```

### Print Jobs

#### Submit Print Job
```http
POST /print
Content-Type: application/json
```

**Request Body:**
```json
{
  "printer_id": "zebra_001",
  "template_type": "ct_label",
  "data": {
    "ct_number": "ABC123DEF45678",
    "part_number": "HP-PART-001",
    "customer": "HP Inc.",
    "quantity": 1
  },
  "copies": 1
}
```

**Response:**
```json
{
  "job_id": "job_123456789",
  "status": "queued",
  "printer_id": "zebra_001",
  "created_at": "2025-01-10T15:00:00Z"
}
```

#### Get Print Job Status
```http
GET /print/job/{job_id}
```

**Response:**
```json
{
  "job_id": "job_123456789",
  "status": "completed",
  "printer_id": "zebra_001",
  "created_at": "2025-01-10T15:00:00Z",
  "completed_at": "2025-01-10T15:00:30Z"
}
```

#### Get Print Queue
```http
GET /print/queue
```

### Label Templates

#### Get Templates
```http
GET /templates
```

#### Create Template
```http
POST /templates
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Custom CT Label",
  "category": "ct_labels",
  "zpl_content": "^XA^FO50,50^A0N,50,50^FD{ct_number}^FS^XZ",
  "preview_url": "data:image/png;base64,..."
}
```

### ZPL Generation

#### Generate ZPL from Template
```http
POST /zpl/generate
Content-Type: application/json
```

**Request Body:**
```json
{
  "template_id": "template_123",
  "data": {
    "ct_number": "ABC123DEF45678",
    "part_number": "HP-PART-001",
    "customer": "HP Inc."
  }
}
```

**Response:**
```json
{
  "zpl_content": "^XA^FO50,50^A0N,50,50^FDABC123DEF45678^FS^XZ",
  "preview_url": "data:image/png;base64,..."
}
```

## 📄 File Storage API (Supabase Storage)

### Base URL
```
https://qeozkzbjvvkgsvtitbny.supabase.co/storage/v1/object/
```

### FAI Documents

#### Upload FAI Document
```http
POST /fai_documents/folder/filename.xlsx
Content-Type: multipart/form-data
```

#### Get FAI Document
```http
GET /fai_documents/folder/filename.xlsx
```

#### List FAI Documents
```http
GET /fai_documents?prefix=folder/
```

### Master Images

#### Upload Master Image
```http
POST /master_images/part_number/image.jpg
Content-Type: multipart/form-data
```

#### Get Master Image
```http
GET /master_images/part_number/image.jpg
```

## 📊 Analytics & Reporting API

### Performance Metrics

#### Get System Performance
```http
GET /analytics/performance?period=24h
```

**Response:**
```json
{
  "period": "24h",
  "metrics": {
    "total_requests": 1250,
    "average_response_time": 180,
    "error_rate": 0.02,
    "active_users": 15
  }
}
```

### Order Analytics

#### Get Order Statistics
```http
GET /analytics/orders?period=7d&group_by=status
```

**Response:**
```json
{
  "period": "7d",
  "data": [
    {
      "status": "pending_procurement_arrangement",
      "count": 45,
      "percentage": 30.0
    },
    {
      "status": "awaiting_kitting_packing", 
      "count": 30,
      "percentage": 20.0
    }
  ]
}
```

## 🔔 WhatsApp Integration API (N8N)

### Base URL
```
https://your-n8n-instance.com/webhook/minierp
```

### Send Notification

#### Send WhatsApp Message
```http
POST /send
Content-Type: application/json
```

**Request Body:**
```json
{
  "recipient": "+1234567890",
  "message": "Order A001 is ready for QC approval",
  "type": "notification",
  "metadata": {
    "order_id": "123e4567-e89b-12d3-a456-************",
    "workflow_type": "qc_approval"
  }
}
```

### Interactive Workflows

#### Send Approval Request
```http
POST /approval
Content-Type: application/json
```

**Request Body:**
```json
{
  "recipient": "+1234567890",
  "workflow_id": "workflow_123",
  "message": "CT Number duplicate detected. Approve override?",
  "buttons": [
    {
      "id": "approve",
      "text": "✅ Approve"
    },
    {
      "id": "reject", 
      "text": "❌ Reject"
    }
  ],
  "expires_at": "2025-01-10T18:00:00Z"
}
```

## 🛡️ Security & Audit API

### Security Events

#### Get Security Events
```http
GET /security_events?order=timestamp.desc&limit=50
```

#### Log Security Event
```http
POST /security_events
Content-Type: application/json
```

**Request Body:**
```json
{
  "event_type": "failed_login",
  "user_email": "<EMAIL>",
  "ip_address": "************",
  "risk_level": "medium",
  "details": {
    "attempt_count": 3,
    "user_agent": "Mozilla/5.0..."
  }
}
```

### Audit Logs

#### Get Audit Logs
```http
GET /audit_logs?resource_type=eq.order_lines&order=timestamp.desc
```

#### Create Audit Entry
```http
POST /audit_logs
Content-Type: application/json
```

**Request Body:**
```json
{
  "action": "UPDATE",
  "resource_type": "order_lines",
  "resource_id": "123e4567-e89b-12d3-a456-************",
  "old_values": {"status": "pending"},
  "new_values": {"status": "in_progress"}
}
```

## 📊 Database Functions API

### Custom Functions

#### Get Dashboard Data
```http
POST /rpc/get_order_dashboard_data
Content-Type: application/json
```

**Request Body:**
```json
{
  "p_limit": 50,
  "p_offset": 0,
  "p_customer_id": null,
  "p_status": null
}
```

#### Validate CT Numbers Batch
```http
POST /rpc/validate_ct_number_batch
Content-Type: application/json
```

**Request Body:**
```json
{
  "p_ct_numbers": ["ABC123DEF45678", "XYZ789GHI01234"]
}
```

**Response:**
```json
[
  {
    "ct_number": "ABC123DEF45678",
    "is_valid": true,
    "existing_order_uid": null
  },
  {
    "ct_number": "XYZ789GHI01234", 
    "is_valid": false,
    "existing_order_uid": "A001"
  }
]
```

## 🔍 Search & Filtering

### Advanced Search
```http
GET /order_lines?or=(part_number.ilike.*SEARCH*,description.ilike.*SEARCH*)&order=created_at.desc
```

### Date Range Filtering
```http
GET /order_lines?created_at=gte.2025-01-01&created_at=lt.2025-02-01
```

### Status Filtering
```http
GET /order_lines?current_status=in.(pending_procurement_arrangement,awaiting_kitting_packing)
```

## 📝 Error Handling

### HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Unprocessable Entity
- `500`: Internal Server Error

### Error Response Format
```json
{
  "error": {
    "code": "INVALID_CT_NUMBER",
    "message": "CT number must be exactly 14 characters",
    "details": {
      "field": "ct_number",
      "value": "ABC123",
      "expected_length": 14,
      "actual_length": 6
    }
  }
}
```

## 🔧 Rate Limiting

### Limits
- **Authentication**: 5 attempts per 15 minutes per IP
- **API Calls**: 100 requests per minute per user
- **File Uploads**: 10 uploads per minute per user
- **Print Jobs**: 50 jobs per minute per printer

### Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1641811200
```

## 📚 SDK & Client Libraries

### JavaScript/TypeScript
```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'https://qeozkzbjvvkgsvtitbny.supabase.co',
  'your-anon-key'
)

// Get orders
const { data, error } = await supabase
  .from('order_lines')
  .select('*')
  .order('created_at', { ascending: false })
```

### MCP Client
```javascript
class MCPClient {
  constructor(baseUrl = 'http://localhost:3001/api/mcp') {
    this.baseUrl = baseUrl
  }

  async printLabel(printerId, templateType, data) {
    const response = await fetch(`${this.baseUrl}/print`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        printer_id: printerId,
        template_type: templateType,
        data
      })
    })
    return response.json()
  }
}
```

## 🧪 Testing

### Health Check Endpoints
- **Database**: `GET /health`
- **MCP Server**: `GET /api/mcp/health`
- **Authentication**: `GET /auth/v1/settings`

### Test Data
Use the following test data for development:
- **Test Customer ID**: `c1234567-1234-1234-1234-123456789012`
- **Test User**: `<EMAIL>`
- **Test CT Number**: `TEST123456789A`

## 📖 Examples

### Complete Order Workflow
```javascript
// 1. Create order
const { data: order } = await supabase
  .from('order_lines')
  .insert({
    uid: 'A001',
    customer_id: 'customer-uuid',
    part_number: 'HP-PART-001',
    description: 'HP Laptop Screen',
    total_order_quantity: 5,
    unit_price: 149.99
  })
  .select()

// 2. Assign CT number
const { data: ctNumber } = await supabase
  .from('ct_numbers')
  .insert({
    ct_number: 'ABC123DEF45678',
    order_line_id: order[0].id
  })

// 3. Print label
const printJob = await mcpClient.printLabel(
  'zebra_001',
  'ct_label',
  {
    ct_number: 'ABC123DEF45678',
    part_number: 'HP-PART-001',
    customer: 'HP Inc.'
  }
)

// 4. Update quantity
await supabase
  .from('order_line_quantities')
  .upsert({
    order_line_id: order[0].id,
    status: 'in_kitting_packing',
    quantity: 5
  })
```

---

**Document Version**: 1.0  
**Last Updated**: January 10, 2025  
**API Version**: v1  
**Contact**: <EMAIL>