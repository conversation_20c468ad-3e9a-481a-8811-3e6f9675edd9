version: '3.8'

services:
  # Mini-ERP Frontend Application
  mini-erp-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - mini-erp-network
    restart: unless-stopped
    depends_on:
      - mcp-printing-server
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MCP Printing Server
  mcp-printing-server:
    build:
      context: ./mcp-printing-server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_PATH=/app/data/printers.db
      - LOG_LEVEL=info
    volumes:
      - mcp-data:/app/data
      - mcp-queue:/app/queue
      - mcp-temp:/app/temp
    networks:
      - mini-erp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/mcp/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional - for advanced setups)
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
    networks:
      - mini-erp-network
    restart: unless-stopped
    depends_on:
      - mini-erp-frontend
      - mcp-printing-server

  # Redis for caching (Optional - for scaling)
  redis-cache:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mini-erp-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - mini-erp-network
    restart: unless-stopped

  # Log aggregation with Loki (Optional)
  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki
    networks:
      - mini-erp-network
    restart: unless-stopped

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - mini-erp-network
    restart: unless-stopped
    depends_on:
      - prometheus
      - loki

networks:
  mini-erp-network:
    driver: bridge

volumes:
  mcp-data:
    driver: local
  mcp-queue:
    driver: local
  mcp-temp:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  loki-data:
    driver: local
  grafana-data:
    driver: local