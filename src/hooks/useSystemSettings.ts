import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface SystemSetting {
  id: string
  setting_key: string
  setting_value: any
  description: string
  updated_by?: string
  updated_at: string
  updated_by_name?: string
}

export interface SettingsFormData {
  [key: string]: any
}

export function useSystemSettings() {
  const { user, hasPermission } = useAuth()
  const [settings, setSettings] = useState<SystemSetting[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load all system settings
  const loadSettings = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('system_settings')
        .select(`
          *,
          updated_by_user:users!system_settings_updated_by_fkey (
            first_name,
            last_name,
            email
          )
        `)
        .order('setting_key')

      if (error) throw error

      const formattedSettings: SystemSetting[] = (data || []).map(setting => ({
        id: setting.id,
        setting_key: setting.setting_key,
        setting_value: setting.setting_value,
        description: setting.description,
        updated_by: setting.updated_by,
        updated_at: setting.updated_at,
        updated_by_name: setting.updated_by_user ? 
          `${setting.updated_by_user.first_name || ''} ${setting.updated_by_user.last_name || ''}`.trim() || 
          setting.updated_by_user.email : undefined
      }))

      setSettings(formattedSettings)
    } catch (err: any) {
      console.error('Failed to load system settings:', err)
      setError(err.message || 'Failed to load system settings')
      toast.error('Failed to load system settings')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update setting
  const updateSetting = useCallback(async (settingKey: string, newValue: any): Promise<boolean> => {
    if (!hasPermission('MANAGE_SYSTEM_SETTINGS')) {
      toast.error('You do not have permission to modify system settings')
      return false
    }

    try {
      const { error } = await supabase
        .from('system_settings')
        .update({
          setting_value: newValue,
          updated_by: user?.id,
          updated_at: new Date().toISOString()
        })
        .eq('setting_key', settingKey)

      if (error) throw error

      toast.success('System setting updated successfully')
      await loadSettings()
      return true
    } catch (err: any) {
      console.error('Failed to update system setting:', err)
      toast.error(err.message || 'Failed to update system setting')
      return false
    }
  }, [hasPermission, user?.id, loadSettings])

  // Get setting by key
  const getSetting = useCallback((key: string): SystemSetting | undefined => {
    return settings.find(setting => setting.setting_key === key)
  }, [settings])

  // Get setting value by key
  const getSettingValue = useCallback((key: string): any => {
    const setting = getSetting(key)
    return setting?.setting_value
  }, [getSetting])

  // Get setting value with default
  const getSettingValueOrDefault = useCallback((key: string, defaultValue: any): any => {
    const value = getSettingValue(key)
    return value !== undefined ? value : defaultValue
  }, [getSettingValue])

  // Update multiple settings at once
  const updateMultipleSettings = useCallback(async (updates: Record<string, any>): Promise<boolean> => {
    if (!hasPermission('MANAGE_SYSTEM_SETTINGS')) {
      toast.error('You do not have permission to modify system settings')
      return false
    }

    try {
      const updatePromises = Object.entries(updates).map(([key, value]) =>
        supabase
          .from('system_settings')
          .update({
            setting_value: value,
            updated_by: user?.id,
            updated_at: new Date().toISOString()
          })
          .eq('setting_key', key)
      )

      const results = await Promise.all(updatePromises)
      
      // Check if any updates failed
      const errors = results.filter(result => result.error)
      if (errors.length > 0) {
        throw new Error(`Failed to update ${errors.length} setting(s)`)
      }

      toast.success(`Successfully updated ${Object.keys(updates).length} setting(s)`)
      await loadSettings()
      return true
    } catch (err: any) {
      console.error('Failed to update system settings:', err)
      toast.error(err.message || 'Failed to update system settings')
      return false
    }
  }, [hasPermission, user?.id, loadSettings])

  // Reset setting to default (would need default values defined somewhere)
  const resetSetting = useCallback(async (settingKey: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_SYSTEM_SETTINGS')) {
      toast.error('You do not have permission to modify system settings')
      return false
    }

    // Define default values for known settings
    const defaultValues: Record<string, any> = {
      'uid_prefix': 'A',
      'eta_colors': {
        rfq: { color: 'gray', label: 'RFQ ETA' },
        first: { color: 'green', label: 'First ETA' },
        second: { color: 'yellow', label: 'Second ETA' },
        third: { color: 'red', label: 'Third ETA' },
        final: { color: 'red', label: 'Final ETA', blink: true }
      },
      'rejection_reasons': [
        'Cosmetic Damage',
        'Functional Failure',
        'Wrong Part Number',
        'Specification Mismatch',
        'Quality Issues',
        'Packaging Damage',
        'Incomplete Item',
        'DOA (Dead on Arrival)',
        'Other'
      ],
      'hold_reasons': [
        'Pending Customer Clarification',
        'Quality Review Required',
        'Awaiting Parts',
        'Technical Issue',
        'Packaging Issue',
        'Documentation Missing',
        'Other'
      ],
      'whatsapp_settings': {
        enabled: false,
        default_groups: {
          directors: [],
          qc_alerts: [],
          np_notifications: []
        },
        n8n_webhook_url: '',
        evolution_api_url: ''
      }
    }

    const defaultValue = defaultValues[settingKey]
    if (defaultValue === undefined) {
      toast.error('No default value defined for this setting')
      return false
    }

    return await updateSetting(settingKey, defaultValue)
  }, [hasPermission, updateSetting])

  // Export settings as JSON
  const exportSettings = useCallback((): string => {
    const exportData = settings.reduce((acc, setting) => {
      acc[setting.setting_key] = setting.setting_value
      return acc
    }, {} as Record<string, any>)
    
    return JSON.stringify(exportData, null, 2)
  }, [settings])

  // Import settings from JSON
  const importSettings = useCallback(async (jsonData: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_SYSTEM_SETTINGS')) {
      toast.error('You do not have permission to import system settings')
      return false
    }

    try {
      const importData = JSON.parse(jsonData)
      
      // Validate that all keys exist in current settings
      const validKeys = settings.map(s => s.setting_key)
      const importKeys = Object.keys(importData)
      const invalidKeys = importKeys.filter(key => !validKeys.includes(key))
      
      if (invalidKeys.length > 0) {
        toast.error(`Invalid setting keys: ${invalidKeys.join(', ')}`)
        return false
      }

      return await updateMultipleSettings(importData)
    } catch (err: any) {
      console.error('Failed to import settings:', err)
      toast.error('Invalid JSON format or import failed')
      return false
    }
  }, [hasPermission, settings, updateMultipleSettings])

  // Load settings on mount
  useEffect(() => {
    if (hasPermission('MANAGE_SYSTEM_SETTINGS') || hasPermission('ACCESS_ADMIN_PANEL')) {
      loadSettings()
    }
  }, [hasPermission, loadSettings])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user || !hasPermission('MANAGE_SYSTEM_SETTINGS')) return

    const channel = supabase
      .channel('system-settings-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'system_settings'
        },
        (payload) => {
          console.log('System settings changed:', payload)
          loadSettings()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, hasPermission, loadSettings])

  return {
    // Data
    settings,
    isLoading,
    error,

    // Actions
    updateSetting,
    updateMultipleSettings,
    resetSetting,
    exportSettings,
    importSettings,

    // Utilities
    getSetting,
    getSettingValue,
    getSettingValueOrDefault,
    refresh: loadSettings,

    // Permissions
    canManageSettings: hasPermission('MANAGE_SYSTEM_SETTINGS')
  }
}