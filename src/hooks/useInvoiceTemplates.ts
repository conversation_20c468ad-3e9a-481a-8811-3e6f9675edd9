import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface InvoiceTemplate {
  id: string
  name: string
  customer_name?: string
  is_default: boolean
  payment_terms: string
  notes?: string
  header_content?: string
  footer_content?: string
  template_data: TemplateConfiguration
  created_at: string
  updated_at: string
  created_by?: string
  is_active: boolean
}

export interface TemplateConfiguration {
  showQuantity?: boolean
  showUnitPrice?: boolean
  showLineTotal?: boolean
  showCTNumbers?: boolean
  showPartMapping?: boolean
  groupByCategory?: boolean
  logoPosition?: 'top-left' | 'top-center' | 'top-right'
  hpSpecific?: boolean
  customFields?: string[]
  [key: string]: any
}

export interface InvoiceTemplateFormData {
  name: string
  customer_name?: string
  is_default: boolean
  payment_terms: string
  notes?: string
  header_content?: string
  footer_content?: string
  template_data: TemplateConfiguration
  is_active: boolean
}

export function useInvoiceTemplates() {
  const { user, hasPermission } = useAuth()
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load all invoice templates
  const loadTemplates = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('invoice_templates')
        .select('*')
        .eq('is_active', true)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false })

      if (error) throw error

      setTemplates(data || [])
    } catch (err: any) {
      console.error('Failed to load invoice templates:', err)
      setError(err.message || 'Failed to load invoice templates')
      toast.error('Failed to load invoice templates')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create new template
  const createTemplate = useCallback(async (templateData: InvoiceTemplateFormData): Promise<boolean> => {
    if (!hasPermission('CREATE_INVOICES')) {
      toast.error('You do not have permission to create invoice templates')
      return false
    }

    try {
      // If setting as default, unset other defaults first
      if (templateData.is_default) {
        await supabase
          .from('invoice_templates')
          .update({ is_default: false })
          .eq('is_default', true)
      }

      const { error } = await supabase
        .from('invoice_templates')
        .insert({
          ...templateData,
          created_by: user?.id
        })

      if (error) throw error

      toast.success('Invoice template created successfully')
      await loadTemplates()
      return true
    } catch (err: any) {
      console.error('Failed to create invoice template:', err)
      toast.error(err.message || 'Failed to create invoice template')
      return false
    }
  }, [hasPermission, user?.id, loadTemplates])

  // Update template
  const updateTemplate = useCallback(async (templateId: string, templateData: Partial<InvoiceTemplateFormData>): Promise<boolean> => {
    if (!hasPermission('CREATE_INVOICES')) {
      toast.error('You do not have permission to update invoice templates')
      return false
    }

    try {
      // If setting as default, unset other defaults first
      if (templateData.is_default) {
        await supabase
          .from('invoice_templates')
          .update({ is_default: false })
          .eq('is_default', true)
          .neq('id', templateId)
      }

      const { error } = await supabase
        .from('invoice_templates')
        .update({
          ...templateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)

      if (error) throw error

      toast.success('Invoice template updated successfully')
      await loadTemplates()
      return true
    } catch (err: any) {
      console.error('Failed to update invoice template:', err)
      toast.error(err.message || 'Failed to update invoice template')
      return false
    }
  }, [hasPermission, loadTemplates])

  // Delete template (soft delete)
  const deleteTemplate = useCallback(async (templateId: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_SYSTEM_SETTINGS')) {
      toast.error('You do not have permission to delete invoice templates')
      return false
    }

    try {
      const template = templates.find(t => t.id === templateId)
      if (template?.is_default) {
        toast.error('Cannot delete the default template. Please set another template as default first.')
        return false
      }

      const { error } = await supabase
        .from('invoice_templates')
        .update({ is_active: false })
        .eq('id', templateId)

      if (error) throw error

      toast.success('Invoice template deleted successfully')
      await loadTemplates()
      return true
    } catch (err: any) {
      console.error('Failed to delete invoice template:', err)
      toast.error(err.message || 'Failed to delete invoice template')
      return false
    }
  }, [hasPermission, templates, loadTemplates])

  // Set template as default
  const setAsDefault = useCallback(async (templateId: string): Promise<boolean> => {
    if (!hasPermission('CREATE_INVOICES')) {
      toast.error('You do not have permission to modify invoice templates')
      return false
    }

    try {
      // Unset all defaults
      await supabase
        .from('invoice_templates')
        .update({ is_default: false })
        .eq('is_default', true)

      // Set new default
      const { error } = await supabase
        .from('invoice_templates')
        .update({ is_default: true })
        .eq('id', templateId)

      if (error) throw error

      toast.success('Default template updated successfully')
      await loadTemplates()
      return true
    } catch (err: any) {
      console.error('Failed to set default template:', err)
      toast.error(err.message || 'Failed to set default template')
      return false
    }
  }, [hasPermission, loadTemplates])

  // Get template by ID
  const getTemplate = useCallback((templateId: string): InvoiceTemplate | undefined => {
    return templates.find(template => template.id === templateId)
  }, [templates])

  // Get default template
  const getDefaultTemplate = useCallback((): InvoiceTemplate | undefined => {
    return templates.find(template => template.is_default)
  }, [templates])

  // Get templates for customer
  const getTemplatesForCustomer = useCallback((customerName: string): InvoiceTemplate[] => {
    return templates.filter(template => 
      !template.customer_name || 
      template.customer_name.toLowerCase() === customerName.toLowerCase()
    )
  }, [templates])

  // Load templates on mount
  useEffect(() => {
    if (hasPermission('ACCESS_INVOICING')) {
      loadTemplates()
    }
  }, [hasPermission, loadTemplates])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user || !hasPermission('ACCESS_INVOICING')) return

    const channel = supabase
      .channel('invoice-templates-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'invoice_templates'
        },
        (payload) => {
          console.log('Invoice templates changed:', payload)
          loadTemplates()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, hasPermission, loadTemplates])

  return {
    // Data
    templates,
    isLoading,
    error,

    // Actions
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setAsDefault,

    // Utilities
    getTemplate,
    getDefaultTemplate,
    getTemplatesForCustomer,
    refresh: loadTemplates,

    // Permissions
    canCreateTemplates: hasPermission('CREATE_INVOICES'),
    canDeleteTemplates: hasPermission('MANAGE_SYSTEM_SETTINGS')
  }
}