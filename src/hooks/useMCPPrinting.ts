import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';

interface MCPConnectionConfig {
  apiUrl: string;
  apiKey: string;
  location: 'SB' | 'NP';
}

interface Printer {
  id: string;
  name: string;
  type: 'zebra' | 'generic';
  ip_address: string;
  port: number;
  location: string;
  manufacturer?: string;
  model?: string;
  capabilities: {
    zpl?: boolean;
    label_printing?: boolean;
    formats?: string[];
    color?: boolean;
    duplex?: boolean;
    paper_sizes?: string[];
  };
  health?: PrinterHealth;
}

interface PrinterHealth {
  status: 'online' | 'offline' | 'warning' | 'unknown';
  last_seen: string;
  response_time_ms?: number;
  error_message?: string;
  queue_size?: number;
}

interface HealthIndicators {
  mcp_server: {
    status: string;
    color: string;
    message: string;
  };
  printer_connectivity: {
    status: string;
    color: string;
    message: string;
    details?: { online: number; total: number };
  };
  frontend_connection: {
    status: string;
    color: string;
    message: string;
    details?: { active_connections: number };
  };
  overall: {
    status: string;
    color: string;
    message: string;
  };
}

interface PrintJob {
  id: string;
  type: 'zebra_label' | 'generic_document';
  printer_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  completed_at?: string;
  error_message?: string;
  attempts: number;
  max_attempts: number;
  metadata?: Record<string, any>;
  options?: Record<string, any>;
}

interface QueueStatus {
  zebra: {
    pending: number;
    processing: number;
    failed: number;
  };
  generic: {
    pending: number;
    processing: number;
    failed: number;
  };
  total: {
    pending: number;
    processing: number;
    failed: number;
  };
}

// Default configuration - can be overridden via environment variables
const DEFAULT_CONFIG: MCPConnectionConfig = {
  apiUrl: import.meta.env.VITE_MCP_API_URL || 'http://localhost:3001/api/mcp',
  apiKey: import.meta.env.VITE_MCP_API_KEY || 'your-secure-api-key-here',
  location: (import.meta.env.VITE_LOCATION as 'SB' | 'NP') || 'SB'
};

export function useMCPPrinting(config: Partial<MCPConnectionConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const queryClient = useQueryClient();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);

  // API request helper with authentication
  const apiRequest = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    const url = `${finalConfig.apiUrl}${endpoint}`;
    
    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': finalConfig.apiKey,
      ...options.headers
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      setConnectionError(error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }, [finalConfig.apiUrl, finalConfig.apiKey]);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const wsUrl = finalConfig.apiUrl.replace(/^http/, 'ws').replace('/api/mcp', '') + ':3002';
    
    const connectWebSocket = () => {
      try {
        const ws = new WebSocket(wsUrl);
        
        ws.onopen = () => {
          setIsConnected(true);
          setConnectionError(null);
          
          // Subscribe to health updates
          ws.send(JSON.stringify({ type: 'subscribe_health' }));
          ws.send(JSON.stringify({ type: 'subscribe_queue' }));
        };
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            
            if (data.type === 'health_update') {
              // Invalidate health queries to trigger refetch
              queryClient.invalidateQueries({ queryKey: ['mcp', 'health'] });
              queryClient.invalidateQueries({ queryKey: ['mcp', 'indicators'] });
            }
            
            if (data.type === 'queue_update') {
              // Invalidate queue queries
              queryClient.invalidateQueries({ queryKey: ['mcp', 'queue'] });
              queryClient.invalidateQueries({ queryKey: ['mcp', 'jobs'] });
            }
          } catch (error) {
            console.error('WebSocket message parsing error:', error);
          }
        };
        
        ws.onclose = () => {
          setIsConnected(false);
          setWebsocket(null);
          
          // Attempt to reconnect after 5 seconds
          setTimeout(connectWebSocket, 5000);
        };
        
        ws.onerror = (error) => {
          setConnectionError('WebSocket connection error');
          console.error('WebSocket error:', error);
        };
        
        setWebsocket(ws);
      } catch (error) {
        setConnectionError('Failed to connect to MCP server');
        setTimeout(connectWebSocket, 5000);
      }
    };

    connectWebSocket();

    return () => {
      if (websocket) {
        websocket.close();
      }
    };
  }, [finalConfig.apiUrl, queryClient, websocket]);

  // ============================================================================
  // QUERIES
  // ============================================================================

  // Get all printers with health status
  const usePrinters = () => {
    return useQuery({
      queryKey: ['mcp', 'printers'],
      queryFn: () => apiRequest('/printers'),
      refetchInterval: 30000, // Refetch every 30 seconds
      staleTime: 10000 // Consider data stale after 10 seconds
    });
  };

  // Get Zebra printers only
  const useZebraPrinters = () => {
    return useQuery({
      queryKey: ['mcp', 'printers', 'zebra'],
      queryFn: () => apiRequest('/printers/zebra'),
      refetchInterval: 30000
    });
  };

  // Get generic printers only
  const useGenericPrinters = () => {
    return useQuery({
      queryKey: ['mcp', 'printers', 'generic'],
      queryFn: () => apiRequest('/printers/generic'),
      refetchInterval: 30000
    });
  };

  // Get health indicators for status display
  const useHealthIndicators = () => {
    return useQuery({
      queryKey: ['mcp', 'indicators'],
      queryFn: () => apiRequest('/health/indicators'),
      refetchInterval: 15000, // Refetch every 15 seconds
      staleTime: 5000
    });
  };

  // Get detailed system health
  const useSystemHealth = () => {
    return useQuery({
      queryKey: ['mcp', 'health'],
      queryFn: () => apiRequest('/health'),
      refetchInterval: 30000
    });
  };

  // Get print queue status
  const useQueueStatus = () => {
    return useQuery({
      queryKey: ['mcp', 'queue'],
      queryFn: () => apiRequest('/health/queue'),
      refetchInterval: 10000 // Refetch every 10 seconds
    });
  };

  // Get print jobs
  const usePrintJobs = (filters: {
    status?: string;
    printer_id?: string;
    type?: string;
    limit?: number;
  } = {}) => {
    return useQuery({
      queryKey: ['mcp', 'jobs', filters],
      queryFn: () => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) params.append(key, value.toString());
        });
        return apiRequest(`/jobs?${params.toString()}`);
      },
      refetchInterval: 15000
    });
  };

  // Get specific print job details
  const usePrintJob = (jobId: string) => {
    return useQuery({
      queryKey: ['mcp', 'jobs', jobId],
      queryFn: () => apiRequest(`/jobs/${jobId}`),
      enabled: !!jobId,
      refetchInterval: 5000
    });
  };

  // ============================================================================
  // MUTATIONS
  // ============================================================================

  // Print Zebra label
  const printZebraLabel = useCallback(async (
    printer_id: string,
    zpl: string,
    metadata?: Record<string, any>
  ) => {
    try {
      return await apiRequest('/zebra/print', {
        method: 'POST',
        body: JSON.stringify({ printer_id, zpl, metadata })
      });
    } catch (error) {
      console.error('Failed to print Zebra label:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [apiRequest]);

  // Print generic document
  const printGenericDocument = useCallback(async (
    printer_id: string,
    pdf_data: string,
    options?: Record<string, any>
  ) => {
    try {
      return await apiRequest('/generic/print', {
        method: 'POST',
        body: JSON.stringify({ printer_id, pdf_data, options })
      });
    } catch (error) {
      console.error('Failed to print generic document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [apiRequest]);

  // Test printer connection
  const testPrinter = useCallback(async (printerId: string) => {
    try {
      return await apiRequest(`/printers/${printerId}/test`, {
        method: 'POST'
      });
    } catch (error) {
      console.error('Failed to test printer:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [apiRequest]);

  // Refresh printer discovery
  const refreshDiscovery = useCallback(async () => {
    try {
      const result = await apiRequest('/printers/discover', { method: 'POST' });
      queryClient.invalidateQueries({ queryKey: ['mcp', 'printers'] });
      return result;
    } catch (error) {
      console.error('Failed to refresh discovery:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }, [apiRequest, queryClient]);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const generateTestZPL = useCallback(async () => {
    return apiRequest('/zebra/test-zpl', { method: 'POST' });
  }, [apiRequest]);

  const validateZPL = useCallback(async (zpl: string) => {
    return apiRequest('/zebra/validate-zpl', {
      method: 'POST',
      body: JSON.stringify({ zpl })
    });
  }, [apiRequest]);

  const validatePDF = useCallback(async (pdf_data: string) => {
    return apiRequest('/generic/validate-pdf', {
      method: 'POST',
      body: JSON.stringify({ pdf_data })
    });
  }, [apiRequest]);

  return {
    // Connection status
    isConnected,
    connectionError,
    location: finalConfig.location,
    
    // Queries
    usePrinters,
    useZebraPrinters,
    useGenericPrinters,
    useHealthIndicators,
    useSystemHealth,
    useQueueStatus,
    usePrintJobs,
    usePrintJob,
    
    // Actions
    printZebraLabel,
    printGenericDocument,
    testPrinter,
    refreshDiscovery,
    
    // Utilities
    generateTestZPL,
    validateZPL,
    validatePDF
  };
}

// Export types for use in components
export type {
  Printer,
  PrinterHealth,
  HealthIndicators,
  PrintJob,
  QueueStatus,
  MCPConnectionConfig
};