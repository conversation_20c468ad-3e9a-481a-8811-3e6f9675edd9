import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useOrders, OrderLineWithDetails } from '@/hooks/useOrders'
import { QuantityTransitionService, QuantityTransition, transitionEvents } from '@/services/QuantityTransitionService'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface KittingTask {
  id: string
  order: OrderLineWithDetails
  priority: 'urgent' | 'high' | 'normal' | 'low'
  awaitingQuantity: number
  assignedTo: string | null
  estimatedTime: string
  ctNumbers: string[]
  dueTime: Date
  complexity: 'simple' | 'medium' | 'complex'
  actualProgress?: number
  startedAt?: Date
  assignedAt?: Date
  ctHistory?: CTHistoryItem[]
}

export interface CTHistoryItem {
  ctNumber: string
  currentState: string
  transitions: {
    fromState: string
    toState: string
    timestamp: string
    user: string
    reason?: string
  }[]
}

export interface CurrentKittingTask {
  id: string
  orderUid: string
  partNumber: string
  description: string
  quantity: number
  startedAt: Date
  estimatedCompletion: Date
  progress: number
  status: 'in_progress' | 'paused' | 'completed'
  assignedTo: string
}

export interface KittingStats {
  totalQueue: number
  unassigned: number
  urgentTasks: number
  myTasks: number
  inProgress: number
  completedToday: number
}

export function useKittingQueue() {
  const { user, hasPermission } = useAuth()
  const { data: orders, isLoading: ordersLoading } = useOrders()
  const [kittingQueue, setKittingQueue] = useState<KittingTask[]>([])
  const [currentTasks, setCurrentTasks] = useState<CurrentKittingTask[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Transform orders data into kitting queue
  const transformOrdersToKittingQueue = useCallback(async (orders: OrderLineWithDetails[]): Promise<KittingTask[]> => {
    if (!orders) return []

    // Filter orders that have quantities in any kitting-related state
    const ordersWithAwaitingKitting = orders.filter(order => {
      const quantities = order.quantities
      return quantities && (
        quantities.awaiting_kitting_packing > 0 || 
        quantities.in_kitting_packing > 0 ||
        quantities.on_hold_kitting > 0
      )
    })

    // Fetch CT numbers and history for all these orders
    const orderLineIds = ordersWithAwaitingKitting
      .map(order => order.id)
      .filter(id => id) // Remove any undefined IDs

    let ctNumbersMap: Record<string, string[]> = {}
    let ctHistoryMap: Record<string, CTHistoryItem[]> = {}

    if (orderLineIds.length > 0) {
      try {
        // Query quantity_logs to get CT numbers and their history
        const { data: ctData, error } = await supabase
          .from('quantity_logs')
          .select(`
            order_line_id, 
            associated_ct_numbers, 
            from_state, 
            to_state, 
            timestamp, 
            reason_text,
            user_id,
            created_by
          `)
          .in('order_line_id', orderLineIds)
          .not('associated_ct_numbers', 'is', null)
          .order('timestamp', { ascending: false })

        if (error) {
          console.error('Failed to fetch CT data:', error)
        } else if (ctData) {
          // Process CT data to build both CT numbers map and history
          ctData.forEach(log => {
            const orderLineId = log.order_line_id
            
            if (log.associated_ct_numbers && log.associated_ct_numbers.length > 0) {
              // Build CT numbers map
              if (!ctNumbersMap[orderLineId]) {
                ctNumbersMap[orderLineId] = []
              }
              
              // Build history for each CT
              log.associated_ct_numbers.forEach((ct: string) => {
                // Add to CT numbers list
                if (!ctNumbersMap[orderLineId].includes(ct)) {
                  ctNumbersMap[orderLineId].push(ct)
                }
                
                // Build history
                if (!ctHistoryMap[orderLineId]) {
                  ctHistoryMap[orderLineId] = []
                }
                
                // Find or create history entry for this CT
                let ctHistoryItem = ctHistoryMap[orderLineId].find(h => h.ctNumber === ct)
                if (!ctHistoryItem) {
                  ctHistoryItem = {
                    ctNumber: ct,
                    currentState: log.to_state,
                    transitions: []
                  }
                  ctHistoryMap[orderLineId].push(ctHistoryItem)
                }
                
                // Add transition
                // For now, show user_id as we don't have user details in this query
                ctHistoryItem.transitions.push({
                  fromState: log.from_state,
                  toState: log.to_state,
                  timestamp: log.timestamp,
                  user: log.created_by || log.user_id || 'System',
                  reason: log.reason_text
                })
                
                // Update current state if this is the most recent transition
                const existingTransitionTime = new Date(ctHistoryItem.transitions[0]?.timestamp || 0).getTime()
                const currentTransitionTime = new Date(log.timestamp).getTime()
                if (currentTransitionTime > existingTransitionTime) {
                  ctHistoryItem.currentState = log.to_state
                }
              })
            }
          })
          
          // Sort transitions chronologically for each CT
          Object.values(ctHistoryMap).forEach(orderHistory => {
            orderHistory.forEach(ctHistory => {
              ctHistory.transitions.sort((a, b) => 
                new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
              )
            })
          })
        }
      } catch (err) {
        console.error('Error fetching CT data:', err)
      }
    }

    return ordersWithAwaitingKitting
      .map(order => {
        const quantities = order.quantities!
        
        // Determine priority based on ETA and order value
        let priority: 'urgent' | 'high' | 'normal' | 'low' = 'normal'
        if (order.currentEtaStatus === 'overdue' || order.currentEtaStatus === 'final') {
          priority = 'urgent'
        } else if (order.currentEtaStatus === 'third' || quantities.awaiting_kitting_packing > 10) {
          priority = 'high'
        } else if (quantities.awaiting_kitting_packing <= 2) {
          priority = 'low'
        }

        // Determine complexity based on quantity and part type
        let complexity: 'simple' | 'medium' | 'complex' = 'medium'
        if (quantities.awaiting_kitting_packing <= 5 && order.category_name !== 'Motherboard') {
          complexity = 'simple'
        } else if (quantities.awaiting_kitting_packing > 15 || order.category_name === 'Motherboard') {
          complexity = 'complex'
        }

        // Estimate time based on complexity and quantity
        const baseTime = complexity === 'simple' ? 15 : complexity === 'medium' ? 30 : 60
        const timeMultiplier = Math.min(quantities.awaiting_kitting_packing / 5, 3)
        const estimatedMinutes = Math.round(baseTime * timeMultiplier)
        const estimatedTime = estimatedMinutes > 60 
          ? `${Math.floor(estimatedMinutes / 60)}h ${estimatedMinutes % 60}m`
          : `${estimatedMinutes} min`

        // Calculate due time based on ETA
        const dueTime = order.current_eta 
          ? new Date(order.current_eta)
          : new Date(Date.now() + 24 * 60 * 60 * 1000) // Default to 24 hours

        // Calculate total kitting quantity (all items in kitting workflow)
        const totalKittingQuantity = 
          (quantities.awaiting_kitting_packing || 0) + 
          (quantities.in_kitting_packing || 0) + 
          (quantities.on_hold_kitting || 0)

        return {
          id: order.id || `task-${order.uid}`,
          order,
          priority,
          awaitingQuantity: totalKittingQuantity,
          assignedTo: null, // TODO: Fetch from database
          estimatedTime,
          ctNumbers: ctNumbersMap[order.id || ''] || [], // Use fetched CT numbers
          ctHistory: ctHistoryMap[order.id || ''] || [], // Include CT history
          dueTime,
          complexity
        } as KittingTask
      })
      .sort((a, b) => {
        // Sort by priority first, then by due time
        const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[a.priority] - priorityOrder[b.priority]
        }
        return a.dueTime.getTime() - b.dueTime.getTime()
      })
  }, [])

  // Load kitting queue data
  const loadKittingQueue = useCallback(async () => {
    if (!orders || ordersLoading) return

    setIsLoading(true)
    setError(null)

    try {
      const queue = await transformOrdersToKittingQueue(orders)
      setKittingQueue(queue)

      // TODO: Load current tasks from database
      // For now, using empty array until database integration
      setCurrentTasks([])

    } catch (err) {
      console.error('Failed to load kitting queue:', err)
      setError('Failed to load kitting queue')
      toast.error('Failed to load kitting queue')
    } finally {
      setIsLoading(false)
    }
  }, [orders, ordersLoading, transformOrdersToKittingQueue])

  // Assign task to current user
  const assignTaskToMe = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      // TODO: Update database with task assignment
      console.log('Assigning task to user:', taskId, user.email)
      
      // Update local state immediately for optimistic UI
      setKittingQueue(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, assignedTo: user.email?.split('@')[0] || 'Unknown', assignedAt: new Date() }
          : task
      ))

      toast.success('Task assigned successfully')
      return true
    } catch (err) {
      console.error('Failed to assign task:', err)
      toast.error('Failed to assign task')
      return false
    }
  }, [user])

  // Start kitting task with real quantity transition
  const startKittingTask = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const task = kittingQueue.find(t => t.id === taskId)
      if (!task) {
        toast.error('Task not found')
        return false
      }

      // Get quantity tracking hook for this order
      const orderLineId = task.order.id || ''
      if (!orderLineId) {
        toast.error('Order line ID not found')
        return false
      }

      // Create quantity transition: awaiting_kitting_packing → in_kitting_packing
      const transition: QuantityTransition = {
        orderLineId,
        fromState: 'awaiting_kitting_packing',
        toState: 'in_kitting_packing',
        quantity: task.awaitingQuantity,
        reason: `Kitting task started by ${user.email?.split('@')[0]}`,
        ctNumbers: task.ctNumbers,
        metadata: {
          taskId,
          assignedTo: user.email?.split('@')[0],
          workflowStage: 'kitting_start'
        }
      }

      // Execute quantity transition using service
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      if (!result.success) {
        toast.error(`Failed to start kitting: ${result.message}`)
        return false
      }

      // Move to current tasks
      const currentTask: CurrentKittingTask = {
        id: taskId,
        orderUid: task.order.uid || '',
        partNumber: task.order.customer_part_number || '',
        description: task.order.bpi_description || '',
        quantity: task.awaitingQuantity,
        startedAt: new Date(),
        estimatedCompletion: new Date(Date.now() + (parseInt(task.estimatedTime) * 60 * 1000)),
        progress: 0,
        status: 'in_progress',
        assignedTo: user.email?.split('@')[0] || 'Unknown'
      }

      setCurrentTasks(prev => [...prev, currentTask])
      
      // Remove from queue
      setKittingQueue(prev => prev.filter(t => t.id !== taskId))

      toast.success(`Kitting started - ${task.awaitingQuantity} units moved to In Kitting`)
      return true
    } catch (err) {
      console.error('Failed to start task:', err)
      toast.error('Failed to start task')
      return false
    }
  }, [user, kittingQueue])

  // Complete kitting task with real quantity transition
  const completeKittingTask = useCallback(async (taskId: string, completedQuantity?: number): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const currentTask = currentTasks.find(t => t.id === taskId)
      if (!currentTask) {
        toast.error('Task not found')
        return false
      }

      const quantityToComplete = completedQuantity || currentTask.quantity

      // Find the original order for this task
      const originalOrder = orders?.find(order => order.uid === currentTask.orderUid)
      if (!originalOrder?.id) {
        toast.error('Original order not found')
        return false
      }

      // Create quantity transition: in_kitting_packing → kitted_packed_awaiting_screening_qc
      const transition: QuantityTransition = {
        orderLineId: originalOrder.id,
        fromState: 'in_kitting_packing',
        toState: 'kitted_packed_awaiting_screening_qc',
        quantity: quantityToComplete,
        reason: `Kitting completed by ${user.email?.split('@')[0]}`,
        metadata: {
          taskId,
          completedBy: user.email?.split('@')[0],
          workflowStage: 'kitting_complete',
          completionTime: new Date().toISOString()
        }
      }

      // Execute quantity transition
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      if (!result.success) {
        toast.error(`Failed to complete kitting: ${result.message}`)
        return false
      }

      // Remove from current tasks
      setCurrentTasks(prev => prev.filter(t => t.id !== taskId))

      toast.success(`Kitting completed - ${quantityToComplete} units moved to Awaiting QC`)
      return true
    } catch (err) {
      console.error('Failed to complete task:', err)
      toast.error('Failed to complete task')
      return false
    }
  }, [user, currentTasks, orders])

  // Update task progress
  const updateTaskProgress = useCallback(async (taskId: string, progress: number): Promise<boolean> => {
    try {
      setCurrentTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, progress: Math.min(100, Math.max(0, progress)) } : task
      ))
      return true
    } catch (err) {
      console.error('Failed to update progress:', err)
      return false
    }
  }, [])

  // Pause kitting task
  const pauseKittingTask = useCallback(async (taskId: string): Promise<boolean> => {
    try {
      setCurrentTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status: 'paused' } : task
      ))
      toast.success('Task paused')
      return true
    } catch (err) {
      console.error('Failed to pause task:', err)
      toast.error('Failed to pause task')
      return false
    }
  }, [])

  // Resume kitting task
  const resumeKittingTask = useCallback(async (taskId: string): Promise<boolean> => {
    try {
      setCurrentTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status: 'in_progress' } : task
      ))
      toast.success('Task resumed')
      return true
    } catch (err) {
      console.error('Failed to resume task:', err)
      toast.error('Failed to resume task')
      return false
    }
  }, [])

  // Calculate kitting stats
  const calculateStats = useCallback((): KittingStats => {
    const userEmail = user?.email?.split('@')[0]
    
    return {
      totalQueue: kittingQueue.length,
      unassigned: kittingQueue.filter(t => !t.assignedTo).length,
      urgentTasks: kittingQueue.filter(t => t.priority === 'urgent').length,
      myTasks: kittingQueue.filter(t => t.assignedTo === userEmail).length + currentTasks.length,
      inProgress: currentTasks.filter(t => t.status === 'in_progress').length,
      completedToday: 0 // TODO: Calculate from database
    }
  }, [kittingQueue, currentTasks, user])

  // Load data when orders change
  useEffect(() => {
    loadKittingQueue()
  }, [loadKittingQueue])

  // Set up real-time subscriptions for kitting-related changes
  useEffect(() => {
    if (!user) return

    console.log('🔄 Setting up kitting real-time subscriptions...')
    
    let quantityChannel: any = null
    let broadcastChannel: any = null
    let pollInterval: NodeJS.Timeout | null = null
    
    // Clean up any existing channels
    const cleanup = () => {
      if (quantityChannel) {
        supabase.removeChannel(quantityChannel)
        quantityChannel = null
      }
      if (broadcastChannel) {
        supabase.removeChannel(broadcastChannel)
        broadcastChannel = null
      }
      if (pollInterval) {
        clearInterval(pollInterval)
        pollInterval = null
      }
    }
    
    // Subscribe to quantity transition events for instant updates
    const handleTransitionEvent = (event: CustomEvent) => {
      console.log('⚡ Received transition event:', event.detail)
      loadKittingQueue()
    }
    
    // Add event listener for transition events
    transitionEvents.addEventListener('quantity-transition', handleTransitionEvent as any)
    
    // Set up Supabase subscriptions with fixed configuration
    const setupSubscriptions = () => {
      // 1. Subscribe to quantity changes with proper filter
      quantityChannel = supabase
        .channel(`kitting-quantities-${user.id}-${Date.now()}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'order_line_quantities'
          },
          (payload) => {
            console.log('📊 Quantity change detected:', payload.eventType)
            const newData = payload.new as any
            const oldData = payload.old as any
            
            // Check if this affects kitting states
            if (newData && oldData) {
              const affectsKitting = 
                newData.awaiting_kitting_packing !== oldData.awaiting_kitting_packing ||
                newData.in_kitting_packing !== oldData.in_kitting_packing ||
                newData.on_hold_kitting !== oldData.on_hold_kitting ||
                newData.kitted_awaiting_qc !== oldData.kitted_awaiting_qc
              
              if (affectsKitting) {
                console.log('🔄 Kitting-related change, reloading...')
                loadKittingQueue()
              }
            }
          }
        )
        .subscribe((status) => {
          console.log('📡 Quantity channel status:', status)
        })
      
      // 2. Set up broadcast channel for instant cross-client updates
      broadcastChannel = supabase
        .channel('kitting-broadcast', {
          config: {
            broadcast: {
              self: true
            }
          }
        })
        .on(
          'broadcast',
          { event: 'kitting-update' },
          (payload) => {
            console.log('📢 Broadcast update received:', payload)
            loadKittingQueue()
          }
        )
        .subscribe((status) => {
          console.log('📡 Broadcast channel status:', status)
          if (status === 'SUBSCRIBED') {
            console.log('✅ Kitting broadcast channel ready')
          }
        })
      
      // 3. Subscribe to CT updates for the dashboard
      supabase
        .channel(`kitting-ct-${user.id}-${Date.now()}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'ct_numbers'
          },
          (payload) => {
            const data = payload.new as any
            if (data && (
              data.current_state === 'awaiting_kitting_packing' ||
              data.current_state === 'in_kitting_packing' ||
              data.current_state === 'on_hold_at_kitting_packing'
            )) {
              console.log('🏷️ Kitting-related CT update:', data.ct_number)
              loadKittingQueue()
            }
          }
        )
        .subscribe()
    }
    
    // Set up subscriptions
    setupSubscriptions()
    
    // Set up polling as fallback (every 60 seconds instead of 30)
    pollInterval = setInterval(() => {
      console.log('🔄 Polling for kitting updates...')
      loadKittingQueue()
    }, 60000)

    return () => {
      console.log('🧹 Cleaning up kitting subscriptions')
      cleanup()
      
      // Remove event listener
      transitionEvents.removeEventListener('quantity-transition', handleTransitionEvent as any)
    }
  }, [user, loadKittingQueue])

  const stats = calculateStats()
  
  return {
    // Core data
    kittingQueue,
    currentTasks,
    isLoading: isLoading || ordersLoading,
    error,
    stats,

    // Convenience properties for backwards compatibility
    totalItems: stats.totalQueue || 0,
    assignedToUser: stats.myTasks || 0,
    onHoldItems: currentTasks.filter(t => t.status === 'paused').length || 0,

    // Actions
    assignTaskToMe,
    startKittingTask,
    completeKittingTask,
    pauseKittingTask,
    resumeKittingTask,
    updateTaskProgress,
    
    // Utilities
    refresh: loadKittingQueue,
    canManageKitting: hasPermission('manage_kitting'),
    canAssignTasks: hasPermission('assign_kitting_tasks')
  }
}