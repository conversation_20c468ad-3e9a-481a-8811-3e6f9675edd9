import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface QuantityHold {
  id: string
  order_line_id: string
  quantity_held: number
  hold_stage: 'kitting_packing' | 'screening_qc'
  hold_reason: string
  hold_notes?: string
  
  // Hold Management
  held_by?: string
  held_at: string
  released_by?: string
  released_at?: string
  is_active: boolean
  
  // Resolution
  resolution_action?: 'released' | 'rejected' | 'escalated'
  resolution_notes?: string
  
  created_at: string
  updated_at: string
  
  // Joined data
  order_uid?: string
  customer_name?: string
  customer_part_number?: string
  held_by_name?: string
  released_by_name?: string
}

export interface QuantityHoldFormData {
  order_line_id: string
  quantity_held: number
  hold_stage: 'kitting_packing' | 'screening_qc'
  hold_reason: string
  hold_notes?: string
}

export interface HoldStats {
  totalActiveHolds: number
  kittingHolds: number
  qcHolds: number
  quantityOnHold: number
  avgHoldDuration: number
  urgentHolds: number
}

export function useQuantityHolds() {
  const { user, hasPermission } = useAuth()
  const [holds, setHolds] = useState<QuantityHold[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load all quantity holds
  const loadHolds = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('quantity_holds')
        .select(`
          *,
          order_lines!inner (
            uid,
            customer_part_number,
            customers (
              name
            )
          ),
          held_by_user:users!quantity_holds_held_by_fkey (
            first_name,
            last_name,
            email
          ),
          released_by_user:users!quantity_holds_released_by_fkey (
            first_name,
            last_name,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      const formattedHolds: QuantityHold[] = (data || []).map(hold => ({
        id: hold.id,
        order_line_id: hold.order_line_id,
        quantity_held: hold.quantity_held,
        hold_stage: hold.hold_stage,
        hold_reason: hold.hold_reason,
        hold_notes: hold.hold_notes,
        held_by: hold.held_by,
        held_at: hold.held_at,
        released_by: hold.released_by,
        released_at: hold.released_at,
        is_active: hold.is_active,
        resolution_action: hold.resolution_action,
        resolution_notes: hold.resolution_notes,
        created_at: hold.created_at,
        updated_at: hold.updated_at,
        
        // Joined data
        order_uid: hold.order_lines?.uid,
        customer_name: hold.order_lines?.customers?.name,
        customer_part_number: hold.order_lines?.customer_part_number,
        held_by_name: hold.held_by_user ? 
          `${hold.held_by_user.first_name || ''} ${hold.held_by_user.last_name || ''}`.trim() || 
          hold.held_by_user.email : undefined,
        released_by_name: hold.released_by_user ? 
          `${hold.released_by_user.first_name || ''} ${hold.released_by_user.last_name || ''}`.trim() || 
          hold.released_by_user.email : undefined
      }))

      setHolds(formattedHolds)
    } catch (err: any) {
      console.error('Failed to load quantity holds:', err)
      setError(err.message || 'Failed to load quantity holds')
      toast.error('Failed to load quantity holds')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create new hold
  const createHold = useCallback(async (holdData: QuantityHoldFormData): Promise<boolean> => {
    if (!hasPermission('MANAGE_KITTING_PACKING') && !hasPermission('MANAGE_SCREENING_QC')) {
      toast.error('You do not have permission to create quantity holds')
      return false
    }

    // Check specific stage permission
    if (holdData.hold_stage === 'kitting_packing' && !hasPermission('MANAGE_KITTING_PACKING')) {
      toast.error('You do not have permission to create kitting holds')
      return false
    }

    if (holdData.hold_stage === 'screening_qc' && !hasPermission('MANAGE_SCREENING_QC')) {
      toast.error('You do not have permission to create QC holds')
      return false
    }

    try {
      const { error } = await supabase
        .from('quantity_holds')
        .insert({
          ...holdData,
          held_by: user?.id,
          is_active: true
        })

      if (error) throw error

      toast.success('Quantity hold created successfully')
      await loadHolds()
      return true
    } catch (err: any) {
      console.error('Failed to create quantity hold:', err)
      toast.error(err.message || 'Failed to create quantity hold')
      return false
    }
  }, [hasPermission, user?.id, loadHolds])

  // Release hold
  const releaseHold = useCallback(async (holdId: string, resolutionNotes?: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_KITTING_PACKING') && !hasPermission('MANAGE_SCREENING_QC')) {
      toast.error('You do not have permission to release quantity holds')
      return false
    }

    try {
      const { error } = await supabase
        .from('quantity_holds')
        .update({
          is_active: false,
          released_by: user?.id,
          released_at: new Date().toISOString(),
          resolution_action: 'released',
          resolution_notes: resolutionNotes,
          updated_at: new Date().toISOString()
        })
        .eq('id', holdId)

      if (error) throw error

      toast.success('Quantity hold released successfully')
      await loadHolds()
      return true
    } catch (err: any) {
      console.error('Failed to release quantity hold:', err)
      toast.error(err.message || 'Failed to release quantity hold')
      return false
    }
  }, [hasPermission, user?.id, loadHolds])

  // Escalate hold
  const escalateHold = useCallback(async (holdId: string, escalationNotes: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_KITTING_PACKING') && !hasPermission('MANAGE_SCREENING_QC')) {
      toast.error('You do not have permission to escalate quantity holds')
      return false
    }

    try {
      const { error } = await supabase
        .from('quantity_holds')
        .update({
          resolution_action: 'escalated',
          resolution_notes: escalationNotes,
          updated_at: new Date().toISOString()
        })
        .eq('id', holdId)

      if (error) throw error

      toast.success('Quantity hold escalated successfully')
      await loadHolds()
      return true
    } catch (err: any) {
      console.error('Failed to escalate quantity hold:', err)
      toast.error(err.message || 'Failed to escalate quantity hold')
      return false
    }
  }, [hasPermission, loadHolds])

  // Reject hold (convert to permanent rejection)
  const rejectHold = useCallback(async (holdId: string, rejectionNotes: string): Promise<boolean> => {
    if (!hasPermission('MANAGE_KITTING_PACKING') && !hasPermission('MANAGE_SCREENING_QC')) {
      toast.error('You do not have permission to reject quantity holds')
      return false
    }

    try {
      const { error } = await supabase
        .from('quantity_holds')
        .update({
          is_active: false,
          released_by: user?.id,
          released_at: new Date().toISOString(),
          resolution_action: 'rejected',
          resolution_notes: rejectionNotes,
          updated_at: new Date().toISOString()
        })
        .eq('id', holdId)

      if (error) throw error

      toast.success('Quantity hold rejected successfully')
      await loadHolds()
      return true
    } catch (err: any) {
      console.error('Failed to reject quantity hold:', err)
      toast.error(err.message || 'Failed to reject quantity hold')
      return false
    }
  }, [hasPermission, user?.id, loadHolds])

  // Get holds for order
  const getHoldsForOrder = useCallback((orderLineId: string): QuantityHold[] => {
    return holds.filter(hold => hold.order_line_id === orderLineId)
  }, [holds])

  // Get active holds
  const getActiveHolds = useCallback((): QuantityHold[] => {
    return holds.filter(hold => hold.is_active)
  }, [holds])

  // Get holds by stage
  const getHoldsByStage = useCallback((stage: 'kitting_packing' | 'screening_qc'): QuantityHold[] => {
    return holds.filter(hold => hold.hold_stage === stage && hold.is_active)
  }, [holds])

  // Calculate hold statistics
  const getHoldStats = useCallback((): HoldStats => {
    const activeHolds = getActiveHolds()
    
    const totalActiveHolds = activeHolds.length
    const kittingHolds = activeHolds.filter(h => h.hold_stage === 'kitting_packing').length
    const qcHolds = activeHolds.filter(h => h.hold_stage === 'screening_qc').length
    const quantityOnHold = activeHolds.reduce((sum, hold) => sum + hold.quantity_held, 0)
    
    // Calculate average hold duration (in hours)
    const now = new Date()
    const durations = activeHolds.map(hold => {
      const heldAt = new Date(hold.held_at)
      return (now.getTime() - heldAt.getTime()) / (1000 * 60 * 60)
    })
    const avgHoldDuration = durations.length > 0 ? 
      durations.reduce((sum, dur) => sum + dur, 0) / durations.length : 0
    
    // Urgent holds (older than 24 hours)
    const urgentHolds = activeHolds.filter(hold => {
      const heldAt = new Date(hold.held_at)
      const hoursAgo = (now.getTime() - heldAt.getTime()) / (1000 * 60 * 60)
      return hoursAgo > 24
    }).length

    return {
      totalActiveHolds,
      kittingHolds,
      qcHolds,
      quantityOnHold,
      avgHoldDuration,
      urgentHolds
    }
  }, [getActiveHolds])

  // Load holds on mount
  useEffect(() => {
    if (hasPermission('MANAGE_KITTING_PACKING') || hasPermission('MANAGE_SCREENING_QC')) {
      loadHolds()
    }
  }, [hasPermission, loadHolds])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user || (!hasPermission('MANAGE_KITTING_PACKING') && !hasPermission('MANAGE_SCREENING_QC'))) return

    const channel = supabase
      .channel('quantity-holds-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quantity_holds'
        },
        (payload) => {
          console.log('Quantity holds changed:', payload)
          loadHolds()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, hasPermission, loadHolds])

  return {
    // Data
    holds,
    isLoading,
    error,

    // Actions
    createHold,
    releaseHold,
    escalateHold,
    rejectHold,

    // Utilities
    getHoldsForOrder,
    getActiveHolds,
    getHoldsByStage,
    getHoldStats,
    refresh: loadHolds,

    // Permissions
    canManageKittingHolds: hasPermission('MANAGE_KITTING_PACKING'),
    canManageQCHolds: hasPermission('MANAGE_SCREENING_QC')
  }
}