import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'
import { QuantityTransitionService } from '@/services/QuantityTransitionService'
import { useQueryClient } from '@tanstack/react-query'

// Quantity State Types
export type QuantityState = 
  | 'total_order_quantity'
  | 'pending_procurement_arrangement'
  | 'requested_from_stock'
  | 'awaiting_kitting_packing'
  | 'in_kitting_packing'
  | 'on_hold_at_kitting_packing'
  | 'kitted_packed_awaiting_screening_qc'
  | 'in_screening_qc'
  | 'on_hold_at_screening_qc'
  | 'screening_qc_passed_ready_for_invoice'
  | 'screening_qc_rejected'
  | 'invoiced'
  | 'shipped_delivered'
  | 'cancelled'

export interface QuantityData {
  orderLineId: string
  totalOrderQuantity: number
  pendingProcurementArrangement: number
  requestedFromStock: number
  awaitingKittingPacking: number
  inKittingPacking: number
  onHoldAtKittingPacking: number
  kittedPackedAwaitingScreeningQc: number
  inScreeningQc: number
  onHoldAtScreeningQc: number
  screeningQcPassedReadyForInvoice: number
  screeningQcRejected: number
  invoiced: number
  shippedDelivered: number
  cancelled: number
  lastUpdatedAt: string
  lastUpdatedBy?: string
  notes?: string
}

export interface QuantityStateDefinition {
  id: string
  stateName: string
  displayName: string
  description?: string
  sortOrder: number
  isActive: boolean
  allowedTransitions: string[]
  requiredPermissions: string[]
  stageCategory: 'procurement' | 'kitting' | 'qc' | 'final'
  createdAt: string
  updatedAt: string
}

export interface QuantityTransition {
  orderLineId: string
  fromState: QuantityState
  toState: QuantityState
  quantity: number
  reason?: string
  rejectionReason?: string
  ctNumbers?: string[]
  metadata?: Record<string, any>
}

export interface TransitionResult {
  success: boolean
  message: string
  newQuantities?: QuantityData
  logId?: string
}

export interface QuantityLog {
  id: string
  orderLineId: string
  fromState?: string
  toState: string
  quantityMoved: number
  reason?: string
  rejectionReason?: string
  ctNumbers?: string[]
  location: 'SB' | 'NP'
  metadata?: Record<string, any>
  createdBy?: string
  createdAt: string
}

export interface QuantityHold {
  id: string
  orderLineId: string
  quantityHeld: number
  holdStage: 'kitting_packing' | 'screening_qc'
  holdReason: string
  holdNotes?: string
  heldBy?: string
  heldAt: string
  releasedBy?: string
  releasedAt?: string
  isActive: boolean
  resolutionAction?: 'released' | 'rejected' | 'escalated'
  resolutionNotes?: string
  createdAt: string
  updatedAt: string
}

export function useQuantityTracking(orderLineId?: string) {
  const { user, hasPermission } = useAuth()
  const queryClient = useQueryClient()
  const [quantities, setQuantities] = useState<QuantityData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [stateDefinitions, setStateDefinitions] = useState<QuantityStateDefinition[]>([])

  // Load quantity state definitions
  const loadStateDefinitions = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('quantity_state_definitions')
        .select('*')
        .eq('is_active', true)
        .order('sort_order')

      if (error) throw error

      const definitions: QuantityStateDefinition[] = (data || []).map(item => ({
        id: item.id,
        stateName: item.state_name,
        displayName: item.display_name,
        description: item.description,
        sortOrder: item.sort_order,
        isActive: item.is_active,
        allowedTransitions: item.allowed_transitions || [],
        requiredPermissions: item.required_permissions || [],
        stageCategory: item.stage_category,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }))

      console.log('✅ Loaded state definitions:', definitions.length, 'states')
      setStateDefinitions(definitions)
    } catch (err) {
      console.error('❌ Failed to load state definitions:', err)
    }
  }, [])

  // Load current quantities for an order line
  const loadQuantities = useCallback(async (id: string) => {
    if (!id) return

    setIsLoading(true)
    setError(null)

    try {
      const { data, error } = await supabase
        .from('order_line_quantities')
        .select('*')
        .eq('order_line_id', id)
        .single()

      if (error) throw error

      if (data) {
        const quantities: QuantityData = {
          orderLineId: data.order_line_id,
          totalOrderQuantity: data.total_order_quantity || 0,
          pendingProcurementArrangement: data.pending_procurement || 0,
          requestedFromStock: data.requested_from_stock || 0,
          awaitingKittingPacking: data.awaiting_kitting_packing || 0,
          inKittingPacking: data.in_kitting_packing || 0,
          onHoldAtKittingPacking: data.on_hold_kitting || 0,
          kittedPackedAwaitingScreeningQc: data.kitted_awaiting_qc || 0,
          inScreeningQc: data.in_screening_qc || 0,
          onHoldAtScreeningQc: data.on_hold_qc || 0,
          screeningQcPassedReadyForInvoice: data.qc_passed_ready_invoice || 0,
          screeningQcRejected: data.qc_rejected || 0,
          invoiced: data.invoiced || 0,
          shippedDelivered: data.shipped_delivered || 0,
          cancelled: data.cancelled || 0,
          lastUpdatedAt: data.updated_at,
          lastUpdatedBy: data.last_updated_by,
          notes: data.notes
        }
        setQuantities(quantities)
      }
    } catch (err) {
      console.error('Failed to load quantities:', err)
      setError('Failed to load quantity data')
      toast.error('Failed to load quantity data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Transition quantity between states
  const transitionQuantity = useCallback(async (transition: QuantityTransition): Promise<TransitionResult> => {
    if (!user) {
      return { success: false, message: 'User not authenticated' }
    }

    try {
      // Validate transition permissions
      console.log('🔍 Validating transition:', {
        fromState: transition.fromState,
        toState: transition.toState,
        userId: user.id
      })
      
      const canTransition = await validateTransition(transition.fromState, transition.toState)
      if (!canTransition) {
        console.error('❌ Transition validation failed')
        return { success: false, message: 'Insufficient permissions for this transition' }
      }
      
      console.log('✅ Transition validation passed')

      // Use the QuantityTransitionService for consistency
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      
      if (result.success) {
        console.log('🔄 Transition successful, invalidating all related queries...')
        
        // Immediately reload quantities
        await loadQuantities(transition.orderLineId)
        
        // Aggressively invalidate all related queries
        const invalidations = [
          queryClient.invalidateQueries({ queryKey: ['ct_numbers'] }),
          queryClient.invalidateQueries({ queryKey: ['ct_numbers', transition.orderLineId] }),
          queryClient.invalidateQueries({ queryKey: ['order-quantities'] }),
          queryClient.invalidateQueries({ queryKey: ['order-quantities', transition.orderLineId] }),
          queryClient.invalidateQueries({ queryKey: ['orders'] }),
          queryClient.invalidateQueries({ queryKey: ['quantity-history', transition.orderLineId] }),
          queryClient.invalidateQueries({ queryKey: ['quantity-logs'] })
        ]
        
        await Promise.all(invalidations)
        
        // Force a refetch of all queries
        await queryClient.refetchQueries({ queryKey: ['orders'] })
        
        console.log('✅ All queries invalidated and refetched')
        
        return result
      } else {
        return result
      }
    } catch (err) {
      console.error('Quantity transition failed:', err)
      const message = err instanceof Error ? err.message : 'Quantity transition failed'
      toast.error(message)
      return { success: false, message }
    }
  }, [user, loadQuantities, queryClient])

  // Validate if user can perform a state transition
  const validateTransition = useCallback(async (fromState: QuantityState, toState: QuantityState): Promise<boolean> => {
    try {
      console.log('📋 validateTransition called:', {
        fromState,
        toState,
        stateDefinitionsCount: stateDefinitions.length
      })
      
      // Check if state definitions are loaded
      if (stateDefinitions.length === 0) {
        console.warn('⚠️ State definitions not loaded yet! Loading now...')
        
        // Load definitions directly
        try {
          const { data, error } = await supabase
            .from('quantity_state_definitions')
            .select('*')
            .eq('is_active', true)
            .order('sort_order')
          
          if (error) throw error
          
          const definitions: QuantityStateDefinition[] = (data || []).map(item => ({
            id: item.id,
            stateName: item.state_name,
            displayName: item.display_name,
            description: item.description,
            sortOrder: item.sort_order,
            isActive: item.is_active,
            allowedTransitions: item.allowed_transitions || [],
            requiredPermissions: item.required_permissions || [],
            stageCategory: item.stage_category,
            createdAt: item.created_at,
            updatedAt: item.updated_at
          }))
          
          console.log('✅ Loaded definitions for validation:', definitions.length)
          
          // Use the loaded definitions for this validation
          const stateDefinition = definitions.find(def => def.stateName === fromState)
          if (!stateDefinition) {
            console.error('❌ State definition not found for:', fromState)
            return false
          }
          
          console.log('📊 State definition found:', {
            stateName: stateDefinition.stateName,
            allowedTransitions: stateDefinition.allowedTransitions,
            requiredPermissions: stateDefinition.requiredPermissions
          })
          
          // Check if transition is allowed
          if (!stateDefinition.allowedTransitions.includes(toState)) {
            console.error('❌ Transition not allowed:', fromState, '→', toState)
            return false
          }
          
          // Check if user has required permissions
          const requiredPermissions = stateDefinition.requiredPermissions || []
          console.log('🔐 Checking permissions:', requiredPermissions)
          
          for (const permission of requiredPermissions) {
            const hasIt = hasPermission(permission as any)
            console.log(`  - ${permission}: ${hasIt ? '✅' : '❌'}`)
            if (!hasIt) {
              console.error('❌ Missing permission:', permission)
              return false
            }
          }
          
          console.log('✅ All validation checks passed!')
          return true
          
        } catch (err) {
          console.error('❌ Failed to load state definitions for validation:', err)
          return false
        }
      }
      
      // Find state definition
      const stateDefinition = stateDefinitions.find(def => def.stateName === fromState)
      if (!stateDefinition) {
        console.error('❌ State definition not found for:', fromState)
        console.log('Available states:', stateDefinitions.map(d => d.stateName))
        return false
      }

      console.log('📊 State definition found:', {
        stateName: stateDefinition.stateName,
        allowedTransitions: stateDefinition.allowedTransitions,
        requiredPermissions: stateDefinition.requiredPermissions
      })

      // Check if transition is allowed
      if (!stateDefinition.allowedTransitions.includes(toState)) {
        console.error('❌ Transition not allowed:', fromState, '→', toState)
        return false
      }

      // Check if user has required permissions
      const requiredPermissions = stateDefinition.requiredPermissions || []
      console.log('🔐 Checking permissions:', requiredPermissions)
      
      for (const permission of requiredPermissions) {
        const hasIt = hasPermission(permission as any)
        console.log(`  - ${permission}: ${hasIt ? '✅' : '❌'}`)
        if (!hasIt) {
          console.error('❌ Missing permission:', permission)
          return false
        }
      }

      console.log('✅ All validation checks passed!')
      return true
    } catch (err) {
      console.error('💥 Transition validation error:', err)
      return false
    }
  }, [stateDefinitions, hasPermission])

  // Get available transitions for current state
  const getAvailableTransitions = useCallback(async (fromState: QuantityState): Promise<QuantityState[]> => {
    try {
      const stateDefinition = stateDefinitions.find(def => def.stateName === fromState)
      if (!stateDefinition) return []

      // Filter transitions based on user permissions
      const availableTransitions: QuantityState[] = []
      for (const toState of stateDefinition.allowedTransitions) {
        const canTransition = await validateTransition(fromState, toState as QuantityState)
        if (canTransition) {
          availableTransitions.push(toState as QuantityState)
        }
      }

      return availableTransitions
    } catch (err) {
      console.error('Failed to get available transitions:', err)
      return []
    }
  }, [stateDefinitions, validateTransition])

  // Get quantity history for an order line
  const getQuantityHistory = useCallback(async (id: string): Promise<QuantityLog[]> => {
    try {
      const { data, error } = await supabase
        .from('quantity_logs')
        .select('*')
        .eq('order_line_id', id)
        .order('timestamp', { ascending: false })

      if (error) throw error

      return (data || []).map(log => ({
        id: log.id,
        orderLineId: log.order_line_id,
        fromState: log.from_state,
        toState: log.to_state,
        quantityMoved: log.quantity_moved || 0,
        reason: log.reason_text,
        rejectionReason: log.rejection_reason,
        ctNumbers: log.associated_ct_numbers || [],
        location: log.location || 'SB',
        metadata: log.metadata,
        createdBy: log.created_by || log.user_id,
        createdAt: log.timestamp
      }))
    } catch (err) {
      console.error('Failed to get quantity history:', err)
      return []
    }
  }, [])

  // Get active holds for an order line
  const getActiveHolds = useCallback(async (id?: string): Promise<QuantityHold[]> => {
    try {
      let query = supabase
        .from('quantity_holds')
        .select('*')
        .eq('is_active', true)
        .order('held_at', { ascending: false })

      if (id) {
        query = query.eq('order_line_id', id)
      }

      const { data, error } = await query

      if (error) throw error

      return (data || []).map(hold => ({
        id: hold.id,
        orderLineId: hold.order_line_id,
        quantityHeld: hold.quantity_held,
        holdStage: hold.hold_stage,
        holdReason: hold.hold_reason,
        holdNotes: hold.hold_notes,
        heldBy: hold.held_by,
        heldAt: hold.held_at,
        releasedBy: hold.released_by,
        releasedAt: hold.released_at,
        isActive: hold.is_active,
        resolutionAction: hold.resolution_action,
        resolutionNotes: hold.resolution_notes,
        createdAt: hold.created_at,
        updatedAt: hold.updated_at
      }))
    } catch (err) {
      console.error('Failed to get active holds:', err)
      return []
    }
  }, [])

  // Load quantities when orderLineId changes
  useEffect(() => {
    if (orderLineId) {
      loadQuantities(orderLineId)
    }
  }, [orderLineId, loadQuantities])

  // Load state definitions on mount
  useEffect(() => {
    loadStateDefinitions()
  }, [loadStateDefinitions])

  // Set up real-time subscription for quantity changes
  useEffect(() => {
    if (!orderLineId) return

    const channel = supabase
      .channel(`quantity-tracking-${orderLineId}-${Date.now()}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_line_quantities',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('📊 Quantity update received:', payload)
          await loadQuantities(orderLineId)
          // Also invalidate related queries
          await queryClient.invalidateQueries({ queryKey: ['orders'] })
          await queryClient.invalidateQueries({ queryKey: ['ct_numbers', orderLineId] })
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quantity_logs',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('📝 Quantity log update received:', payload)
          // Slight delay to ensure database consistency
          setTimeout(async () => {
            await loadQuantities(orderLineId)
            await queryClient.invalidateQueries({ queryKey: ['orders'] })
          }, 100)
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ct_numbers',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('🏷️ CT number update received, refreshing quantities:', payload)
          await loadQuantities(orderLineId)
          await queryClient.invalidateQueries({ queryKey: ['orders'] })
          await queryClient.invalidateQueries({ queryKey: ['ct_numbers', orderLineId] })
        }
      )
      .subscribe((status) => {
        console.log(`📡 Quantity tracking subscription status for ${orderLineId}:`, status)
      })

    return () => {
      console.log(`🔌 Cleaning up quantity tracking subscription for ${orderLineId}`)
      supabase.removeChannel(channel)
    }
  }, [orderLineId, loadQuantities, queryClient])

  return {
    // Core state
    quantities,
    isLoading,
    error,
    stateDefinitions,
    
    // Actions
    loadQuantities,
    transitionQuantity,
    validateTransition,
    getAvailableTransitions,
    getQuantityHistory,
    getActiveHolds,
    
    // Utility functions
    getTotalInProgress: () => {
      if (!quantities) return 0
      return quantities.inKittingPacking + quantities.inScreeningQc
    },
    
    getTotalOnHold: () => {
      if (!quantities) return 0
      return quantities.onHoldAtKittingPacking + quantities.onHoldAtScreeningQc
    },
    
    getTotalCompleted: () => {
      if (!quantities) return 0
      return quantities.shippedDelivered + quantities.cancelled
    },

    getProgressPercentage: () => {
      if (!quantities || quantities.totalOrderQuantity === 0) return 0
      const completed = quantities.shippedDelivered + quantities.cancelled
      return Math.round((completed / quantities.totalOrderQuantity) * 100)
    },

    getStateDisplayName: (stateName: QuantityState) => {
      const definition = stateDefinitions.find(def => def.stateName === stateName)
      return definition?.displayName || stateName.replace(/_/g, ' ')
    },

    getStageCategory: (stateName: QuantityState) => {
      const definition = stateDefinitions.find(def => def.stateName === stateName)
      return definition?.stageCategory || 'final'
    }
  }
}