import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useOrders, OrderLineWithDetails } from '@/hooks/useOrders'
import { QuantityTransitionService, QuantityTransition } from '@/services/QuantityTransitionService'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface QCTask {
  id: string
  order: OrderLineWithDetails
  priority: 'urgent' | 'high' | 'normal' | 'low'
  quantity: number
  assignedTo: string | null
  estimatedTime: string
  ctNumbers: string[]
  dueTime: Date
  complexity: 'simple' | 'medium' | 'complex'
  hasFailedPreviously: boolean
  qcType: 'visual_only' | 'functional_only' | 'visual_functional' | 'full_inspection'
  customerRequirements: string
  assignedAt?: Date
  previousFailureCount?: number
}

export interface CurrentQCTask {
  id: string
  orderUid: string
  partNumber: string
  description: string
  quantity: number
  startedAt: Date
  estimatedCompletion: Date
  progress: number
  status: 'in_progress' | 'paused' | 'completed'
  currentStage: 'visual_inspection' | 'functional_test' | 'documentation' | 'final_review'
  ctNumbers: string[]
  assignedTo: string
  qcType: QCTask['qcType']
}

export interface QCHoldItem {
  id: string
  orderUid: string
  partNumber: string
  description: string
  quantity: number
  holdReason: string
  holdDate: Date
  holdBy: string
  priority: 'urgent' | 'high' | 'medium' | 'low'
  ctNumbers: string[]
  orderLineId: string
  canResolve?: boolean
}

export interface QCStats {
  qcQueue: number
  unassigned: number
  onHold: number
  rework: number
  myTasks: number
  inProgress: number
  passedToday: number
  failedToday: number
}

export function useQCQueue() {
  const { user, hasPermission } = useAuth()
  const { data: orders, isLoading: ordersLoading } = useOrders()
  const [qcQueue, setQCQueue] = useState<QCTask[]>([])
  const [currentTasks, setCurrentTasks] = useState<CurrentQCTask[]>([])
  const [holdItems, setHoldItems] = useState<QCHoldItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Transform orders data into QC queue
  const transformOrdersToQCQueue = useCallback((orders: OrderLineWithDetails[]): QCTask[] => {
    if (!orders) return []

    return orders
      .filter(order => {
        // Only include orders that have quantities awaiting QC
        const quantities = order.quantities
        return quantities && quantities.kitted_awaiting_qc > 0
      })
      .map(order => {
        const quantities = order.quantities!
        
        // Determine priority based on ETA and previous failures
        let priority: 'urgent' | 'high' | 'normal' | 'low' = 'normal'
        if (order.currentEtaStatus === 'overdue' || order.currentEtaStatus === 'final') {
          priority = 'urgent'
        } else if (order.currentEtaStatus === 'third' || quantities.qc_rejected > 0) {
          priority = 'high'
        } else if (quantities.kitted_awaiting_qc <= 2) {
          priority = 'low'
        }

        // Determine QC type based on part category
        let qcType: QCTask['qcType'] = 'visual_functional'
        const category = order.category_name?.toLowerCase()
        if (category?.includes('screen') || category?.includes('lcd')) {
          qcType = 'visual_only'
        } else if (category?.includes('battery') || category?.includes('power')) {
          qcType = 'functional_only'
        } else if (category?.includes('motherboard') || category?.includes('main board')) {
          qcType = 'full_inspection'
        }

        // Determine complexity
        let complexity: 'simple' | 'medium' | 'complex' = 'medium'
        if (qcType === 'visual_only' && quantities.kitted_awaiting_qc <= 5) {
          complexity = 'simple'
        } else if (qcType === 'full_inspection' || quantities.kitted_awaiting_qc > 10) {
          complexity = 'complex'
        }

        // Customer requirements based on customer
        let customerRequirements = 'Standard QC protocol'
        if (order.customer_name?.toLowerCase().includes('hp')) {
          customerRequirements = 'HP Quality Standards - Extended testing required'
        } else if (order.customer_name?.toLowerCase().includes('lenovo')) {
          customerRequirements = 'Lenovo QC Protocol - Documentation required'
        }

        // Estimate time
        const baseTime = complexity === 'simple' ? 20 : complexity === 'medium' ? 45 : 90
        const timeMultiplier = Math.min(quantities.kitted_awaiting_qc / 3, 2)
        const estimatedMinutes = Math.round(baseTime * timeMultiplier)
        const estimatedTime = estimatedMinutes > 60 
          ? `${Math.floor(estimatedMinutes / 60)}h ${estimatedMinutes % 60}m`
          : `${estimatedMinutes} min`

        // Calculate due time
        const dueTime = order.current_eta 
          ? new Date(new Date(order.current_eta).getTime() - 12 * 60 * 60 * 1000) // 12 hours before ETA
          : new Date(Date.now() + 8 * 60 * 60 * 1000) // Default to 8 hours

        return {
          id: order.id || `qc-${order.uid}`,
          order,
          priority,
          quantity: quantities.kitted_awaiting_qc,
          assignedTo: null, // TODO: Fetch from database
          estimatedTime,
          ctNumbers: [], // TODO: Fetch CT numbers for this quantity
          dueTime,
          complexity,
          hasFailedPreviously: quantities.qc_rejected > 0,
          qcType,
          customerRequirements,
          previousFailureCount: quantities.qc_rejected
        } as QCTask
      })
      .sort((a, b) => {
        // Sort by priority first, then by previous failures, then by due time
        const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[a.priority] - priorityOrder[b.priority]
        }
        if (a.hasFailedPreviously !== b.hasFailedPreviously) {
          return a.hasFailedPreviously ? -1 : 1 // Failed items first
        }
        return a.dueTime.getTime() - b.dueTime.getTime()
      })
  }, [])

  // Load QC queue data
  const loadQCQueue = useCallback(async () => {
    if (!orders || ordersLoading) return

    setIsLoading(true)
    setError(null)

    try {
      const queue = transformOrdersToQCQueue(orders)
      setQCQueue(queue)

      // TODO: Load current tasks and hold items from database
      setCurrentTasks([])
      setHoldItems([])

    } catch (err) {
      console.error('Failed to load QC queue:', err)
      setError('Failed to load QC queue')
      toast.error('Failed to load QC queue')
    } finally {
      setIsLoading(false)
    }
  }, [orders, ordersLoading, transformOrdersToQCQueue])

  // Assign QC task to current user
  const assignQCTaskToMe = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      console.log('Assigning QC task to user:', taskId, user.email)
      
      setQCQueue(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, assignedTo: user.email?.split('@')[0] || 'Unknown', assignedAt: new Date() }
          : task
      ))

      toast.success('QC task assigned successfully')
      return true
    } catch (err) {
      console.error('Failed to assign QC task:', err)
      toast.error('Failed to assign QC task')
      return false
    }
  }, [user])

  // Start QC task with real quantity transition
  const startQCTask = useCallback(async (taskId: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const task = qcQueue.find(t => t.id === taskId)
      if (!task) {
        toast.error('QC task not found')
        return false
      }

      const orderLineId = task.order.id || ''
      if (!orderLineId) {
        toast.error('Order line ID not found')
        return false
      }

      // Create quantity transition: kitted_packed_awaiting_screening_qc → in_screening_qc
      const transition: QuantityTransition = {
        orderLineId,
        fromState: 'kitted_packed_awaiting_screening_qc',
        toState: 'in_screening_qc',
        quantity: task.quantity,
        reason: `QC task started by ${user.email?.split('@')[0]}`,
        ctNumbers: task.ctNumbers,
        metadata: {
          taskId,
          assignedTo: user.email?.split('@')[0],
          workflowStage: 'qc_start',
          qcType: task.qcType
        }
      }

      // Execute quantity transition
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      if (!result.success) {
        toast.error(`Failed to start QC: ${result.message}`)
        return false
      }

      const currentTask: CurrentQCTask = {
        id: taskId,
        orderUid: task.order.uid || '',
        partNumber: task.order.customer_part_number || '',
        description: task.order.bpi_description || '',
        quantity: task.quantity,
        startedAt: new Date(),
        estimatedCompletion: new Date(Date.now() + (parseInt(task.estimatedTime) * 60 * 1000)),
        progress: 0,
        status: 'in_progress',
        currentStage: 'visual_inspection',
        ctNumbers: task.ctNumbers,
        assignedTo: user.email?.split('@')[0] || 'Unknown',
        qcType: task.qcType
      }

      setCurrentTasks(prev => [...prev, currentTask])
      setQCQueue(prev => prev.filter(t => t.id !== taskId))

      toast.success(`QC started - ${task.quantity} units moved to In Screening QC`)
      return true
    } catch (err) {
      console.error('Failed to start QC task:', err)
      toast.error('Failed to start QC task')
      return false
    }
  }, [user, qcQueue])

  // Pass QC task with real quantity transition
  const passQCTask = useCallback(async (taskId: string, notes?: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const currentTask = currentTasks.find(t => t.id === taskId)
      if (!currentTask) {
        toast.error('QC task not found')
        return false
      }

      // Find the original order for this task
      const originalOrder = orders?.find(order => order.uid === currentTask.orderUid)
      if (!originalOrder?.id) {
        toast.error('Original order not found')
        return false
      }

      // Create quantity transition: in_screening_qc → screening_qc_passed_ready_for_invoice
      const transition: QuantityTransition = {
        orderLineId: originalOrder.id,
        fromState: 'in_screening_qc',
        toState: 'screening_qc_passed_ready_for_invoice',
        quantity: currentTask.quantity,
        reason: `QC passed by ${user.email?.split('@')[0]}${notes ? `: ${notes}` : ''}`,
        ctNumbers: currentTask.ctNumbers,
        metadata: {
          taskId,
          passedBy: user.email?.split('@')[0],
          workflowStage: 'qc_pass',
          qcType: currentTask.qcType,
          notes,
          completionTime: new Date().toISOString()
        }
      }

      // Execute quantity transition
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      if (!result.success) {
        toast.error(`Failed to pass QC: ${result.message}`)
        return false
      }

      setCurrentTasks(prev => prev.filter(t => t.id !== taskId))

      toast.success(`QC passed - ${currentTask.quantity} units moved to Ready for Invoice`)
      return true
    } catch (err) {
      console.error('Failed to pass QC task:', err)
      toast.error('Failed to pass QC task')
      return false
    }
  }, [user, currentTasks, orders])

  // Fail QC task with real quantity transition
  const failQCTask = useCallback(async (taskId: string, failureReason: string, rejectionNotes?: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const currentTask = currentTasks.find(t => t.id === taskId)
      if (!currentTask) {
        toast.error('QC task not found')
        return false
      }

      // Find the original order for this task
      const originalOrder = orders?.find(order => order.uid === currentTask.orderUid)
      if (!originalOrder?.id) {
        toast.error('Original order not found')
        return false
      }

      // Create quantity transition: in_screening_qc → screening_qc_rejected
      const transition: QuantityTransition = {
        orderLineId: originalOrder.id,
        fromState: 'in_screening_qc',
        toState: 'screening_qc_rejected',
        quantity: currentTask.quantity,
        reason: `QC failed by ${user.email?.split('@')[0]}`,
        rejectionReason: failureReason,
        ctNumbers: currentTask.ctNumbers,
        metadata: {
          taskId,
          rejectedBy: user.email?.split('@')[0],
          workflowStage: 'qc_fail',
          qcType: currentTask.qcType,
          failureReason,
          rejectionNotes,
          rejectionTime: new Date().toISOString()
        }
      }

      // Execute quantity transition
      const result = await QuantityTransitionService.executeTransition(transition, user.id)
      if (!result.success) {
        toast.error(`Failed to reject QC: ${result.message}`)
        return false
      }

      setCurrentTasks(prev => prev.filter(t => t.id !== taskId))

      // TODO: Trigger WhatsApp notification for QC rejection
      console.log('🚨 QC Rejection - WhatsApp notification should be sent:', {
        orderUid: currentTask.orderUid,
        partNumber: currentTask.partNumber,
        failureReason,
        quantity: currentTask.quantity
      })

      toast.error(`QC rejected - ${currentTask.quantity} units failed: ${failureReason}`)
      return true
    } catch (err) {
      console.error('Failed to fail QC task:', err)
      toast.error('Failed to fail QC task')
      return false
    }
  }, [user, currentTasks, orders])

  // Hold QC task
  const holdQCTask = useCallback(async (taskId: string, holdReason: string, holdNotes?: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const currentTask = currentTasks.find(t => t.id === taskId)
      if (!currentTask) {
        toast.error('QC task not found')
        return false
      }

      console.log('Holding QC task:', taskId, 'reason:', holdReason)

      // Create hold item
      const holdItem: QCHoldItem = {
        id: `hold-${taskId}`,
        orderUid: currentTask.orderUid,
        partNumber: currentTask.partNumber,
        description: currentTask.description,
        quantity: currentTask.quantity,
        holdReason,
        holdDate: new Date(),
        holdBy: user.email?.split('@')[0] || 'Unknown',
        priority: 'medium',
        ctNumbers: currentTask.ctNumbers,
        orderLineId: taskId,
        canResolve: true
      }

      setHoldItems(prev => [...prev, holdItem])
      setCurrentTasks(prev => prev.filter(t => t.id !== taskId))

      toast.warning(`QC task placed on hold: ${holdReason}`)
      return true
    } catch (err) {
      console.error('Failed to hold QC task:', err)
      toast.error('Failed to hold QC task')
      return false
    }
  }, [user, currentTasks])

  // Resolve hold
  const resolveQCHold = useCallback(async (holdId: string, resolutionAction: 'release' | 'reject' | 'escalate', resolutionNotes?: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const holdItem = holdItems.find(h => h.id === holdId)
      if (!holdItem) {
        toast.error('Hold item not found')
        return false
      }

      console.log('Resolving QC hold:', holdId, 'action:', resolutionAction)

      if (resolutionAction === 'release') {
        // Put back into QC queue
        // TODO: Implement putting back into queue
        toast.success(`Hold resolved: ${holdItem.partNumber} returned to QC queue`)
      } else if (resolutionAction === 'reject') {
        // Move to rejected status
        toast.warning(`Hold resolved: ${holdItem.partNumber} marked as rejected`)
      } else {
        // Escalate to supervisor
        toast.info(`Hold escalated: ${holdItem.partNumber} sent to supervisor`)
      }

      setHoldItems(prev => prev.filter(h => h.id !== holdId))
      return true
    } catch (err) {
      console.error('Failed to resolve hold:', err)
      toast.error('Failed to resolve hold')
      return false
    }
  }, [user, holdItems])

  // Update QC progress
  const updateQCProgress = useCallback(async (taskId: string, progress: number, stage?: CurrentQCTask['currentStage']): Promise<boolean> => {
    try {
      setCurrentTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { 
              ...task, 
              progress: Math.min(100, Math.max(0, progress)),
              currentStage: stage || task.currentStage
            } 
          : task
      ))
      return true
    } catch (err) {
      console.error('Failed to update QC progress:', err)
      return false
    }
  }, [])

  // Pause QC task
  const pauseQCTask = useCallback(async (taskId: string): Promise<boolean> => {
    try {
      setCurrentTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status: 'paused' } : task
      ))
      toast.success('QC task paused')
      return true
    } catch (err) {
      console.error('Failed to pause QC task:', err)
      toast.error('Failed to pause QC task')
      return false
    }
  }, [])

  // Calculate QC stats
  const calculateStats = useCallback((): QCStats => {
    const userEmail = user?.email?.split('@')[0]
    
    return {
      qcQueue: qcQueue.length,
      unassigned: qcQueue.filter(t => !t.assignedTo).length,
      onHold: holdItems.length,
      rework: qcQueue.filter(t => t.hasFailedPreviously).length,
      myTasks: qcQueue.filter(t => t.assignedTo === userEmail).length + currentTasks.length,
      inProgress: currentTasks.filter(t => t.status === 'in_progress').length,
      passedToday: 0, // TODO: Calculate from database
      failedToday: 0  // TODO: Calculate from database
    }
  }, [qcQueue, currentTasks, holdItems, user])

  // Load data when orders change
  useEffect(() => {
    loadQCQueue()
  }, [loadQCQueue])

  // Set up real-time subscriptions for QC-related changes
  useEffect(() => {
    if (!user) return

    const channel = supabase
      .channel('qc-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_line_quantities'
        },
        (payload) => {
          console.log('QC quantities updated:', payload)
          loadQCQueue()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quantity_holds'
        },
        (payload) => {
          console.log('QC holds updated:', payload)
          loadQCQueue()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, loadQCQueue])

  const stats = calculateStats()
  
  return {
    // Core data
    qcQueue,
    currentTasks,
    holdItems,
    isLoading: isLoading || ordersLoading,
    error,
    stats,

    // Convenience properties for backwards compatibility
    totalItems: stats.totalQueue || 0,
    inProgress: stats.inProgress || 0,
    passedToday: stats.passedToday || 0,
    rejectedToday: stats.rejectedToday || 0,
    holdItemsCount: holdItems.length || 0,

    // Actions
    assignQCTaskToMe,
    startQCTask,
    passQCTask,
    failQCTask,
    holdQCTask,
    resolveQCHold,
    pauseQCTask,
    updateQCProgress,
    
    // Utilities
    refresh: loadQCQueue,
    canManageQC: hasPermission('manage_qc'),
    canAssignTasks: hasPermission('assign_qc_tasks'),
    canResolveHolds: hasPermission('resolve_qc_holds')
  }
}