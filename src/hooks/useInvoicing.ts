import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useOrders, OrderLineWithDetails } from '@/hooks/useOrders'
import { useInvoiceTemplates } from '@/hooks/useInvoiceTemplates'
import { QuantityTransitionService, QuantityTransition } from '@/services/QuantityTransitionService'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface ReadyToInvoiceItem {
  id: string
  order: OrderLineWithDetails
  readyQuantity: number
  unitPrice: number
  totalValue: number
  qcPassedDate: Date
  priority: 'urgent' | 'high' | 'normal' | 'low'
  customerInvoiceRef: string
  estimatedShipDate: Date
  ctNumbers: string[]
  margin?: number
  customerPO?: string
  specialInstructions?: string
}

export interface PendingInvoice {
  id: string
  invoiceNumber: string
  customerName: string
  totalAmount: number
  orderCount: number
  createdDate: Date
  dueDate: Date
  status: 'draft' | 'pending_approval' | 'ready_to_send' | 'sent' | 'paid'
  assignedTo: string
  orderLineIds: string[]
  invoiceItems: InvoiceItem[]
  paymentTerms?: string
  notes?: string
}

export interface InvoiceItem {
  orderLineId: string
  partNumber: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
  ctNumbers: string[]
}

// InvoiceTemplate moved to useInvoiceTemplates hook

export interface InvoicingStats {
  readyToInvoice: number
  pendingInvoices: number
  totalValue: number
  urgentItems: number
  draftInvoices: number
  sentInvoices: number
  overdueInvoices: number
  monthlyTotal: number
}

export function useInvoicing() {
  const { user, hasPermission } = useAuth()
  const { data: orders, isLoading: ordersLoading } = useOrders()
  const { templates: invoiceTemplates } = useInvoiceTemplates()
  const [readyToInvoice, setReadyToInvoice] = useState<ReadyToInvoiceItem[]>([])
  const [pendingInvoices, setPendingInvoices] = useState<PendingInvoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Transform orders data into ready-to-invoice items
  const transformOrdersToInvoiceItems = useCallback((orders: OrderLineWithDetails[]): ReadyToInvoiceItem[] => {
    if (!orders) return []

    return orders
      .filter(order => {
        // Only include orders that have quantities ready for invoice
        const quantities = order.quantities
        return quantities && quantities.qc_passed_ready_invoice > 0
      })
      .map(order => {
        const quantities = order.quantities!
        
        // Determine priority based on ETA and customer
        let priority: 'urgent' | 'high' | 'normal' | 'low' = 'normal'
        if (order.currentEtaStatus === 'overdue' || order.currentEtaStatus === 'final') {
          priority = 'urgent'
        } else if (order.currentEtaStatus === 'third' || order.customer_name?.toLowerCase().includes('hp')) {
          priority = 'high'
        } else if (quantities.qc_passed_ready_invoice <= 2) {
          priority = 'low'
        }

        // Calculate estimated ship date (usually next business day)
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        if (tomorrow.getDay() === 6) { // Saturday
          tomorrow.setDate(tomorrow.getDate() + 2)
        } else if (tomorrow.getDay() === 0) { // Sunday
          tomorrow.setDate(tomorrow.getDate() + 1)
        }

        // Generate customer invoice reference
        const customerPrefix = order.customer_name?.substring(0, 3).toUpperCase() || 'CUS'
        const datePrefix = new Date().toISOString().slice(2, 10).replace(/-/g, '')
        const customerInvoiceRef = `INV-${customerPrefix}-${order.uid}-${datePrefix}`

        // Calculate unit price and total value
        const unitPrice = (order as any).unit_price || 0
        const totalValue = unitPrice * quantities.qc_passed_ready_invoice

        return {
          id: order.id || `invoice-${order.uid}`,
          order,
          readyQuantity: quantities.qc_passed_ready_invoice,
          unitPrice,
          totalValue,
          qcPassedDate: new Date(quantities.updated_at || Date.now()),
          priority,
          customerInvoiceRef,
          estimatedShipDate: tomorrow,
          ctNumbers: [], // TODO: Fetch actual CT numbers for ready quantities
          margin: (order as any).margin,
          customerPO: order.po_number,
          specialInstructions: (order as any).special_instructions
        } as ReadyToInvoiceItem
      })
      .sort((a, b) => {
        // Sort by priority first, then by QC passed date
        const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 }
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[a.priority] - priorityOrder[b.priority]
        }
        return a.qcPassedDate.getTime() - b.qcPassedDate.getTime()
      })
  }, [])

  // Load invoicing data
  const loadInvoicingData = useCallback(async () => {
    if (!orders || ordersLoading) return

    setIsLoading(true)
    setError(null)

    try {
      // Transform orders to ready-to-invoice items
      const invoiceItems = transformOrdersToInvoiceItems(orders)
      setReadyToInvoice(invoiceItems)

      // Load pending invoices from database
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select('*')
        .in('status', ['draft', 'pending_approval', 'ready_to_send'])
        .order('created_at', { ascending: false })

      if (invoicesError) {
        console.error('Error loading pending invoices:', invoicesError)
      } else {
        const pendingInvoicesData: PendingInvoice[] = (invoicesData || []).map(invoice => ({
          id: invoice.id,
          invoiceNumber: invoice.invoice_number,
          customerName: invoice.customer_name,
          totalAmount: invoice.total_amount,
          orderCount: invoice.order_count || 1,
          createdDate: new Date(invoice.created_at),
          dueDate: new Date(invoice.due_date),
          status: invoice.status,
          assignedTo: invoice.assigned_to || 'Unassigned',
          orderLineIds: invoice.order_line_ids || [],
          invoiceItems: invoice.invoice_items || [],
          paymentTerms: invoice.payment_terms,
          notes: invoice.notes
        }))
        setPendingInvoices(pendingInvoicesData)
      }

      // Invoice templates are now loaded via useInvoiceTemplates hook

    } catch (err) {
      console.error('Failed to load invoicing data:', err)
      setError('Failed to load invoicing data')
      toast.error('Failed to load invoicing data')
    } finally {
      setIsLoading(false)
    }
  }, [orders, ordersLoading, transformOrdersToInvoiceItems])

  // Create invoice from selected items
  const createInvoice = useCallback(async (itemIds: string[]): Promise<string | null> => {
    if (!user) {
      toast.error('User not authenticated')
      return null
    }

    try {
      const selectedItems = readyToInvoice.filter(item => itemIds.includes(item.id))
      if (selectedItems.length === 0) {
        toast.error('No items selected for invoice creation')
        return null
      }

      // Group by customer
      const customerGroups = selectedItems.reduce((groups, item) => {
        const customerName = item.order.customer_name || 'Unknown Customer'
        if (!groups[customerName]) {
          groups[customerName] = []
        }
        groups[customerName].push(item)
        return groups
      }, {} as Record<string, ReadyToInvoiceItem[]>)

      const invoiceIds: string[] = []

      for (const [customerName, items] of Object.entries(customerGroups)) {
        const totalAmount = items.reduce((sum, item) => sum + item.totalValue, 0)
        const orderLineIds = items.map(item => item.order.id || '')
        
        // Generate invoice number
        const invoiceNumber = `BPI-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`
        
        const invoiceItems: InvoiceItem[] = items.map(item => ({
          orderLineId: item.order.id || '',
          partNumber: item.order.customer_part_number || '',
          description: item.order.bpi_description || '',
          quantity: item.readyQuantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalValue,
          ctNumbers: item.ctNumbers
        }))

        // Create invoice in database
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('invoices')
          .insert({
            invoice_number: invoiceNumber,
            customer_name: customerName,
            total_amount: totalAmount,
            order_count: items.length,
            status: 'draft',
            assigned_to: user.email?.split('@')[0] || 'Unknown',
            order_line_ids: orderLineIds,
            invoice_items: invoiceItems,
            payment_terms: 'Net 30',
            due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            created_by: user.id
          })
          .select()
          .single()

        if (invoiceError) {
          console.error('Error creating invoice:', invoiceError)
          throw invoiceError
        }

        invoiceIds.push(invoiceData.id)

        // Execute quantity transitions: screening_qc_passed_ready_for_invoice → invoiced
        for (const item of items) {
          if (item.order.id) {
            const transition: QuantityTransition = {
              orderLineId: item.order.id,
              fromState: 'screening_qc_passed_ready_for_invoice',
              toState: 'invoiced',
              quantity: item.readyQuantity,
              reason: `Invoiced - ${invoiceNumber}`,
              ctNumbers: item.ctNumbers,
              metadata: {
                invoiceId: invoiceData.id,
                invoiceNumber,
                customerName,
                workflowStage: 'invoicing',
                invoicedBy: user.email?.split('@')[0],
                invoiceAmount: item.totalValue
              }
            }

            const result = await QuantityTransitionService.executeTransition(transition, user.id)
            if (!result.success) {
              console.error(`Failed to update quantities for invoice item ${item.order.uid}:`, result.message)
            }
          }
        }

        console.log('Created invoice:', invoiceNumber, 'for', customerName, 'with', items.length, 'items')
      }

      // Refresh data
      await loadInvoicingData()

      toast.success(`Created ${invoiceIds.length} invoice(s) successfully`)
      return invoiceIds[0] // Return first invoice ID
    } catch (err) {
      console.error('Failed to create invoice:', err)
      toast.error('Failed to create invoice')
      return null
    }
  }, [user, readyToInvoice, loadInvoicingData])

  // Quick invoice for single item
  const createQuickInvoice = useCallback(async (itemId: string): Promise<string | null> => {
    return createInvoice([itemId])
  }, [createInvoice])

  // Send invoice
  const sendInvoice = useCallback(async (invoiceId: string, method: 'email' | 'print' | 'both' = 'email'): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const invoice = pendingInvoices.find(inv => inv.id === invoiceId)
      if (!invoice) {
        toast.error('Invoice not found')
        return false
      }

      // Update invoice status
      const { error } = await supabase
        .from('invoices')
        .update({
          status: 'sent',
          sent_at: new Date().toISOString(),
          sent_by: user.id,
          sent_method: method
        })
        .eq('id', invoiceId)

      if (error) throw error

      // TODO: Implement actual email/print sending logic
      console.log('Sending invoice via', method, ':', invoice.invoiceNumber)

      // Update local state
      setPendingInvoices(prev => prev.map(inv => 
        inv.id === invoiceId 
          ? { ...inv, status: 'sent' as const }
          : inv
      ))

      toast.success(`Invoice ${invoice.invoiceNumber} sent successfully`)
      return true
    } catch (err) {
      console.error('Failed to send invoice:', err)
      toast.error('Failed to send invoice')
      return false
    }
  }, [user, pendingInvoices])

  // Update invoice status
  const updateInvoiceStatus = useCallback(async (invoiceId: string, status: PendingInvoice['status'], notes?: string): Promise<boolean> => {
    if (!user) {
      toast.error('User not authenticated')
      return false
    }

    try {
      const { error } = await supabase
        .from('invoices')
        .update({
          status,
          notes,
          updated_at: new Date().toISOString(),
          updated_by: user.id
        })
        .eq('id', invoiceId)

      if (error) throw error

      // Update local state
      setPendingInvoices(prev => prev.map(inv => 
        inv.id === invoiceId 
          ? { ...inv, status, notes: notes || inv.notes }
          : inv
      ))

      toast.success('Invoice status updated successfully')
      return true
    } catch (err) {
      console.error('Failed to update invoice status:', err)
      toast.error('Failed to update invoice status')
      return false
    }
  }, [user])

  // Calculate invoicing stats
  const calculateStats = useCallback((): InvoicingStats => {
    const totalValue = readyToInvoice.reduce((sum, item) => sum + item.totalValue, 0)
    const urgentCount = readyToInvoice.filter(item => item.priority === 'urgent').length
    
    const now = new Date()
    const overdueInvoices = pendingInvoices.filter(inv => 
      inv.status === 'sent' && inv.dueDate < now
    ).length

    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    const monthlyInvoices = pendingInvoices.filter(inv => 
      inv.createdDate >= monthStart
    )
    const monthlyTotal = monthlyInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0)
    
    return {
      readyToInvoice: readyToInvoice.length,
      pendingInvoices: pendingInvoices.length,
      totalValue,
      urgentItems: urgentCount,
      draftInvoices: pendingInvoices.filter(inv => inv.status === 'draft').length,
      sentInvoices: pendingInvoices.filter(inv => inv.status === 'sent').length,
      overdueInvoices,
      monthlyTotal
    }
  }, [readyToInvoice, pendingInvoices])

  // Load data when orders change
  useEffect(() => {
    loadInvoicingData()
  }, [loadInvoicingData])

  // Set up real-time subscriptions for invoicing-related changes
  useEffect(() => {
    if (!user) return

    const channel = supabase
      .channel('invoicing-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_line_quantities'
        },
        (payload) => {
          console.log('Invoicing quantities updated:', payload)
          loadInvoicingData()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'invoices'
        },
        (payload) => {
          console.log('Invoices updated:', payload)
          loadInvoicingData()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, loadInvoicingData])

  return {
    // Core data
    readyToInvoice,
    pendingInvoices,
    invoiceTemplates,
    isLoading: isLoading || ordersLoading,
    error,
    stats: calculateStats(),

    // Actions
    createInvoice,
    createQuickInvoice,
    sendInvoice,
    updateInvoiceStatus,
    
    // Utilities
    refresh: loadInvoicingData,
    canCreateInvoices: hasPermission('admin'),
    canSendInvoices: hasPermission('admin'),
    canManageInvoices: hasPermission('admin'),
    
    // Helpers
    formatCurrency: (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount)
    },
    
    getTotalSelectedValue: (selectedItemIds: string[]) => {
      return readyToInvoice
        .filter(item => selectedItemIds.includes(item.id))
        .reduce((sum, item) => sum + item.totalValue, 0)
    }
  }
}