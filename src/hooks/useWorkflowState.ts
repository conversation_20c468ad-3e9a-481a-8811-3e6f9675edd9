import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { workflowStateManager, WorkflowType, WorkflowEvent } from '@/services/WorkflowStateManager'
import { toast } from 'sonner'

export interface WorkflowStateConfig {
  workflowType: WorkflowType
  persistenceKey?: string
  enablePersistence?: boolean
  enableRealTimeSync?: boolean
  enableOptimisticUpdates?: boolean
  syncInterval?: number
}

export interface WorkflowTaskState {
  id: string
  orderLineId: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'paused'
  progress: number
  assignedTo?: string
  startedAt?: string
  completedAt?: string
  metadata?: Record<string, any>
}

export interface WorkflowState {
  currentTasks: WorkflowTaskState[]
  assignedTasks: string[]
  taskProgress: Record<string, number>
  metrics: {
    completedToday: number
    averageTime: number
    successRate: number
  }
  lastSync: string
  isOnline: boolean
}

export function useWorkflowState(config: WorkflowStateConfig) {
  const { user } = useAuth()
  const [state, setState] = useState<WorkflowState>({
    currentTasks: [],
    assignedTasks: [],
    taskProgress: {},
    metrics: {
      completedToday: 0,
      averageTime: 0,
      successRate: 100
    },
    lastSync: new Date().toISOString(),
    isOnline: navigator.onLine
  })
  
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const unsubscribeRefs = useRef<(() => void)[]>([])
  const syncIntervalRef = useRef<NodeJS.Timeout>()

  // Generate persistence key
  const persistenceKey = useMemo(() => {
    return config.persistenceKey || `workflow-${config.workflowType}-${user?.id || 'anonymous'}`
  }, [config.persistenceKey, config.workflowType, user?.id])

  // Load persisted state on mount
  useEffect(() => {
    if (config.enablePersistence && user) {
      try {
        const persistedState = workflowStateManager.getPersistedState<WorkflowState>({
          key: persistenceKey,
          storage: 'localStorage',
          expirationTime: 8 * 60 * 60 * 1000 // 8 hours
        })

        if (persistedState) {
          setState(prev => ({
            ...prev,
            ...persistedState,
            isOnline: navigator.onLine
          }))
          console.log(`📥 Loaded persisted workflow state for ${config.workflowType}`)
        }
      } catch (err) {
        console.error('Failed to load persisted workflow state:', err)
      }
    }
    setIsLoading(false)
  }, [config.enablePersistence, config.workflowType, user, persistenceKey])

  // Persist state changes
  const persistState = useCallback((newState: WorkflowState) => {
    if (config.enablePersistence && user) {
      workflowStateManager.persistState({
        key: persistenceKey,
        storage: 'localStorage',
        expirationTime: 8 * 60 * 60 * 1000,
        syncAcrossComponents: true
      }, newState)
    }
  }, [config.enablePersistence, user, persistenceKey])

  // Update state with persistence
  const updateState = useCallback((updater: (prev: WorkflowState) => WorkflowState) => {
    setState(prev => {
      const newState = updater(prev)
      persistState(newState)
      return newState
    })
  }, [persistState])

  // Subscribe to workflow events
  useEffect(() => {
    if (!config.enableRealTimeSync || !user) return

    const subscriptions = [
      // Subscribe to task completion events
      workflowStateManager.subscribe('task_completed', (eventData) => {
        if (eventData.workflowType === config.workflowType && eventData.userId === user.id) {
          updateState(prev => ({
            ...prev,
            currentTasks: prev.currentTasks.map(task => 
              task.orderLineId === eventData.orderLineId
                ? { ...task, status: 'completed', completedAt: eventData.timestamp, progress: 100 }
                : task
            ),
            metrics: {
              ...prev.metrics,
              completedToday: prev.metrics.completedToday + 1
            },
            lastSync: new Date().toISOString()
          }))
          
          toast.success(`${config.workflowType} task completed successfully`)
        }
      }),

      // Subscribe to task assignment events
      workflowStateManager.subscribe('task_assigned', (eventData) => {
        if (eventData.workflowType === config.workflowType && eventData.userId === user.id) {
          updateState(prev => ({
            ...prev,
            assignedTasks: [...prev.assignedTasks, eventData.taskId || ''],
            lastSync: new Date().toISOString()
          }))
        }
      }),

      // Subscribe to state changes
      workflowStateManager.subscribe('state_changed', (eventData) => {
        if (eventData.workflowType === config.workflowType) {
          updateState(prev => ({
            ...prev,
            lastSync: new Date().toISOString()
          }))
        }
      }),

      // Subscribe to task failures
      workflowStateManager.subscribe('task_failed', (eventData) => {
        if (eventData.workflowType === config.workflowType && eventData.userId === user.id) {
          updateState(prev => ({
            ...prev,
            currentTasks: prev.currentTasks.map(task => 
              task.orderLineId === eventData.orderLineId
                ? { ...task, status: 'failed' }
                : task
            ),
            lastSync: new Date().toISOString()
          }))
          
          toast.error(`${config.workflowType} task failed`)
        }
      })
    ]

    unsubscribeRefs.current = subscriptions
    console.log(`🔔 Subscribed to workflow events for ${config.workflowType}`)

    return () => {
      subscriptions.forEach(unsubscribe => unsubscribe())
      unsubscribeRefs.current = []
    }
  }, [config.enableRealTimeSync, config.workflowType, user, updateState])

  // Set up periodic sync
  useEffect(() => {
    if (config.syncInterval && config.enableRealTimeSync) {
      syncIntervalRef.current = setInterval(() => {
        updateState(prev => ({
          ...prev,
          lastSync: new Date().toISOString(),
          isOnline: navigator.onLine
        }))
      }, config.syncInterval)

      return () => {
        if (syncIntervalRef.current) {
          clearInterval(syncIntervalRef.current)
        }
      }
    }
  }, [config.syncInterval, config.enableRealTimeSync, updateState])

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => {
      updateState(prev => ({ ...prev, isOnline: true }))
      toast.success('Connection restored')
    }

    const handleOffline = () => {
      updateState(prev => ({ ...prev, isOnline: false }))
      toast.warning('Working offline')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [updateState])

  // Start a new task
  const startTask = useCallback((orderLineId: string, metadata?: Record<string, any>) => {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const newTask: WorkflowTaskState = {
      id: taskId,
      orderLineId,
      status: 'in_progress',
      progress: 0,
      assignedTo: user?.email,
      startedAt: new Date().toISOString(),
      metadata
    }

    updateState(prev => ({
      ...prev,
      currentTasks: [...prev.currentTasks, newTask]
    }))

    // Emit task started event
    workflowStateManager.emit('task_started', {
      workflowType: config.workflowType,
      userId: user?.id || '',
      orderLineId,
      taskId,
      payload: newTask
    })

    return taskId
  }, [config.workflowType, user, updateState])

  // Update task progress
  const updateTaskProgress = useCallback((taskId: string, progress: number) => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.map(task =>
        task.id === taskId ? { ...task, progress: Math.min(100, Math.max(0, progress)) } : task
      ),
      taskProgress: {
        ...prev.taskProgress,
        [taskId]: progress
      }
    }))
  }, [updateState])

  // Complete a task
  const completeTask = useCallback((taskId: string, result?: any) => {
    updateState(prev => {
      const task = prev.currentTasks.find(t => t.id === taskId)
      if (!task) return prev

      return {
        ...prev,
        currentTasks: prev.currentTasks.map(t =>
          t.id === taskId 
            ? { ...t, status: 'completed', progress: 100, completedAt: new Date().toISOString() }
            : t
        ),
        metrics: {
          ...prev.metrics,
          completedToday: prev.metrics.completedToday + 1
        }
      }
    })

    // Emit task completed event
    workflowStateManager.emit('task_completed', {
      workflowType: config.workflowType,
      userId: user?.id || '',
      taskId,
      payload: result
    })
  }, [config.workflowType, user, updateState])

  // Fail a task
  const failTask = useCallback((taskId: string, reason?: string) => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.map(task =>
        task.id === taskId 
          ? { ...task, status: 'failed', metadata: { ...task.metadata, failureReason: reason } }
          : task
      )
    }))

    // Emit task failed event
    workflowStateManager.emit('task_failed', {
      workflowType: config.workflowType,
      userId: user?.id || '',
      taskId,
      payload: { reason }
    })
  }, [config.workflowType, user, updateState])

  // Pause a task
  const pauseTask = useCallback((taskId: string) => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.map(task =>
        task.id === taskId ? { ...task, status: 'paused' } : task
      )
    }))
  }, [updateState])

  // Resume a task
  const resumeTask = useCallback((taskId: string) => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.map(task =>
        task.id === taskId ? { ...task, status: 'in_progress' } : task
      )
    }))
  }, [updateState])

  // Remove a task
  const removeTask = useCallback((taskId: string) => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.filter(task => task.id !== taskId),
      taskProgress: Object.fromEntries(
        Object.entries(prev.taskProgress).filter(([id]) => id !== taskId)
      )
    }))
  }, [updateState])

  // Clear all completed tasks
  const clearCompletedTasks = useCallback(() => {
    updateState(prev => ({
      ...prev,
      currentTasks: prev.currentTasks.filter(task => task.status !== 'completed')
    }))
  }, [updateState])

  // Get task by ID
  const getTask = useCallback((taskId: string) => {
    return state.currentTasks.find(task => task.id === taskId)
  }, [state.currentTasks])

  // Get tasks by status
  const getTasksByStatus = useCallback((status: WorkflowTaskState['status']) => {
    return state.currentTasks.filter(task => task.status === status)
  }, [state.currentTasks])

  // Refresh workflow metrics
  const refreshMetrics = useCallback(async () => {
    try {
      const metrics = await workflowStateManager.getWorkflowMetrics(config.workflowType, 'day')
      if (metrics) {
        updateState(prev => ({
          ...prev,
          metrics: {
            completedToday: metrics.totalTransitions,
            averageTime: metrics.averageTime,
            successRate: prev.metrics.successRate // Keep existing success rate
          }
        }))
      }
    } catch (err) {
      console.error('Failed to refresh workflow metrics:', err)
    }
  }, [config.workflowType, updateState])

  // Clear persisted state
  const clearState = useCallback(() => {
    if (config.enablePersistence) {
      workflowStateManager.clearPersistedState({
        key: persistenceKey,
        storage: 'localStorage'
      })
    }
    
    setState({
      currentTasks: [],
      assignedTasks: [],
      taskProgress: {},
      metrics: {
        completedToday: 0,
        averageTime: 0,
        successRate: 100
      },
      lastSync: new Date().toISOString(),
      isOnline: navigator.onLine
    })
  }, [config.enablePersistence, persistenceKey])

  return {
    // State
    state,
    isLoading,
    error,

    // Task management
    startTask,
    updateTaskProgress,
    completeTask,
    failTask,
    pauseTask,
    resumeTask,
    removeTask,
    clearCompletedTasks,

    // Queries
    getTask,
    getTasksByStatus,
    currentTasks: state.currentTasks,
    inProgressTasks: getTasksByStatus('in_progress'),
    completedTasks: getTasksByStatus('completed'),
    failedTasks: getTasksByStatus('failed'),

    // Utilities
    refreshMetrics,
    clearState,
    
    // Status
    isOnline: state.isOnline,
    lastSync: state.lastSync,
    metrics: state.metrics
  }
}