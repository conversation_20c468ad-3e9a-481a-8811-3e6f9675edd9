import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export interface User {
  id: string
  email: string
  role_id: string
  role_name: string
  role_description: string
  first_name: string | null
  last_name: string | null
  whatsapp_number: string | null
  is_active: boolean
  last_login: string | null
  created_at: string
  updated_at: string
  permissions: Record<string, boolean>
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: Record<string, boolean>
  created_at: string
  user_count?: number
}

export interface UserFormData {
  email: string
  password?: string
  role_id: string
  first_name: string
  last_name: string
  whatsapp_number: string
  is_active: boolean
}

export interface RoleFormData {
  name: string
  description: string
  permissions: Record<string, boolean>
}

export function useUserManagement() {
  const { user: currentUser, hasPermission } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load users and roles data
  const loadData = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Load roles
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select('*')
        .order('name')

      if (rolesError) throw rolesError

      // Load users with role information
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          roles!inner (
            id,
            name,
            description,
            permissions
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError) throw usersError

      // Get user counts for roles
      const { data: userCounts, error: countError } = await supabase
        .from('users')
        .select('role_id')

      if (countError) throw countError

      const roleCounts = userCounts.reduce((acc, user) => {
        acc[user.role_id] = (acc[user.role_id] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const formattedRoles: Role[] = (rolesData || []).map(role => ({
        ...role,
        user_count: roleCounts[role.id] || 0
      }))

      const formattedUsers: User[] = (usersData || []).map(user => ({
        id: user.id,
        email: user.email,
        role_id: user.role_id,
        role_name: user.roles.name,
        role_description: user.roles.description,
        first_name: user.first_name,
        last_name: user.last_name,
        whatsapp_number: user.whatsapp_number,
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at,
        updated_at: user.updated_at,
        permissions: user.roles.permissions
      }))

      setRoles(formattedRoles)
      setUsers(formattedUsers)
    } catch (err: any) {
      console.error('Failed to load user data:', err)
      setError(err.message || 'Failed to load user data')
      toast.error('Failed to load user data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create new user
  const createUser = useCallback(async (userFormData: UserFormData): Promise<boolean> => {
    if (!userFormData.email || !userFormData.password || !userFormData.role_id) {
      toast.error('Please fill in all required fields')
      return false
    }

    if (!hasPermission('MANAGE_USERS')) {
      toast.error('You do not have permission to create users')
      return false
    }

    try {
      // Create user via Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userFormData.email,
        password: userFormData.password,
        options: {
          data: {
            first_name: userFormData.first_name,
            last_name: userFormData.last_name
          }
        }
      })

      if (authError) throw authError

      if (authData.user) {
        // Insert user details into users table
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: userFormData.email,
            role_id: userFormData.role_id,
            first_name: userFormData.first_name,
            last_name: userFormData.last_name,
            whatsapp_number: userFormData.whatsapp_number,
            is_active: userFormData.is_active
          })

        if (userError) throw userError

        toast.success('User created successfully')
        await loadData()
        return true
      }
      return false
    } catch (err: any) {
      console.error('Failed to create user:', err)
      toast.error(err.message || 'Failed to create user')
      return false
    }
  }, [hasPermission, loadData])

  // Update user
  const updateUser = useCallback(async (userId: string, userFormData: Partial<UserFormData>): Promise<boolean> => {
    if (!userFormData.email || !userFormData.role_id) {
      toast.error('Please fill in all required fields')
      return false
    }

    if (!hasPermission('MANAGE_USERS')) {
      toast.error('You do not have permission to update users')
      return false
    }

    try {
      const { error } = await supabase
        .from('users')
        .update({
          role_id: userFormData.role_id,
          first_name: userFormData.first_name,
          last_name: userFormData.last_name,
          whatsapp_number: userFormData.whatsapp_number,
          is_active: userFormData.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      toast.success('User updated successfully')
      await loadData()
      return true
    } catch (err: any) {
      console.error('Failed to update user:', err)
      toast.error(err.message || 'Failed to update user')
      return false
    }
  }, [hasPermission, loadData])

  // Toggle user active status
  const toggleUserStatus = useCallback(async (userId: string, currentStatus: boolean): Promise<boolean> => {
    if (!hasPermission('MANAGE_USERS')) {
      toast.error('You do not have permission to modify users')
      return false
    }

    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      toast.success(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully`)
      await loadData()
      return true
    } catch (err: any) {
      console.error('Failed to toggle user status:', err)
      toast.error(err.message || 'Failed to update user status')
      return false
    }
  }, [hasPermission, loadData])

  // Create role
  const createRole = useCallback(async (roleFormData: RoleFormData): Promise<boolean> => {
    if (!roleFormData.name || !roleFormData.description) {
      toast.error('Please fill in all required fields')
      return false
    }

    if (!hasPermission('MANAGE_ROLES')) {
      toast.error('You do not have permission to create roles')
      return false
    }

    try {
      const { error } = await supabase
        .from('roles')
        .insert({
          name: roleFormData.name,
          description: roleFormData.description,
          permissions: roleFormData.permissions
        })

      if (error) throw error

      toast.success('Role created successfully')
      await loadData()
      return true
    } catch (err: any) {
      console.error('Failed to create role:', err)
      toast.error(err.message || 'Failed to create role')
      return false
    }
  }, [hasPermission, loadData])

  // Update role
  const updateRole = useCallback(async (roleId: string, roleFormData: RoleFormData): Promise<boolean> => {
    if (!roleFormData.name || !roleFormData.description) {
      toast.error('Please fill in all required fields')
      return false
    }

    if (!hasPermission('MANAGE_ROLES')) {
      toast.error('You do not have permission to update roles')
      return false
    }

    try {
      const { error } = await supabase
        .from('roles')
        .update({
          name: roleFormData.name,
          description: roleFormData.description,
          permissions: roleFormData.permissions
        })
        .eq('id', roleId)

      if (error) throw error

      toast.success('Role updated successfully')
      await loadData()
      return true
    } catch (err: any) {
      console.error('Failed to update role:', err)
      toast.error(err.message || 'Failed to update role')
      return false
    }
  }, [hasPermission, loadData])

  // Get user by ID
  const getUserById = useCallback((userId: string): User | undefined => {
    return users.find(user => user.id === userId)
  }, [users])

  // Get role by ID
  const getRoleById = useCallback((roleId: string): Role | undefined => {
    return roles.find(role => role.id === roleId)
  }, [roles])

  // Filter users
  const filterUsers = useCallback((searchTerm: string, roleFilter: string) => {
    return users.filter(user => {
      const matchesSearch = searchTerm === '' || 
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.role_name.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesRole = roleFilter === 'all' || user.role_id === roleFilter
      
      return matchesSearch && matchesRole
    })
  }, [users])

  // Load data on mount
  useEffect(() => {
    loadData()
  }, [loadData])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!currentUser) return

    const usersChannel = supabase
      .channel('users-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users'
        },
        (payload) => {
          console.log('Users table changed:', payload)
          loadData()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'roles'
        },
        (payload) => {
          console.log('Roles table changed:', payload)
          loadData()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(usersChannel)
    }
  }, [currentUser, loadData])

  return {
    // Data
    users,
    roles,
    isLoading,
    error,

    // Actions
    createUser,
    updateUser,
    toggleUserStatus,
    createRole,
    updateRole,

    // Utilities
    getUserById,
    getRoleById,
    filterUsers,
    refresh: loadData,

    // Permissions
    canManageUsers: hasPermission('MANAGE_USERS'),
    canManageRoles: hasPermission('MANAGE_ROLES')
  }
}