import { useCallback, useEffect, useMemo } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useKittingQueue } from '@/hooks/useKittingQueue'
import { useQCQueue } from '@/hooks/useQCQueue'
import { useInvoicing } from '@/hooks/useInvoicing'
import { useWorkflowState } from '@/hooks/useWorkflowState'
import { workflowStateManager, WorkflowType } from '@/services/WorkflowStateManager'
import { QuantityTransitionService, QuantityTransition } from '@/services/QuantityTransitionService'
import { toast } from 'sonner'

export interface WorkflowTransition {
  orderLineId: string
  fromWorkflow: WorkflowType
  toWorkflow: WorkflowType
  quantity: number
  reason: string
  ctNumbers?: string[]
  metadata?: Record<string, any>
}

export interface WorkflowCoordinationResult {
  success: boolean
  message: string
  updatedWorkflows: WorkflowType[]
  transitionId?: string
}

export function useWorkflowCoordinator() {
  const { user } = useAuth()
  
  // Individual workflow hooks
  const kittingQueue = useKittingQueue()
  const qcQueue = useQCQueue()
  const invoicing = useInvoicing()

  // Enhanced workflow state management
  const kittingState = useWorkflowState({
    workflowType: 'kitting',
    enablePersistence: true,
    enableRealTimeSync: true,
    enableOptimisticUpdates: true,
    syncInterval: 30000 // 30 seconds
  })

  const qcState = useWorkflowState({
    workflowType: 'qc',
    enablePersistence: true,
    enableRealTimeSync: true,
    enableOptimisticUpdates: true,
    syncInterval: 30000
  })

  const invoicingState = useWorkflowState({
    workflowType: 'invoicing',
    enablePersistence: true,
    enableRealTimeSync: true,
    enableOptimisticUpdates: true,
    syncInterval: 30000
  })

  // Subscribe to cross-workflow events
  useEffect(() => {
    if (!user) return

    const unsubscribeStateChanges = workflowStateManager.subscribe('state_changed', (eventData) => {
      console.log('📊 Cross-workflow state change detected:', eventData)
      
      // Refresh all affected workflows
      switch (eventData.workflowType) {
        case 'kitting':
          qcQueue.refresh()
          break
        case 'qc':
          invoicing.refresh()
          kittingQueue.refresh()
          break
        case 'invoicing':
          // No downstream workflows to refresh
          break
      }
    })

    const unsubscribeTaskCompleted = workflowStateManager.subscribe('task_completed', (eventData) => {
      console.log('✅ Cross-workflow task completion detected:', eventData)
      
      // Coordinate handoffs between workflows
      if (eventData.workflowType === 'kitting') {
        // Kitting completed, refresh QC queue
        qcQueue.refresh()
        toast.success('Items ready for QC inspection')
      } else if (eventData.workflowType === 'qc') {
        // QC completed, refresh invoicing
        invoicing.refresh()
        toast.success('Items ready for invoicing')
      }
    })

    return () => {
      unsubscribeStateChanges()
      unsubscribeTaskCompleted()
    }
  }, [user, kittingQueue, qcQueue, invoicing])

  // Execute coordinated workflow transition
  const executeCoordinatedTransition = useCallback(async (
    transition: WorkflowTransition
  ): Promise<WorkflowCoordinationResult> => {
    if (!user) {
      return { 
        success: false, 
        message: 'User not authenticated', 
        updatedWorkflows: [] 
      }
    }

    try {
      console.log('🔄 Executing coordinated workflow transition:', transition)

      // Start optimistic updates
      const taskId = kittingState.startTask(transition.orderLineId, {
        transition,
        coordinatedTransition: true
      })

      // Determine the quantity transition based on workflow handoff
      const quantityTransition = await buildQuantityTransition(transition, user.id)
      
      if (!quantityTransition) {
        kittingState.failTask(taskId, 'Failed to build quantity transition')
        return {
          success: false,
          message: 'Invalid workflow transition',
          updatedWorkflows: []
        }
      }

      // Execute the quantity transition
      const result = await QuantityTransitionService.executeTransition(quantityTransition, user.id)
      
      if (!result.success) {
        kittingState.failTask(taskId, result.message)
        return {
          success: false,
          message: result.message,
          updatedWorkflows: []
        }
      }

      // Complete the coordination task
      kittingState.completeTask(taskId, result)

      // Coordinate state updates across workflows
      const updatedWorkflows = await coordinateWorkflowUpdates(transition)

      // Emit coordination event
      workflowStateManager.emit('task_completed', {
        workflowType: transition.fromWorkflow,
        userId: user.id,
        orderLineId: transition.orderLineId,
        payload: {
          transition,
          result,
          coordinatedBy: 'useWorkflowCoordinator'
        }
      })

      return {
        success: true,
        message: `Successfully transitioned from ${transition.fromWorkflow} to ${transition.toWorkflow}`,
        updatedWorkflows,
        transitionId: result.logId
      }

    } catch (error: any) {
      console.error('Failed to execute coordinated workflow transition:', error)
      return {
        success: false,
        message: error.message || 'Coordination failed',
        updatedWorkflows: []
      }
    }
  }, [user, kittingState])

  // Build quantity transition from workflow transition
  const buildQuantityTransition = async (
    transition: WorkflowTransition,
    userId: string
  ): Promise<QuantityTransition | null> => {
    const baseTransition: Partial<QuantityTransition> = {
      orderLineId: transition.orderLineId,
      quantity: transition.quantity,
      reason: transition.reason,
      ctNumbers: transition.ctNumbers,
      metadata: {
        ...transition.metadata,
        coordinatedTransition: true,
        fromWorkflow: transition.fromWorkflow,
        toWorkflow: transition.toWorkflow
      }
    }

    // Map workflow transitions to quantity state transitions
    if (transition.fromWorkflow === 'kitting' && transition.toWorkflow === 'qc') {
      return {
        ...baseTransition,
        fromState: 'in_kitting_packing',
        toState: 'kitted_packed_awaiting_screening_qc'
      } as QuantityTransition
    }

    if (transition.fromWorkflow === 'qc' && transition.toWorkflow === 'invoicing') {
      return {
        ...baseTransition,
        fromState: 'in_screening_qc',
        toState: 'screening_qc_passed_ready_for_invoice'
      } as QuantityTransition
    }

    // Add more workflow transition mappings as needed
    console.error('Unsupported workflow transition:', transition)
    return null
  }

  // Coordinate updates across multiple workflows
  const coordinateWorkflowUpdates = async (transition: WorkflowTransition): Promise<WorkflowType[]> => {
    const updatedWorkflows: WorkflowType[] = []

    try {
      // Refresh source workflow
      switch (transition.fromWorkflow) {
        case 'kitting':
          await kittingQueue.refresh()
          updatedWorkflows.push('kitting')
          break
        case 'qc':
          await qcQueue.refresh()
          updatedWorkflows.push('qc')
          break
        case 'invoicing':
          await invoicing.refresh()
          updatedWorkflows.push('invoicing')
          break
      }

      // Refresh target workflow
      switch (transition.toWorkflow) {
        case 'kitting':
          await kittingQueue.refresh()
          if (!updatedWorkflows.includes('kitting')) updatedWorkflows.push('kitting')
          break
        case 'qc':
          await qcQueue.refresh()
          if (!updatedWorkflows.includes('qc')) updatedWorkflows.push('qc')
          break
        case 'invoicing':
          await invoicing.refresh()
          if (!updatedWorkflows.includes('invoicing')) updatedWorkflows.push('invoicing')
          break
      }

      console.log('🔄 Coordinated workflow updates:', updatedWorkflows)
    } catch (error) {
      console.error('Failed to coordinate workflow updates:', error)
    }

    return updatedWorkflows
  }

  // Enhanced kitting to QC handoff
  const handoffKittingToQC = useCallback(async (
    orderLineId: string, 
    quantity: number, 
    ctNumbers?: string[]
  ) => {
    const transition: WorkflowTransition = {
      orderLineId,
      fromWorkflow: 'kitting',
      toWorkflow: 'qc',
      quantity,
      reason: `Kitting completed by ${user?.email?.split('@')[0] || 'Unknown'}`,
      ctNumbers,
      metadata: {
        handoffType: 'kitting_to_qc',
        completedBy: user?.email
      }
    }

    return executeCoordinatedTransition(transition)
  }, [user, executeCoordinatedTransition])

  // Enhanced QC to invoicing handoff
  const handoffQCToInvoicing = useCallback(async (
    orderLineId: string,
    quantity: number,
    qcNotes?: string,
    ctNumbers?: string[]
  ) => {
    const transition: WorkflowTransition = {
      orderLineId,
      fromWorkflow: 'qc',
      toWorkflow: 'invoicing',
      quantity,
      reason: `QC passed by ${user?.email?.split('@')[0] || 'Unknown'}${qcNotes ? `: ${qcNotes}` : ''}`,
      ctNumbers,
      metadata: {
        handoffType: 'qc_to_invoicing',
        qcNotes,
        passedBy: user?.email
      }
    }

    return executeCoordinatedTransition(transition)
  }, [user, executeCoordinatedTransition])

  // Get overall workflow health
  const getWorkflowHealth = useMemo(() => {
    const totalTasks = kittingState.currentTasks.length + qcState.currentTasks.length + invoicingState.currentTasks.length
    const failedTasks = kittingState.failedTasks.length + qcState.failedTasks.length + invoicingState.failedTasks.length
    const inProgressTasks = kittingState.inProgressTasks.length + qcState.inProgressTasks.length + invoicingState.inProgressTasks.length

    return {
      totalTasks,
      failedTasks,
      inProgressTasks,
      successRate: totalTasks > 0 ? ((totalTasks - failedTasks) / totalTasks) * 100 : 100,
      isHealthy: failedTasks === 0 && inProgressTasks < 10, // Healthy if no failures and manageable workload
      bottleneck: inProgressTasks > 15 ? 'high_load' : failedTasks > 0 ? 'failures' : null
    }
  }, [kittingState, qcState, invoicingState])

  // Refresh all workflows
  const refreshAllWorkflows = useCallback(async () => {
    try {
      await Promise.all([
        kittingQueue.refresh(),
        qcQueue.refresh(),
        invoicing.refresh()
      ])

      await Promise.all([
        kittingState.refreshMetrics(),
        qcState.refreshMetrics(),
        invoicingState.refreshMetrics()
      ])

      toast.success('All workflows refreshed successfully')
    } catch (error) {
      console.error('Failed to refresh all workflows:', error)
      toast.error('Failed to refresh workflows')
    }
  }, [kittingQueue, qcQueue, invoicing, kittingState, qcState, invoicingState])

  // Clear all workflow states
  const clearAllWorkflowStates = useCallback(() => {
    kittingState.clearState()
    qcState.clearState()
    invoicingState.clearState()
    toast.success('All workflow states cleared')
  }, [kittingState, qcState, invoicingState])

  return {
    // Original workflow hooks (enhanced with coordination)
    kittingQueue,
    qcQueue,
    invoicing,

    // Enhanced workflow state management
    kittingState,
    qcState,
    invoicingState,

    // Coordination functions
    executeCoordinatedTransition,
    handoffKittingToQC,
    handoffQCToInvoicing,

    // Health and monitoring
    workflowHealth: getWorkflowHealth,
    
    // Utilities
    refreshAllWorkflows,
    clearAllWorkflowStates,

    // Status
    isOnline: kittingState.isOnline && qcState.isOnline && invoicingState.isOnline,
    isLoading: kittingState.isLoading || qcState.isLoading || invoicingState.isLoading,
    lastSync: Math.max(
      new Date(kittingState.lastSync).getTime(),
      new Date(qcState.lastSync).getTime(),
      new Date(invoicingState.lastSync).getTime()
    )
  }
}