import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

// Workflow state types
export type WorkflowType = 'kitting' | 'qc' | 'invoicing'
export type WorkflowEvent = 'task_started' | 'task_completed' | 'task_failed' | 'task_assigned' | 'state_changed'

interface WorkflowState {
  workflowType: WorkflowType
  userId: string
  currentTasks: any[]
  assignedTasks: string[]
  taskProgress: Record<string, number>
  lastUpdated: string
  sessionId: string
}

interface WorkflowEventData {
  id: string
  type: WorkflowEvent
  workflowType: WorkflowType
  userId: string
  orderLineId?: string
  taskId?: string
  payload: any
  timestamp: string
}

interface PersistenceConfig {
  key: string
  storage: 'localStorage' | 'sessionStorage' | 'indexedDB'
  expirationTime?: number
  syncAcrossComponents?: boolean
}

export class WorkflowStateManager {
  private static instance: WorkflowStateManager
  private eventBus: Map<string, Function[]> = new Map()
  private persistedStates: Map<string, any> = new Map()
  private sessionId: string

  constructor() {
    this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.initializeEventBus()
    this.setupRealTimeSubscriptions()
  }

  static getInstance(): WorkflowStateManager {
    if (!WorkflowStateManager.instance) {
      WorkflowStateManager.instance = new WorkflowStateManager()
    }
    return WorkflowStateManager.instance
  }

  /**
   * Initialize the event bus for cross-workflow communication
   */
  private initializeEventBus() {
    console.log('🚀 Initializing Workflow State Manager', { sessionId: this.sessionId })
  }

  /**
   * Set up real-time subscriptions for workflow coordination
   */
  private setupRealTimeSubscriptions() {
    const channel = supabase
      .channel('workflow-coordination')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_line_quantities'
        },
        (payload) => {
          this.handleQuantityChange(payload)
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quantity_logs'
        },
        (payload) => {
          this.handleQuantityLogEvent(payload)
        }
      )
      .subscribe()

    console.log('📡 Real-time workflow coordination subscriptions established')
  }

  /**
   * Handle quantity state changes and coordinate workflows
   */
  private handleQuantityChange(payload: any) {
    const { new: newRecord, old: oldRecord, eventType } = payload

    if (eventType === 'UPDATE' && newRecord && oldRecord) {
      // Identify which quantities changed
      const changes = this.detectQuantityChanges(oldRecord, newRecord)
      
      // Broadcast events to relevant workflows
      changes.forEach(change => {
        this.emit('state_changed', {
          workflowType: this.getWorkflowFromQuantityState(change.state),
          orderLineId: newRecord.order_line_id,
          payload: change
        })
      })
    }
  }

  /**
   * Handle quantity log events for audit and coordination
   */
  private handleQuantityLogEvent(payload: any) {
    const { new: logEntry } = payload

    if (logEntry) {
      // Determine workflow type from transition
      const workflowType = this.getWorkflowFromTransition(logEntry.from_state, logEntry.to_state)
      
      // Emit workflow event
      this.emit('task_completed', {
        workflowType,
        orderLineId: logEntry.order_line_id,
        userId: logEntry.user_id,
        payload: logEntry
      })
    }
  }

  /**
   * Detect changes between old and new quantity records
   */
  private detectQuantityChanges(oldRecord: any, newRecord: any) {
    const changes = []
    const quantityFields = [
      'pending_procurement',
      'awaiting_kitting_packing', 
      'in_kitting_packing',
      'kitted_awaiting_qc',
      'in_screening_qc',
      'qc_passed_ready_invoice',
      'invoiced'
    ]

    quantityFields.forEach(field => {
      const oldValue = oldRecord[field] || 0
      const newValue = newRecord[field] || 0
      
      if (oldValue !== newValue) {
        changes.push({
          state: field,
          oldValue,
          newValue,
          delta: newValue - oldValue
        })
      }
    })

    return changes
  }

  /**
   * Get workflow type from quantity state
   */
  private getWorkflowFromQuantityState(state: string): WorkflowType {
    if (state.includes('kitting')) return 'kitting'
    if (state.includes('qc') || state.includes('screening')) return 'qc'
    if (state.includes('invoice')) return 'invoicing'
    return 'kitting' // default
  }

  /**
   * Get workflow type from state transition
   */
  private getWorkflowFromTransition(fromState: string, toState: string): WorkflowType {
    if (fromState.includes('kitting') || toState.includes('kitting')) return 'kitting'
    if (fromState.includes('qc') || toState.includes('qc') || fromState.includes('screening') || toState.includes('screening')) return 'qc'
    if (fromState.includes('invoice') || toState.includes('invoice')) return 'invoicing'
    return 'kitting' // default
  }

  /**
   * Emit workflow event to all subscribers
   */
  emit(eventType: WorkflowEvent, data: Partial<WorkflowEventData>) {
    const eventData: WorkflowEventData = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: eventType,
      workflowType: data.workflowType || 'kitting',
      userId: data.userId || '',
      orderLineId: data.orderLineId,
      taskId: data.taskId,
      payload: data.payload || {},
      timestamp: new Date().toISOString()
    }

    console.log(`📨 Workflow event emitted:`, eventData)

    // Notify all subscribers
    const subscribers = this.eventBus.get(eventType) || []
    subscribers.forEach(callback => {
      try {
        callback(eventData)
      } catch (error) {
        console.error(`Error in workflow event subscriber for ${eventType}:`, error)
      }
    })

    // Also notify wildcard subscribers
    const wildcardSubscribers = this.eventBus.get('*') || []
    wildcardSubscribers.forEach(callback => {
      try {
        callback(eventData)
      } catch (error) {
        console.error(`Error in wildcard workflow event subscriber:`, error)
      }
    })
  }

  /**
   * Subscribe to workflow events
   */
  subscribe(eventType: WorkflowEvent | '*', callback: (data: WorkflowEventData) => void): () => void {
    if (!this.eventBus.has(eventType)) {
      this.eventBus.set(eventType, [])
    }
    
    this.eventBus.get(eventType)!.push(callback)
    
    console.log(`🔔 Subscribed to workflow event: ${eventType}`)

    // Return unsubscribe function
    return () => {
      const subscribers = this.eventBus.get(eventType) || []
      const index = subscribers.indexOf(callback)
      if (index > -1) {
        subscribers.splice(index, 1)
      }
    }
  }

  /**
   * Persist workflow state to local storage
   */
  persistState(config: PersistenceConfig, state: any): void {
    try {
      const stateWithMeta = {
        ...state,
        sessionId: this.sessionId,
        lastUpdated: new Date().toISOString(),
        expiresAt: config.expirationTime 
          ? new Date(Date.now() + config.expirationTime).toISOString()
          : null
      }

      const serializedState = JSON.stringify(stateWithMeta)

      switch (config.storage) {
        case 'localStorage':
          localStorage.setItem(config.key, serializedState)
          break
        case 'sessionStorage':
          sessionStorage.setItem(config.key, serializedState)
          break
        case 'indexedDB':
          this.persistToIndexedDB(config.key, stateWithMeta)
          break
      }

      this.persistedStates.set(config.key, stateWithMeta)
      console.log(`💾 Persisted workflow state: ${config.key}`)

    } catch (error) {
      console.error(`Failed to persist workflow state for ${config.key}:`, error)
      toast.error('Failed to save workflow state')
    }
  }

  /**
   * Retrieve persisted workflow state
   */
  getPersistedState<T>(config: PersistenceConfig): T | null {
    try {
      let serializedState: string | null = null

      switch (config.storage) {
        case 'localStorage':
          serializedState = localStorage.getItem(config.key)
          break
        case 'sessionStorage':
          serializedState = sessionStorage.getItem(config.key)
          break
        case 'indexedDB':
          return this.getFromIndexedDB<T>(config.key)
      }

      if (!serializedState) return null

      const state = JSON.parse(serializedState)

      // Check expiration
      if (state.expiresAt && new Date(state.expiresAt) < new Date()) {
        this.clearPersistedState(config)
        return null
      }

      console.log(`📥 Retrieved persisted workflow state: ${config.key}`)
      return state as T

    } catch (error) {
      console.error(`Failed to retrieve persisted workflow state for ${config.key}:`, error)
      return null
    }
  }

  /**
   * Clear persisted workflow state
   */
  clearPersistedState(config: PersistenceConfig): void {
    try {
      switch (config.storage) {
        case 'localStorage':
          localStorage.removeItem(config.key)
          break
        case 'sessionStorage':
          sessionStorage.removeItem(config.key)
          break
        case 'indexedDB':
          this.removeFromIndexedDB(config.key)
          break
      }

      this.persistedStates.delete(config.key)
      console.log(`🗑️ Cleared persisted workflow state: ${config.key}`)

    } catch (error) {
      console.error(`Failed to clear persisted workflow state for ${config.key}:`, error)
    }
  }

  /**
   * Coordinate workflow transition across multiple workflows
   */
  async coordinateWorkflowTransition(transition: {
    orderLineId: string
    fromWorkflow: WorkflowType
    toWorkflow: WorkflowType
    userId: string
    payload: any
  }): Promise<boolean> {
    try {
      console.log(`🔄 Coordinating workflow transition:`, transition)

      // Emit transition started event
      this.emit('task_started', {
        workflowType: transition.toWorkflow,
        userId: transition.userId,
        orderLineId: transition.orderLineId,
        payload: transition.payload
      })

      // Wait for transition to complete (listen for quantity changes)
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          unsubscribe()
          resolve(false)
        }, 30000) // 30 second timeout

        const unsubscribe = this.subscribe('state_changed', (eventData) => {
          if (eventData.orderLineId === transition.orderLineId) {
            clearTimeout(timeout)
            unsubscribe()
            resolve(true)
          }
        })
      })

    } catch (error) {
      console.error('Failed to coordinate workflow transition:', error)
      return false
    }
  }

  /**
   * Get workflow statistics and metrics
   */
  async getWorkflowMetrics(workflowType: WorkflowType, timeRange: 'hour' | 'day' | 'week' = 'day') {
    try {
      const startTime = new Date()
      
      switch (timeRange) {
        case 'hour':
          startTime.setHours(startTime.getHours() - 1)
          break
        case 'day':
          startTime.setDate(startTime.getDate() - 1)
          break
        case 'week':
          startTime.setDate(startTime.getDate() - 7)
          break
      }

      const { data: logs, error } = await supabase
        .from('quantity_logs')
        .select('*')
        .gte('timestamp', startTime.toISOString())
        .or(`from_state.ilike.%${workflowType}%,to_state.ilike.%${workflowType}%`)

      if (error) throw error

      // Calculate metrics
      const totalTransitions = logs?.length || 0
      const uniqueOrders = new Set(logs?.map(log => log.order_line_id)).size
      const averageTime = this.calculateAverageTransitionTime(logs || [])

      return {
        totalTransitions,
        uniqueOrders,
        averageTime,
        timeRange,
        workflowType
      }
    } catch (error) {
      console.error(`Failed to get workflow metrics for ${workflowType}:`, error)
      return null
    }
  }

  /**
   * Calculate average transition time
   */
  private calculateAverageTransitionTime(logs: any[]): number {
    if (logs.length === 0) return 0

    const transitions = logs.reduce((acc, log) => {
      const orderTransitions = acc[log.order_line_id] || []
      orderTransitions.push(new Date(log.timestamp).getTime())
      acc[log.order_line_id] = orderTransitions.sort()
      return acc
    }, {} as Record<string, number[]>)

    const durations = Object.values(transitions)
      .filter(times => times.length > 1)
      .map(times => times[times.length - 1] - times[0])

    return durations.length > 0 
      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length / 1000 / 60 // minutes
      : 0
  }

  /**
   * IndexedDB operations (for offline support)
   */
  private async persistToIndexedDB(key: string, state: any): Promise<void> {
    // Implementation for IndexedDB storage
    // TODO: Implement full IndexedDB integration for offline support
    console.log(`IndexedDB persistence not yet implemented for ${key}`)
  }

  private async getFromIndexedDB<T>(key: string): Promise<T | null> {
    // Implementation for IndexedDB retrieval
    // TODO: Implement full IndexedDB integration for offline support
    console.log(`IndexedDB retrieval not yet implemented for ${key}`)
    return null
  }

  private async removeFromIndexedDB(key: string): Promise<void> {
    // Implementation for IndexedDB removal
    // TODO: Implement full IndexedDB integration for offline support
    console.log(`IndexedDB removal not yet implemented for ${key}`)
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.eventBus.clear()
    this.persistedStates.clear()
    console.log('🧹 Workflow State Manager destroyed')
  }
}

// Export singleton instance
export const workflowStateManager = WorkflowStateManager.getInstance()