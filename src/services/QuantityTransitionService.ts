import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

// Event emitter for quantity transitions
class TransitionEventEmitter extends EventTarget {
  emitTransition(orderLineId: string, transition: any) {
    this.dispatchEvent(new CustomEvent('quantity-transition', {
      detail: { orderLineId, transition }
    }))
  }
}

export const transitionEvents = new TransitionEventEmitter()

// Quantity State Types
export type QuantityState = 
  | 'total_order_quantity'
  | 'pending_procurement_arrangement'
  | 'requested_from_stock'
  | 'awaiting_kitting_packing'
  | 'in_kitting_packing'
  | 'on_hold_at_kitting_packing'
  | 'kitted_packed_awaiting_screening_qc'
  | 'in_screening_qc'
  | 'on_hold_at_screening_qc'
  | 'screening_qc_passed_ready_for_invoice'
  | 'screening_qc_rejected'
  | 'invoiced'
  | 'shipped_delivered'
  | 'cancelled'

export interface QuantityTransition {
  orderLineId: string
  fromState: QuantityState
  toState: QuantityState
  quantity: number
  reason?: string
  rejectionReason?: string
  ctNumbers?: string[]
  metadata?: Record<string, any>
}

export interface TransitionResult {
  success: boolean
  message: string
  logId?: string
}

export class QuantityTransitionService {
  /**
   * Execute a quantity state transition
   */
  static async executeTransition(
    transition: QuantityTransition, 
    userId: string
  ): Promise<TransitionResult> {
    try {
      console.log('🔄 Executing quantity transition:', {
        orderLineId: transition.orderLineId,
        from: transition.fromState,
        to: transition.toState,
        quantity: transition.quantity,
        ctNumbers: transition.ctNumbers
      })

      // Validate transition
      if (!transition.orderLineId || !transition.fromState || !transition.toState) {
        return { success: false, message: 'Invalid transition parameters' }
      }

      if (transition.quantity <= 0) {
        return { success: false, message: 'Quantity must be greater than 0' }
      }

      // Execute transition via Supabase RPC (if it exists)
      const { data, error } = await supabase.rpc('transition_quantity', {
        p_order_line_id: transition.orderLineId,
        p_from_state: transition.fromState,
        p_to_state: transition.toState,
        p_quantity: transition.quantity,
        p_reason: transition.reason,
        p_rejection_reason: transition.rejectionReason,
        p_ct_numbers: transition.ctNumbers,
        p_metadata: transition.metadata,
        p_user_id: userId
      })

      if (error) {
        console.error('❌ Quantity transition RPC error:', error)
        
        // If RPC doesn't exist yet, handle manually
        if (error.message?.includes('function transition_quantity')) {
          console.log('⚠️ RPC not found, executing manually')
          return await this.executeTransitionManually(transition, userId)
        }
        
        throw error
      }

      console.log('✅ Quantity transition RPC successful:', {
        response: data,
        orderLineId: transition.orderLineId,
        transition: `${transition.fromState} → ${transition.toState}`,
        quantity: transition.quantity
      })
      
      // Emit transition event for immediate UI updates
      transitionEvents.emitTransition(transition.orderLineId, {
        fromState: transition.fromState,
        toState: transition.toState,
        quantity: transition.quantity,
        ctNumbers: transition.ctNumbers,
        timestamp: new Date().toISOString()
      })
      
      // Also broadcast to the kitting channel if this affects kitting
      if (transition.fromState.includes('kitting') || transition.toState.includes('kitting')) {
        const broadcastChannel = supabase.channel('kitting-broadcast')
        broadcastChannel.send({
          type: 'broadcast',
          event: 'kitting-update',
          payload: {
            orderLineId: transition.orderLineId,
            transition: {
              fromState: transition.fromState,
              toState: transition.toState,
              quantity: transition.quantity,
              ctNumbers: transition.ctNumbers
            },
            timestamp: new Date().toISOString()
          }
        })
      }
      
      // Force update the timestamp on the order line to trigger real-time subscriptions
      await supabase
        .from('order_lines')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', transition.orderLineId)
      
      // Also update CT number state if CT numbers are provided
      if (transition.ctNumbers && transition.ctNumbers.length > 0) {
        console.log('🏷️ Updating CT number states...', transition.ctNumbers)
        for (const ctNumber of transition.ctNumbers) {
          const { error: ctError } = await supabase
            .from('ct_numbers')
            .update({ 
              current_state: transition.toState,
              updated_at: new Date().toISOString()
            })
            .eq('ct_number', ctNumber)
            
          if (ctError) {
            console.error('❌ Failed to update CT state:', ctError)
          } else {
            console.log('✅ Updated CT state:', ctNumber, '→', transition.toState)
          }
        }
      }

      toast.success(`Successfully moved ${transition.quantity} items to ${transition.toState.replace(/_/g, ' ')}`)
      
      return { 
        success: true, 
        message: 'Quantity transition successful',
        logId: data?.log_id
      }
    } catch (err) {
      console.error('❌ Quantity transition failed:', err)
      const message = 'Quantity transition failed'
      toast.error(message)
      return { success: false, message }
    }
  }

  /**
   * Manual transition execution when RPC doesn't exist
   */
  private static async executeTransitionManually(
    transition: QuantityTransition,
    userId: string
  ): Promise<TransitionResult> {
    try {
      // Get current quantities
      const { data: currentQuantities, error: fetchError } = await supabase
        .from('order_line_quantities')
        .select('*')
        .eq('order_line_id', transition.orderLineId)
        .single()

      if (fetchError) {
        throw fetchError
      }

      if (!currentQuantities) {
        return { success: false, message: 'Order quantities not found' }
      }

      // Calculate new quantities
      const fromField = this.getQuantityFieldName(transition.fromState)
      const toField = this.getQuantityFieldName(transition.toState)

      if (!fromField || !toField) {
        return { success: false, message: 'Invalid quantity state' }
      }

      const currentFromQty = currentQuantities[fromField] || 0
      if (currentFromQty < transition.quantity) {
        return { 
          success: false, 
          message: `Insufficient quantity in ${transition.fromState.replace(/_/g, ' ')}. Available: ${currentFromQty}, Requested: ${transition.quantity}` 
        }
      }

      // Update quantities
      const updates = {
        [fromField]: currentFromQty - transition.quantity,
        [toField]: (currentQuantities[toField] || 0) + transition.quantity,
        updated_at: new Date().toISOString(),
        last_updated_by: userId
      }

      const { error: updateError } = await supabase
        .from('order_line_quantities')
        .update(updates)
        .eq('order_line_id', transition.orderLineId)

      if (updateError) {
        throw updateError
      }

      // Create quantity log entry
      const logEntry = {
        order_line_id: transition.orderLineId,
        from_state: transition.fromState,
        to_state: transition.toState,
        quantity_moved: transition.quantity,
        reason_text: transition.reason,
        rejection_reason: transition.rejectionReason,
        associated_ct_numbers: transition.ctNumbers || [],
        location: 'SB', // Default to SB location
        metadata: transition.metadata,
        user_id: userId,
        timestamp: new Date().toISOString()
      }

      const { error: logError } = await supabase
        .from('quantity_logs')
        .insert(logEntry)

      if (logError) {
        console.error('Failed to create quantity log:', logError)
        // Continue anyway - quantity update succeeded
      }

      // Also update CT number state if CT numbers are provided
      if (transition.ctNumbers && transition.ctNumbers.length > 0) {
        console.log('🏷️ Updating CT number states (manual)...')
        for (const ctNumber of transition.ctNumbers) {
          const { error: ctError } = await supabase
            .from('ct_numbers')
            .update({ 
              current_state: transition.toState,
              updated_at: new Date().toISOString()
            })
            .eq('ct_number', ctNumber)
            
          if (ctError) {
            console.error('❌ Failed to update CT state:', ctError)
          }
        }
      }

      console.log('✅ Manual quantity transition successful')
      toast.success(`Successfully moved ${transition.quantity} items to ${transition.toState.replace(/_/g, ' ')}`)
      
      return { 
        success: true, 
        message: 'Quantity transition successful'
      }
    } catch (err) {
      console.error('❌ Manual quantity transition failed:', err)
      return { success: false, message: 'Manual quantity transition failed' }
    }
  }

  /**
   * Map quantity state to database field name
   */
  private static getQuantityFieldName(state: QuantityState): string | null {
    const mapping: Record<QuantityState, string> = {
      'total_order_quantity': 'total_order_quantity',
      'pending_procurement_arrangement': 'pending_procurement',
      'requested_from_stock': 'requested_from_stock',
      'awaiting_kitting_packing': 'awaiting_kitting_packing',
      'in_kitting_packing': 'in_kitting_packing',
      'on_hold_at_kitting_packing': 'on_hold_kitting',
      'kitted_packed_awaiting_screening_qc': 'kitted_awaiting_qc',
      'in_screening_qc': 'in_screening_qc',
      'on_hold_at_screening_qc': 'on_hold_qc',
      'screening_qc_passed_ready_for_invoice': 'qc_passed_ready_invoice',
      'screening_qc_rejected': 'qc_rejected',
      'invoiced': 'invoiced',
      'shipped_delivered': 'shipped_delivered',
      'cancelled': 'cancelled'
    }

    return mapping[state] || null
  }

  /**
   * Validate if a transition is allowed
   */
  static validateTransition(fromState: QuantityState, toState: QuantityState): boolean {
    // Define allowed transitions
    const allowedTransitions: Record<QuantityState, QuantityState[]> = {
      'total_order_quantity': [],
      'pending_procurement_arrangement': ['requested_from_stock', 'awaiting_kitting_packing', 'cancelled'],
      'requested_from_stock': ['awaiting_kitting_packing', 'pending_procurement_arrangement'],
      'awaiting_kitting_packing': ['in_kitting_packing', 'cancelled'],
      'in_kitting_packing': ['kitted_packed_awaiting_screening_qc', 'on_hold_at_kitting_packing', 'pending_procurement_arrangement'],
      'on_hold_at_kitting_packing': ['in_kitting_packing', 'pending_procurement_arrangement'],
      'kitted_packed_awaiting_screening_qc': ['in_screening_qc', 'cancelled'],
      'in_screening_qc': ['screening_qc_passed_ready_for_invoice', 'screening_qc_rejected', 'on_hold_at_screening_qc'],
      'on_hold_at_screening_qc': ['in_screening_qc', 'screening_qc_rejected'],
      'screening_qc_passed_ready_for_invoice': ['invoiced', 'cancelled'],
      'screening_qc_rejected': ['pending_procurement_arrangement'],
      'invoiced': ['shipped_delivered'],
      'shipped_delivered': [],
      'cancelled': []
    }

    return allowedTransitions[fromState]?.includes(toState) || false
  }

  /**
   * Get display name for quantity state
   */
  static getStateDisplayName(state: QuantityState): string {
    const displayNames: Record<QuantityState, string> = {
      'total_order_quantity': 'Total Order Quantity',
      'pending_procurement_arrangement': 'Pending Procurement',
      'requested_from_stock': 'Requested from Stock',
      'awaiting_kitting_packing': 'Awaiting Kitting/Packing',
      'in_kitting_packing': 'In Kitting/Packing',
      'on_hold_at_kitting_packing': 'On Hold at Kitting',
      'kitted_packed_awaiting_screening_qc': 'Awaiting QC',
      'in_screening_qc': 'In Screening/QC',
      'on_hold_at_screening_qc': 'On Hold at QC',
      'screening_qc_passed_ready_for_invoice': 'Ready for Invoice',
      'screening_qc_rejected': 'QC Rejected',
      'invoiced': 'Invoiced',
      'shipped_delivered': 'Shipped/Delivered',
      'cancelled': 'Cancelled'
    }

    return displayNames[state] || state.replace(/_/g, ' ')
  }

  /**
   * Get workflow stage category
   */
  static getStageCategory(state: QuantityState): 'procurement' | 'kitting' | 'qc' | 'final' {
    if (['pending_procurement_arrangement', 'requested_from_stock'].includes(state)) {
      return 'procurement'
    }
    if (['awaiting_kitting_packing', 'in_kitting_packing', 'on_hold_at_kitting_packing'].includes(state)) {
      return 'kitting'
    }
    if (['kitted_packed_awaiting_screening_qc', 'in_screening_qc', 'on_hold_at_screening_qc', 'screening_qc_passed_ready_for_invoice', 'screening_qc_rejected'].includes(state)) {
      return 'qc'
    }
    return 'final'
  }
}