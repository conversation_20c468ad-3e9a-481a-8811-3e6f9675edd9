// GEMMA TEST - CT Number Validation
// Tests the core CT number validation logic to prevent business rule violations

import { describe, test, expect } from 'vitest'
import { 
  validateCTNumber, 
  validateCTNumbers, 
  formatCTForDisplay, 
  unformatCTNumber,
  extractCTNumbers,
  validateCTBatch,
  generateCTNumber
} from '../../utils/ctNumberValidation'

describe('GEMMA: CT Number Validation', () => {
  describe('validateCTNumber - Core Business Logic', () => {
    test('accepts valid 14-character alphanumeric CT number', () => {
      const result = validateCTNumber('ABCD1234567890')
      
      expect(result.isValid).toBe(true)
      expect(result.formatted).toBe('ABCD1234567890')
      expect(result.errors).toHaveLength(0)
    })

    test('converts lowercase to uppercase', () => {
      const result = validateCTNumber('abcd1234567890')
      
      expect(result.isValid).toBe(true)
      expect(result.formatted).toBe('ABCD1234567890')
    })

    test('removes special characters and validates', () => {
      const result = validateCTNumber('ABCD-1234-5678-90')
      
      expect(result.isValid).toBe(true)
      expect(result.formatted).toBe('ABCD1234567890')
    })

    test('rejects CT numbers that are too short', () => {
      const result = validateCTNumber('ABC123')
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('CT number must be exactly 14 characters (currently 6)')
    })

    test('rejects CT numbers that are too long', () => {
      const result = validateCTNumber('ABCD1234567890EXTRA')
      
      expect(result.isValid).toBe(false)
      expect(result.formatted).toBe('ABCD1234567890') // Should truncate
      expect(result.errors).toContain('CT number must be exactly 14 characters (currently 19)')
    })

    test('rejects empty CT numbers', () => {
      const result = validateCTNumber('')
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('CT number cannot be empty')
    })

    test('handles whitespace correctly', () => {
      const result = validateCTNumber('  ABCD1234567890  ')
      
      expect(result.isValid).toBe(true)
      expect(result.formatted).toBe('ABCD1234567890')
    })
  })

  describe('validateCTNumbers - Batch Processing', () => {
    test('validates multiple CT numbers correctly', () => {
      const inputs = ['ABCD1234567890', 'EFGH0987654321', 'INVALID123']
      const results = validateCTNumbers(inputs)
      
      expect(results).toHaveLength(3)
      expect(results[0].isValid).toBe(true)
      expect(results[1].isValid).toBe(true)
      expect(results[2].isValid).toBe(false)
    })
  })

  describe('formatCTForDisplay - Visual Formatting', () => {
    test('formats valid CT number with dashes', () => {
      const formatted = formatCTForDisplay('ABCD1234567890')
      
      expect(formatted).toBe('ABCD-1234-5678-90')
    })

    test('returns unchanged if not 14 characters', () => {
      const formatted = formatCTForDisplay('ABC123')
      
      expect(formatted).toBe('ABC123')
    })
  })

  describe('unformatCTNumber - Remove Display Formatting', () => {
    test('removes dashes from formatted CT number', () => {
      const unformatted = unformatCTNumber('ABCD-1234-5678-90')
      
      expect(unformatted).toBe('ABCD1234567890')
    })

    test('converts to uppercase and removes formatting', () => {
      // Note: The function removes non-uppercase-alphanumeric first, then converts to uppercase
      // So lowercase letters get removed before conversion
      const unformatted = unformatCTNumber('ABCD-1234-5678-90')
      
      expect(unformatted).toBe('ABCD1234567890')
    })
  })

  describe('extractCTNumbers - Input Parsing', () => {
    test('extracts CT numbers from multi-line input', () => {
      const input = `ABCD1234567890
      EFGH0987654321
      IJKL1111222233`
      
      const extracted = extractCTNumbers(input)
      
      expect(extracted).toEqual(['ABCD1234567890', 'EFGH0987654321', 'IJKL1111222233'])
    })

    test('extracts CT numbers from comma-separated input', () => {
      const input = 'ABCD1234567890, EFGH0987654321, IJKL1111222233'
      
      const extracted = extractCTNumbers(input)
      
      expect(extracted).toEqual(['ABCD1234567890', 'EFGH0987654321', 'IJKL1111222233'])
    })

    test('handles mixed separators', () => {
      const input = 'ABCD1234567890\nEFGH0987654321,IJKL1111222233\tMNOP4444555566'
      
      const extracted = extractCTNumbers(input)
      
      expect(extracted).toEqual(['ABCD1234567890', 'EFGH0987654321', 'IJKL1111222233', 'MNOP4444555566'])
    })
  })

  describe('validateCTBatch - Batch Validation Summary', () => {
    test('provides accurate batch validation summary', () => {
      const inputs = [
        'ABCD1234567890',  // Valid
        'EFGH0987654321',  // Valid
        'INVALID123',      // Invalid - too short
        'ABCD1234567890'   // Duplicate
      ]
      
      const result = validateCTBatch(inputs)
      
      expect(result.total).toBe(4)
      expect(result.valid).toBe(2) // Only unique valid ones
      expect(result.invalid).toBe(1)
      expect(result.duplicatesInBatch).toContain('ABCD1234567890')
      expect(result.validCTs).toEqual(['ABCD1234567890', 'EFGH0987654321'])
      expect(result.invalidCTs).toHaveLength(1)
      expect(result.invalidCTs[0].ct).toBe('INVALID123')
    })

    test('handles all valid CT numbers', () => {
      const inputs = ['ABCD1234567890', 'EFGH0987654321', 'IJKL1111222233']
      
      const result = validateCTBatch(inputs)
      
      expect(result.total).toBe(3)
      expect(result.valid).toBe(3)
      expect(result.invalid).toBe(0)
      expect(result.duplicatesInBatch).toHaveLength(0)
    })

    test('handles all invalid CT numbers', () => {
      const inputs = ['ABC', 'DEF123', '']
      
      const result = validateCTBatch(inputs)
      
      expect(result.total).toBe(3)
      expect(result.valid).toBe(0)
      expect(result.invalid).toBe(3)
      expect(result.validCTs).toHaveLength(0)
      expect(result.invalidCTs).toHaveLength(3)
    })
  })

  describe('generateCTNumber - CT Generation Logic', () => {
    test('generates 14-character CT number', () => {
      const generated = generateCTNumber({ strategy: 'random_suffix' })
      
      expect(generated).toHaveLength(14)
      expect(/^[A-Z0-9]{14}$/.test(generated)).toBe(true)
    })

    test('uses FAI master strategy when base string provided', () => {
      const baseString = 'MASTERCT123456'
      const generated = generateCTNumber({ 
        strategy: 'fai_master', 
        baseString 
      })
      
      expect(generated).toBe('MASTERCT123456')
    })

    test('falls back to random when invalid base string', () => {
      const generated = generateCTNumber({ 
        strategy: 'fai_master', 
        baseString: 'INVALID' 
      })
      
      expect(generated).toHaveLength(14)
      expect(/^[A-Z0-9]{14}$/.test(generated)).toBe(true)
    })
  })
})