// GEMMA TEST - Date Format Validation
// Tests date validation functions to prevent incorrect date handling

import { describe, test, expect } from 'vitest'
import { 
  validateDDMMYY,
  validateOrderQuantity,
  validatePricePerUnit,
  validateVID,
  validateMSC,
  validateImportRow
} from '../../utils/orderImportValidation'

describe('GEMMA: Date Format Validation', () => {
  describe('validateDDMMYY - Legacy DDMMYY format (backward compatibility)', () => {
    test('accepts valid DDMMYY format (6 digits)', () => {
      const result = validateDDMMYY('070125') // Jan 7, 2025
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2025-01-07') // Should be correct now
      expect(result.error).toBeUndefined()
    })

    test('now accepts DD/MM/YY format with slashes (updated behavior)', () => {
      const result = validateDDMMYY('07/01/25') // Jan 7, 2025 with slashes
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2025-01-07')
      expect(result.error).toBeUndefined()
    })

    test('handles leap year correctly', () => {
      const result = validateDDMMYY('290224') // Feb 29, 2024 (leap year)
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2024-02-29') // Should be correct now
    })

    test('rejects invalid leap year date', () => {
      const result = validateDDMMYY('290225') // Feb 29, 2025 (not leap year)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Date does not exist')
    })

    test('rejects invalid day', () => {
      const result = validateDDMMYY('320125') // January 32nd
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid day "32"')
    })

    test('rejects invalid month', () => {
      const result = validateDDMMYY('071325') // Month 13
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid month "13"')
    })

    test('accepts empty date as valid', () => {
      const result = validateDDMMYY('')
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBeUndefined()
    })

    test('rejects too short date string', () => {
      const result = validateDDMMYY('0725') // Only 4 digits
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid date format')
    })

    test('rejects too long date string', () => {
      const result = validateDDMMYY('07012025') // 8 digits
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid date format')
    })
  })

  describe('validateDDMMYY - NEW: DD/MM/YY Format (With Slashes)', () => {
    test('should accept DD/MM/YY format with slashes', () => {
      const result = validateDDMMYY('07/01/25') // Jan 7, 2025
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2025-01-07')
    })

    test('should accept DD/MM/YYYY format with full year', () => {
      const result = validateDDMMYY('07/01/2025') // Jan 7, 2025
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2025-01-07')
    })

    test('should handle single digit days and months', () => {
      const result = validateDDMMYY('7/1/25') // Jan 7, 2025
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2025-01-07')
    })

    test('should reject invalid DD/MM/YY dates', () => {
      const result = validateDDMMYY('32/01/25') // Jan 32nd
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid day')
    })

    test('should handle leap years in DD/MM/YY format', () => {
      const result = validateDDMMYY('29/02/24') // Feb 29, 2024 (leap year)
      
      expect(result.isValid).toBe(true)
      expect(result.isoDate).toBe('2024-02-29')
    })

    test('should reject invalid leap year in DD/MM/YY format', () => {
      const result = validateDDMMYY('29/02/25') // Feb 29, 2025 (not leap year)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Date does not exist')
    })

    test('should reject malformed DD/MM/YY dates', () => {
      const result = validateDDMMYY('7/1') // Missing year
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Invalid date format')
    })
  })

  describe('validateOrderQuantity - Business Logic', () => {
    test('accepts valid positive integer', () => {
      const result = validateOrderQuantity(100)
      
      expect(result.isValid).toBe(true)
      expect(result.value).toBe(100)
      expect(result.error).toBeUndefined()
    })

    test('accepts valid string number', () => {
      const result = validateOrderQuantity('50')
      
      expect(result.isValid).toBe(true)
      expect(result.value).toBe(50)
    })

    test('rejects zero quantity', () => {
      const result = validateOrderQuantity(0)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Quantity must be positive')
    })

    test('rejects negative quantity', () => {
      const result = validateOrderQuantity(-5)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Quantity must be positive')
    })

    test('rejects decimal quantity', () => {
      const result = validateOrderQuantity(10.5)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Quantity must be a whole number')
    })

    test('rejects non-numeric quantity', () => {
      const result = validateOrderQuantity('abc')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be a number')
    })

    test('rejects empty/null quantity', () => {
      const result = validateOrderQuantity('')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Order Quantity is required')
    })
  })

  describe('validatePricePerUnit - Financial Validation', () => {
    test('accepts valid positive price', () => {
      const result = validatePricePerUnit(25.99)
      
      expect(result.isValid).toBe(true)
      expect(result.value).toBe(25.99)
    })

    test('accepts zero price', () => {
      const result = validatePricePerUnit(0)
      
      expect(result.isValid).toBe(true)
      // Note: 0 is falsy, so function returns early without setting value
      expect(result.value).toBeUndefined()
    })

    test('accepts empty price when not required', () => {
      const result = validatePricePerUnit('', false)
      
      expect(result.isValid).toBe(true)
      expect(result.value).toBeUndefined()
    })

    test('rejects empty price when required', () => {
      const result = validatePricePerUnit('', true)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Price is required')
    })

    test('rejects negative price', () => {
      const result = validatePricePerUnit(-10)
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Price cannot be negative')
    })

    test('accepts string number price', () => {
      const result = validatePricePerUnit('15.50')
      
      expect(result.isValid).toBe(true)
      expect(result.value).toBe(15.50)
    })
  })

  describe('validateVID - Vendor ID Format', () => {
    test('accepts valid 2-character VID', () => {
      const result = validateVID('HP')
      
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    test('accepts valid 10-character VID', () => {
      const result = validateVID('VENDOR1234')
      
      expect(result.isValid).toBe(true)
    })

    test('accepts empty VID (optional field)', () => {
      const result = validateVID('')
      
      expect(result.isValid).toBe(true)
    })

    test('rejects VID too short', () => {
      const result = validateVID('A')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 2-10 alphanumeric characters')
    })

    test('rejects VID too long', () => {
      const result = validateVID('TOOLONGVENDOR')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 2-10 alphanumeric characters')
    })

    test('rejects VID with special characters', () => {
      const result = validateVID('HP@123')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 2-10 alphanumeric characters')
    })
  })

  describe('validateMSC - Master Shipping Carton Format', () => {
    test('accepts valid 5-character MSC', () => {
      const result = validateMSC('MSC01')
      
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    test('accepts valid 6-character MSC', () => {
      const result = validateMSC('MSC001')
      
      expect(result.isValid).toBe(true)
    })

    test('accepts empty MSC (optional field)', () => {
      const result = validateMSC('')
      
      expect(result.isValid).toBe(true)
    })

    test('rejects MSC too short', () => {
      const result = validateMSC('MSC1')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 5-6 alphanumeric characters')
    })

    test('rejects MSC too long', () => {
      const result = validateMSC('MSC0001')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 5-6 alphanumeric characters')
    })

    test('rejects MSC with special characters', () => {
      const result = validateMSC('MSC-01')
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('Must be 5-6 alphanumeric characters')
    })
  })
})