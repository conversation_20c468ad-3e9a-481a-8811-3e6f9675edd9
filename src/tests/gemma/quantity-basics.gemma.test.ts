// GEMMA TEST - Quantity Calculations & Basic Math
// Tests quantity tracking calculations to prevent arithmetic errors and business logic bugs

import { describe, test, expect } from 'vitest'
import type { QuantityData } from '../../hooks/useQuantityTracking'

// Global helper functions (from useQuantityTracking utility functions)
function getTotalInProgress(quantities: QuantityData): number {
  return quantities.inKittingPacking + quantities.inScreeningQc
}

function getTotalOnHold(quantities: QuantityData): number {
  return quantities.onHoldAtKittingPacking + quantities.onHoldAtScreeningQc
}

function getTotalCompleted(quantities: QuantityData): number {
  return quantities.shippedDelivered + quantities.cancelled
}

function getProgressPercentage(quantities: QuantityData): number {
  if (quantities.totalOrderQuantity === 0) return 0
  const completed = quantities.shippedDelivered + quantities.cancelled
  return Math.round((completed / quantities.totalOrderQuantity) * 100)
}

describe('GEMMA: Quantity Calculations & Basic Math', () => {
  // Helper function to create sample quantity data
  function createQuantityData(overrides: Partial<QuantityData> = {}): QuantityData {
    return {
      orderLineId: 'test-order-123',
      totalOrderQuantity: 100,
      pendingProcurementArrangement: 0,
      requestedFromStock: 0,
      awaitingKittingPacking: 0,
      inKittingPacking: 0,
      onHoldAtKittingPacking: 0,
      kittedPackedAwaitingScreeningQc: 0,
      inScreeningQc: 0,
      onHoldAtScreeningQc: 0,
      screeningQcPassedReadyForInvoice: 0,
      screeningQcRejected: 0,
      invoiced: 0,
      shippedDelivered: 0,
      cancelled: 0,
      lastUpdatedAt: new Date().toISOString(),
      lastUpdatedBy: 'test-user',
      notes: 'Test data',
      ...overrides
    }
  }

  describe('Total In Progress Calculation', () => {

    test('calculates zero when no quantities in progress', () => {
      const quantities = createQuantityData()
      
      const result = getTotalInProgress(quantities)
      
      expect(result).toBe(0)
    })

    test('calculates total when items in kitting only', () => {
      const quantities = createQuantityData({
        inKittingPacking: 25
      })
      
      const result = getTotalInProgress(quantities)
      
      expect(result).toBe(25)
    })

    test('calculates total when items in QC only', () => {
      const quantities = createQuantityData({
        inScreeningQc: 15
      })
      
      const result = getTotalInProgress(quantities)
      
      expect(result).toBe(15)
    })

    test('calculates total when items in both kitting and QC', () => {
      const quantities = createQuantityData({
        inKittingPacking: 30,
        inScreeningQc: 20
      })
      
      const result = getTotalInProgress(quantities)
      
      expect(result).toBe(50) // 30 + 20
    })

    test('ignores other states in calculation', () => {
      const quantities = createQuantityData({
        inKittingPacking: 10,
        inScreeningQc: 5,
        pendingProcurementArrangement: 50,
        shippedDelivered: 35
      })
      
      const result = getTotalInProgress(quantities)
      
      expect(result).toBe(15) // Only 10 + 5, ignores pending and shipped
    })
  })

  describe('Total On Hold Calculation', () => {

    test('calculates zero when no quantities on hold', () => {
      const quantities = createQuantityData()
      
      const result = getTotalOnHold(quantities)
      
      expect(result).toBe(0)
    })

    test('calculates total when items on hold at kitting only', () => {
      const quantities = createQuantityData({
        onHoldAtKittingPacking: 12
      })
      
      const result = getTotalOnHold(quantities)
      
      expect(result).toBe(12)
    })

    test('calculates total when items on hold at QC only', () => {
      const quantities = createQuantityData({
        onHoldAtScreeningQc: 8
      })
      
      const result = getTotalOnHold(quantities)
      
      expect(result).toBe(8)
    })

    test('calculates total when items on hold at both stages', () => {
      const quantities = createQuantityData({
        onHoldAtKittingPacking: 7,
        onHoldAtScreeningQc: 3
      })
      
      const result = getTotalOnHold(quantities)
      
      expect(result).toBe(10) // 7 + 3
    })
  })

  describe('Total Completed Calculation', () => {

    test('calculates zero when nothing completed', () => {
      const quantities = createQuantityData()
      
      const result = getTotalCompleted(quantities)
      
      expect(result).toBe(0)
    })

    test('calculates total when items shipped only', () => {
      const quantities = createQuantityData({
        shippedDelivered: 85
      })
      
      const result = getTotalCompleted(quantities)
      
      expect(result).toBe(85)
    })

    test('calculates total when items cancelled only', () => {
      const quantities = createQuantityData({
        cancelled: 15
      })
      
      const result = getTotalCompleted(quantities)
      
      expect(result).toBe(15)
    })

    test('calculates total when items both shipped and cancelled', () => {
      const quantities = createQuantityData({
        shippedDelivered: 70,
        cancelled: 10
      })
      
      const result = getTotalCompleted(quantities)
      
      expect(result).toBe(80) // 70 + 10
    })
  })

  describe('Progress Percentage Calculation', () => {

    test('returns 0 when total order quantity is zero', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 0,
        shippedDelivered: 5
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(0)
    })

    test('returns 0 when nothing is completed', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(0)
    })

    test('returns 100 when all items are completed', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100,
        shippedDelivered: 100
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(100)
    })

    test('calculates correct percentage for partial completion', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100,
        shippedDelivered: 60,
        cancelled: 15
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(75) // (60 + 15) / 100 * 100 = 75%
    })

    test('rounds percentage correctly', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 3,
        shippedDelivered: 1
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(33) // 1/3 = 33.333... rounds to 33
    })

    test('handles exact division', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 4,
        shippedDelivered: 1
      })
      
      const result = getProgressPercentage(quantities)
      
      expect(result).toBe(25) // 1/4 = 0.25 = 25%
    })
  })

  describe('Quantity State Sum Validation', () => {
    test('all quantities should sum to total order quantity (business rule)', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100,
        pendingProcurementArrangement: 20,
        requestedFromStock: 15,
        awaitingKittingPacking: 10,
        inKittingPacking: 8,
        onHoldAtKittingPacking: 2,
        kittedPackedAwaitingScreeningQc: 5,
        inScreeningQc: 12,
        onHoldAtScreeningQc: 3,
        screeningQcPassedReadyForInvoice: 7,
        screeningQcRejected: 8,
        invoiced: 5,
        shippedDelivered: 4,
        cancelled: 1
      })

      const sumOfStates = 
        quantities.pendingProcurementArrangement +
        quantities.requestedFromStock +
        quantities.awaitingKittingPacking +
        quantities.inKittingPacking +
        quantities.onHoldAtKittingPacking +
        quantities.kittedPackedAwaitingScreeningQc +
        quantities.inScreeningQc +
        quantities.onHoldAtScreeningQc +
        quantities.screeningQcPassedReadyForInvoice +
        quantities.screeningQcRejected +
        quantities.invoiced +
        quantities.shippedDelivered +
        quantities.cancelled

      expect(sumOfStates).toBe(quantities.totalOrderQuantity)
      expect(sumOfStates).toBe(100) // Double check
    })

    test('detects invalid quantity distribution', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100,
        pendingProcurementArrangement: 50,
        shippedDelivered: 60 // Total would be 110, which is invalid
      })

      const sumOfStates = 
        quantities.pendingProcurementArrangement +
        quantities.requestedFromStock +
        quantities.awaitingKittingPacking +
        quantities.inKittingPacking +
        quantities.onHoldAtKittingPacking +
        quantities.kittedPackedAwaitingScreeningQc +
        quantities.inScreeningQc +
        quantities.onHoldAtScreeningQc +
        quantities.screeningQcPassedReadyForInvoice +
        quantities.screeningQcRejected +
        quantities.invoiced +
        quantities.shippedDelivered +
        quantities.cancelled

      // This should NOT equal total order quantity (indicates data corruption)
      expect(sumOfStates).not.toBe(quantities.totalOrderQuantity)
      expect(sumOfStates).toBe(110) // Should be 100
    })
  })

  describe('Edge Cases & Error Conditions', () => {
    test('handles negative quantities correctly', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100,
        inKittingPacking: -5 // Invalid negative quantity
      })

      const result = getTotalInProgress(quantities)
      
      // Function should still work, but negative values indicate data problems
      expect(result).toBe(-5)
      expect(result).toBeLessThan(0) // This is a data integrity issue
    })

    test('handles very large quantities', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 1000000,
        shippedDelivered: 999999
      })

      const percentage = getProgressPercentage(quantities)
      
      expect(percentage).toBe(100) // 999999/1000000 ≈ 100%
    })

    test('handles zero division edge case', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 0,
        shippedDelivered: 0
      })

      const percentage = getProgressPercentage(quantities)
      
      expect(percentage).toBe(0) // Should not throw division by zero
    })

    test('handles decimal quantities (should be whole numbers)', () => {
      const quantities = createQuantityData({
        totalOrderQuantity: 100.5, // Invalid decimal quantity
        shippedDelivered: 50.3
      })

      const percentage = getProgressPercentage(quantities)
      
      // Function works, but decimals indicate data integrity issues
      expect(percentage).toBe(50) // Math.round(50.3/100.5 * 100) = 50
    })
  })

  describe('State Transition Math', () => {
    test('moving quantity between states maintains total', () => {
      const initialQuantities = createQuantityData({
        totalOrderQuantity: 100,
        pendingProcurementArrangement: 100
      })

      // Simulate moving 25 from pending to kitting
      const afterTransition = createQuantityData({
        totalOrderQuantity: 100,
        pendingProcurementArrangement: 75, // Decreased by 25
        awaitingKittingPacking: 25 // Increased by 25
      })

      const initialSum = 
        initialQuantities.pendingProcurementArrangement +
        initialQuantities.awaitingKittingPacking

      const afterSum = 
        afterTransition.pendingProcurementArrangement +
        afterTransition.awaitingKittingPacking

      expect(initialSum).toBe(100)
      expect(afterSum).toBe(100)
      expect(initialSum).toBe(afterSum) // Total preserved
    })

    test('validates minimum quantity requirements', () => {
      const quantities = createQuantityData({
        pendingProcurementArrangement: 10
      })

      // Trying to move 15 items when only 10 available
      const attemptedMove = 15
      const available = quantities.pendingProcurementArrangement

      expect(attemptedMove).toBeGreaterThan(available)
      expect(available).toBeLessThan(attemptedMove)
      
      // In real implementation, this should be blocked
      const canMove = available >= attemptedMove
      expect(canMove).toBe(false)
    })
  })
})