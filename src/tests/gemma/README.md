# GEMMA Testing Suite

**Purpose**: Simple, practical tests to prevent rollbacks and catch business logic errors early.

## What is GEMMA Testing?

GEMMA tests are **safety nets** for your working code. They check that critical business functions continue to work correctly as you build new features.

## Files in this folder:

### **Foundation Tests** (Basic Functions)
- **ct-validation.gemma.test.ts** ✅ - Tests CT number validation (14-digit, alphanumeric, business rules)
- **date-validation.gemma.test.ts** ✅ - Tests DD/MM/YY date format, quantity, price, VID, MSC validation
- **order-creation.gemma.test.ts** ✅ - Tests UID generation, date conversion, order data preparation
- **quantity-basics.gemma.test.ts** ✅ - Tests quantity calculations, progress percentages, business rules
- **database.gemma.test.ts** ✅ - Tests Supabase connectivity, query structure, error handling

### **Business Relationship Chain Tests** (How Systems Work Together)
- **chain-order-realtime.gemma.test.ts** ✅ - Tests Order Creation → Database → Real-time Updates
- **chain-state-audit.gemma.test.ts** ✅ - Tests Quantity State Changes → Audit Logging → History
- **chain-permissions-workflow.gemma.test.ts** ✅ - Tests User Roles → Allowed Actions → UI Changes
- **chain-whatsapp-notifications.gemma.test.ts** ✅ - Tests QC Events → WhatsApp → Approval Flows
- **chain-workflow-coordination.gemma.test.ts** ✅ - Tests Enhanced Workflow State Management → Cross-Workflow Coordination
- **chain-quantity-tracking-integration.gemma.test.ts** ✅ - Tests Quantity Tracking Integration → Progressive State Transitions
- **chain-user-management-integration.gemma.test.ts** ✅ - Tests User Management Integration → Role-Based Administration

## What are Business Relationship Chain Tests?

**Chain tests verify that when you change one thing, all related things update correctly.**

Examples:
- When you create an order → UID generates → Database saves → Other users see it instantly
- When you change quantity state → Audit log created → Progress bars update → Notifications sent
- When user role changes → UI shows different options → Database access restricted

**Why Chain Tests Matter:**
- **Prevent Rollbacks**: Catch when one system breaks another system
- **Real-world Scenarios**: Test actual user workflows, not isolated functions
- **Integration Confidence**: Verify multiple systems work together correctly

## How to run tests:

```bash
# Run all GEMMA tests once
npm run test:gemma

# Run tests and watch for changes
npm run test:gemma:watch

# Run only foundation tests (basic functions)
npm run test:gemma -- --testNamePattern="^(?!.*Chain)"

# Run only business relationship chain tests
npm run test:gemma -- --testNamePattern="Chain"
```

## When to run tests:

1. **Before adding new features** - Make sure existing code still works
2. **After major changes** - Verify you didn't break anything
3. **Before committing code** - Final safety check

## Current Test Status:

**Foundation Tests**: 122 tests ✅ (all passing - date format migration complete)  
**Chain Tests**: 7 test suites ✅ (350+ business relationship tests implemented)
- Original 4 chain tests: 180 business relationship tests
- New integration tests: 170+ advanced workflow coordination tests
- Complete coverage of enhanced system integrations

**Total Coverage**: Foundation functions + Complete business workflow relationships + Advanced integration testing

## Test Results:

- ✅ **Green/Pass** = Function works correctly
- ❌ **Red/Fail** = Something is broken, needs fixing

## Easy Removal:

If you don't like GEMMA tests:
1. Delete this entire `src/tests/gemma/` folder
2. Remove `vitest` from package.json dependencies
3. Remove `test:gemma` scripts from package.json

---

**Created**: January 9, 2025  
**Purpose**: Prevent rollbacks, catch errors early, build with confidence