// GEMMA TEST - Order Creation Logic
// Tests order creation functions to prevent UID conflicts and data validation issues

import { describe, test, expect, beforeEach, vi } from 'vitest'

// Mock Supabase to avoid database dependencies during testing
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      like: vi.fn(() => ({
        order: vi.fn(() => ({
          // This will be overridden in individual tests
        }))
      }))
    })),
    insert: vi.fn(() => ({
      select: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
}

// Mock the supabase import
vi.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}))

// Import the functions we want to test
// Note: These are not exported from the hook, so we'll test the logic directly

describe('GEMMA: Order Creation Logic', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('UID Generation Logic', () => {
    test('generates A001 for first order when no existing orders', () => {
      // Test the UID generation logic
      const uidPrefix = 'A'
      const existingUIDs: string[] = []
      
      let nextNumber = 1
      if (existingUIDs.length > 0) {
        const numbers = existingUIDs
          .map(uid => {
            const numberPart = uid.replace(uidPrefix, '')
            return parseInt(numberPart, 10)
          })
          .filter(num => !isNaN(num))
          .sort((a, b) => b - a)
        
        if (numbers.length > 0) {
          nextNumber = numbers[0] + 1
        }
      }
      
      const formattedNumber = nextNumber.toString().padStart(3, '0')
      const newUID = `${uidPrefix}${formattedNumber}`
      
      expect(newUID).toBe('A001')
    })

    test('generates next sequential UID when orders exist', () => {
      const uidPrefix = 'A'
      const existingUIDs = ['A001', 'A002', 'A005'] // Simulate gap in sequence
      
      let nextNumber = 1
      if (existingUIDs.length > 0) {
        const numbers = existingUIDs
          .map(uid => {
            const numberPart = uid.replace(uidPrefix, '')
            return parseInt(numberPart, 10)
          })
          .filter(num => !isNaN(num))
          .sort((a, b) => b - a)
        
        if (numbers.length > 0) {
          nextNumber = numbers[0] + 1
        }
      }
      
      const formattedNumber = nextNumber.toString().padStart(3, '0')
      const newUID = `${uidPrefix}${formattedNumber}`
      
      expect(newUID).toBe('A006') // Should be next after highest (A005)
    })

    test('handles non-numeric UIDs correctly', () => {
      const uidPrefix = 'A'
      const existingUIDs = ['A001', 'AXYZ', 'A002', 'INVALID'] // Mixed valid/invalid
      
      let nextNumber = 1
      if (existingUIDs.length > 0) {
        const numbers = existingUIDs
          .map(uid => {
            const numberPart = uid.replace(uidPrefix, '')
            return parseInt(numberPart, 10)
          })
          .filter(num => !isNaN(num)) // This filters out NaN from 'XYZ', 'NVALID'
          .sort((a, b) => b - a)
        
        if (numbers.length > 0) {
          nextNumber = numbers[0] + 1
        }
      }
      
      const formattedNumber = nextNumber.toString().padStart(3, '0')
      const newUID = `${uidPrefix}${formattedNumber}`
      
      expect(newUID).toBe('A003') // Should ignore non-numeric UIDs
    })

    test('generates UID with different prefix', () => {
      const uidPrefix = 'B'
      const existingUIDs = ['B010', 'B011']
      
      let nextNumber = 1
      if (existingUIDs.length > 0) {
        const numbers = existingUIDs
          .map(uid => {
            const numberPart = uid.replace(uidPrefix, '')
            return parseInt(numberPart, 10)
          })
          .filter(num => !isNaN(num))
          .sort((a, b) => b - a)
        
        if (numbers.length > 0) {
          nextNumber = numbers[0] + 1
        }
      }
      
      const formattedNumber = nextNumber.toString().padStart(3, '0')
      const newUID = `${uidPrefix}${formattedNumber}`
      
      expect(newUID).toBe('B012')
    })

    test('pads UID numbers to 3 digits', () => {
      const uidPrefix = 'A'
      const numbers = [1, 15, 999]
      
      numbers.forEach(num => {
        const formattedNumber = num.toString().padStart(3, '0')
        const uid = `${uidPrefix}${formattedNumber}`
        
        expect(uid.length).toBe(4) // A + 3 digits
        expect(uid).toMatch(/^A\d{3}$/)
      })
      
      expect(`A${numbers[0].toString().padStart(3, '0')}`).toBe('A001')
      expect(`A${numbers[1].toString().padStart(3, '0')}`).toBe('A015')
      expect(`A${numbers[2].toString().padStart(3, '0')}`).toBe('A999')
    })
  })

  describe('Date Conversion Logic', () => {
    function convertDateToISO(ddmmyy: string): string | null {
      if (!ddmmyy || ddmmyy.length !== 6) return null
      
      const day = ddmmyy.substring(0, 2)
      const month = ddmmyy.substring(2, 4)
      const year = `20${ddmmyy.substring(4, 6)}` // Assume 20xx years
      
      // Validate the date
      const date = new Date(`${year}-${month}-${day}`)
      if (isNaN(date.getTime())) return null
      
      return date.toISOString().split('T')[0] // Return YYYY-MM-DD format
    }

    test('converts valid DDMMYY to ISO format', () => {
      const result = convertDateToISO('070125') // Jan 7, 2025
      
      expect(result).toBe('2025-01-07')
    })

    test('converts leap year date correctly', () => {
      const result = convertDateToISO('290224') // Feb 29, 2024
      
      expect(result).toBe('2024-02-29')
    })

    test('returns null for invalid date', () => {
      const result = convertDateToISO('320125') // Jan 32nd doesn't exist
      
      expect(result).toBeNull()
    })

    test('returns null for empty or invalid input', () => {
      expect(convertDateToISO('')).toBeNull()
      expect(convertDateToISO('123')).toBeNull() // Too short
      expect(convertDateToISO('1234567')).toBeNull() // Too long
      expect(convertDateToISO('ABCDEF')).toBeNull() // Non-numeric
    })

    test('handles different years correctly', () => {
      expect(convertDateToISO('010124')).toBe('2024-01-01') // 2024
      expect(convertDateToISO('010125')).toBe('2025-01-01') // 2025
      expect(convertDateToISO('010199')).toBe('2099-01-01') // 2099
      expect(convertDateToISO('010100')).toBe('2000-01-01') // 2000
    })
  })

  describe('Order Data Preparation', () => {
    test('prepares order data correctly with all fields', () => {
      const inputData = {
        customer_id: 'cust-123',
        po_number: 'PO001',
        po_date: '070125',
        customer_part_number: '  PART123  ',
        core_part_number: '  CORE456  ',
        customer_description: '  Test Description  ',
        bpi_description: '  BPI Desc  ',
        product_category_id: 'cat-456',
        order_quantity: 100,
        price: 25.99,
        lead_time: '  2 weeks  ',
        current_eta: '150125',
        vid: '  VID01  ',
        msc: '  MSC01  ',
        misc_field_1: '  Misc1  ',
        misc_field_2: '  Misc2  ',
        misc_field_3: '  Misc3  ',
        created_by: 'user-789'
      }

      // Simulate the data preparation logic
      const dbOrderData = {
        uid: 'A001', // Would be generated
        customer_id: inputData.customer_id,
        po_number: inputData.po_number || null,
        po_date: inputData.po_date ? '2025-01-07' : null, // Converted
        customer_part_number: inputData.customer_part_number.trim(),
        core_part_number: inputData.core_part_number?.trim() || null,
        customer_description: inputData.customer_description?.trim() || null,
        bpi_description: inputData.bpi_description?.trim() || null,
        product_category_id: inputData.product_category_id,
        order_quantity: inputData.order_quantity,
        price: inputData.price || null,
        lead_time: inputData.lead_time?.trim() || null,
        current_eta: inputData.current_eta ? '2025-01-15' : null, // Converted
        vid: inputData.vid?.trim() || null,
        msc: inputData.msc?.trim() || null,
        misc_field_1: inputData.misc_field_1?.trim() || null,
        misc_field_2: inputData.misc_field_2?.trim() || null,
        misc_field_3: inputData.misc_field_3?.trim() || null,
        created_by: inputData.created_by,
        part_mapping_approved: false,
        assigned_user_ids: [],
        eta_delay_reasons: {}
      }

      expect(dbOrderData.customer_part_number).toBe('PART123') // Trimmed
      expect(dbOrderData.core_part_number).toBe('CORE456') // Trimmed
      expect(dbOrderData.po_date).toBe('2025-01-07') // Converted
      expect(dbOrderData.current_eta).toBe('2025-01-15') // Converted
      expect(dbOrderData.vid).toBe('VID01') // Trimmed
      expect(dbOrderData.part_mapping_approved).toBe(false) // Default
      expect(dbOrderData.assigned_user_ids).toEqual([]) // Default
    })

    test('handles optional fields correctly', () => {
      const minimalData = {
        customer_id: 'cust-123',
        customer_part_number: 'PART123',
        product_category_id: 'cat-456',
        order_quantity: 50,
        created_by: 'user-789'
      }

      const dbOrderData = {
        uid: 'A001',
        customer_id: minimalData.customer_id,
        po_number: null, // Optional, not provided
        po_date: null, // Optional, not provided
        customer_part_number: minimalData.customer_part_number.trim(),
        core_part_number: null, // Optional, not provided
        customer_description: null, // Optional, not provided
        bpi_description: null, // Optional, not provided
        product_category_id: minimalData.product_category_id,
        order_quantity: minimalData.order_quantity,
        price: null, // Optional, not provided
        lead_time: null, // Optional, not provided
        current_eta: null, // Optional, not provided
        vid: null, // Optional, not provided
        msc: null, // Optional, not provided
        misc_field_1: null, // Optional, not provided
        misc_field_2: null, // Optional, not provided
        misc_field_3: null, // Optional, not provided
        created_by: minimalData.created_by,
        part_mapping_approved: false,
        assigned_user_ids: [],
        eta_delay_reasons: {}
      }

      expect(dbOrderData.po_number).toBeNull()
      expect(dbOrderData.po_date).toBeNull()
      expect(dbOrderData.price).toBeNull()
      expect(dbOrderData.customer_part_number).toBe('PART123')
      expect(dbOrderData.order_quantity).toBe(50)
    })
  })

  describe('Quantities Initialization', () => {
    test('creates correct initial quantities record', () => {
      const orderQuantity = 100
      const orderLineId = 'order-123'

      const quantitiesData = {
        order_line_id: orderLineId,
        total_order_quantity: orderQuantity,
        pending_procurement: orderQuantity, // Start with all quantity pending
        requested_from_stock: 0,
        awaiting_kitting_packing: 0,
        in_kitting_packing: 0,
        on_hold_kitting: 0,
        kitted_awaiting_qc: 0,
        in_screening_qc: 0,
        on_hold_qc: 0,
        qc_passed_ready_invoice: 0,
        qc_rejected: 0,
        invoiced: 0,
        shipped_delivered: 0,
        cancelled: 0
      }

      expect(quantitiesData.total_order_quantity).toBe(100)
      expect(quantitiesData.pending_procurement).toBe(100) // All quantity starts as pending
      expect(quantitiesData.requested_from_stock).toBe(0)
      expect(quantitiesData.qc_passed_ready_invoice).toBe(0)
      
      // Verify all other states start at 0
      const stateFields = [
        'awaiting_kitting_packing', 'in_kitting_packing', 'on_hold_kitting',
        'kitted_awaiting_qc', 'in_screening_qc', 'on_hold_qc',
        'qc_rejected', 'invoiced', 'shipped_delivered', 'cancelled'
      ]
      
      stateFields.forEach(field => {
        expect(quantitiesData[field as keyof typeof quantitiesData]).toBe(0)
      })
    })

    test('total equals sum of all quantity states initially', () => {
      const orderQuantity = 50
      
      const quantitiesData = {
        order_line_id: 'order-456',
        total_order_quantity: orderQuantity,
        pending_procurement: orderQuantity,
        requested_from_stock: 0,
        awaiting_kitting_packing: 0,
        in_kitting_packing: 0,
        on_hold_kitting: 0,
        kitted_awaiting_qc: 0,
        in_screening_qc: 0,
        on_hold_qc: 0,
        qc_passed_ready_invoice: 0,
        qc_rejected: 0,
        invoiced: 0,
        shipped_delivered: 0,
        cancelled: 0
      }

      // Calculate sum of all states
      const sumOfStates = 
        quantitiesData.pending_procurement +
        quantitiesData.requested_from_stock +
        quantitiesData.awaiting_kitting_packing +
        quantitiesData.in_kitting_packing +
        quantitiesData.on_hold_kitting +
        quantitiesData.kitted_awaiting_qc +
        quantitiesData.in_screening_qc +
        quantitiesData.on_hold_qc +
        quantitiesData.qc_passed_ready_invoice +
        quantitiesData.qc_rejected +
        quantitiesData.invoiced +
        quantitiesData.shipped_delivered +
        quantitiesData.cancelled

      expect(sumOfStates).toBe(quantitiesData.total_order_quantity)
    })
  })
})