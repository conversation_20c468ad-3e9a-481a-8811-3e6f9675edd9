@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Zoom-responsive adjustments */
@layer utilities {
  /* Auto-adjust for browser zoom levels */
  .zoom-responsive {
    transform-origin: top left;
  }
  
  /* Ensure minimum sizes remain readable at all zoom levels */
  .min-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Flexible grid that adapts to zoom */
  .adaptive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
    gap: 1rem;
  }
  
  /* Responsive text scaling */
  .responsive-text {
    font-size: clamp(0.75rem, 1vw + 0.5rem, 1rem);
  }
}

/* Global responsive adjustments */
@media screen and (max-width: 1024px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

@media screen and (max-width: 768px) {
  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

/* Improved mobile navigation spacing */
@media screen and (max-width: 768px) {
  .mobile-nav {
    padding: 0.5rem;
  }
}

/* DESKTOP MODAL OVERRIDE - Force desktop modal behavior */
@media screen and (min-width: 1025px) {
  .desktop-modal-override {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    right: auto !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    border-radius: 12px !important;
    max-width: 1280px !important;
    width: 95vw !important;
    max-height: 90vh !important;
    height: 90vh !important;
  }
}

/* Force override any mobile modal styles */
@media screen and (max-width: 768px) {
  .desktop-modal-override {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    right: auto !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    border-radius: 12px !important;
    max-width: 95vw !important;
    width: 95vw !important;
    max-height: 90vh !important;
    height: 90vh !important;
  }
}
@media screen and (max-width: 640px) {
  .nav-compact {
    height: 3rem;
  }
  
  .nav-compact .nav-item {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Animation utilities for dropdown menus */
@keyframes animate-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoom-in-95 {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-in {
  animation: animate-in 200ms ease-out;
}

.fade-in-0 {
  animation: fade-in 200ms ease-out;
}

.zoom-in-95 {
  animation: zoom-in-95 200ms ease-out;
}

/* Zoom-responsive layout adjustments */
@media (min-resolution: 0.75dppx) and (max-resolution: 0.95dppx) {
  /* When zoomed out to 75-95% */
  .grid {
    gap: 0.75rem !important;
  }
  
  body {
    font-size: 14px;
  }
}

@media (min-resolution: 0.5dppx) and (max-resolution: 0.74dppx) {
  /* When zoomed out to 50-74% */
  .grid {
    gap: 0.5rem !important;
  }
  
  body {
    font-size: 13px;
  }
  
  /* Ensure cards maintain minimum sizes */
  .grid > * {
    min-width: 180px;
  }
}

/* Ensure equal spacing in grids */
.grid {
  display: grid;
  grid-auto-rows: 1fr;
}

/* Maintain aspect ratios on zoom */
@supports (zoom: 1) {
  .maintain-ratio {
    zoom: 1;
  }
}