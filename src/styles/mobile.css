/* Mobile-specific styles for Mini-ERP */

/* ========================================
   MOBILE-FIRST RESPONSIVE DESIGN
======================================== */

/* Base mobile styles (320px and up) */
@media screen and (max-width: 768px) {
  
  /* Layout adjustments */
  .container {
    padding: 0.5rem !important;
    max-width: 100% !important;
  }
  
  .grid {
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }
  
  /* Navigation optimizations */
  .navigation {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    height: 60px !important;
    background: white !important;
    border-top: 1px solid #e5e7eb !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    z-index: 1000 !important;
  }
  
  .navigation-items {
    display: flex !important;
    justify-content: space-around !important;
    align-items: center !important;
    height: 100% !important;
    padding: 0 !important;
  }
  
  .navigation-item {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 0.25rem !important;
    font-size: 0.75rem !important;
    min-height: 44px !important;
  }
  
  .navigation-icon {
    width: 20px !important;
    height: 20px !important;
    margin-bottom: 2px !important;
  }
  
  /* Main content area adjustment for bottom nav */
  .main-content {
    padding-bottom: 80px !important;
    padding-top: 1rem !important;
  }
  
  /* Header optimizations */
  .header {
    position: sticky !important;
    top: 0 !important;
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
    z-index: 999 !important;
    padding: 0.75rem !important;
    min-height: 60px !important;
  }
  
  .header-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
  }
  
  /* Card optimizations */
  .card {
    margin: 0.5rem 0 !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
  
  .order-card {
    padding: 1rem !important;
    margin-bottom: 0.75rem !important;
  }
  
  .order-card-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }
  
  .order-card-actions {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important;
    width: 100% !important;
    margin-top: 1rem !important;
  }
  
  /* Button optimizations */
  .button-primary,
  .button-secondary,
  .button {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
    border-radius: 0.5rem !important;
    font-weight: 500 !important;
  }
  
  .button-icon-only {
    width: 44px !important;
    height: 44px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  /* Form optimizations */
  .form-group {
    margin-bottom: 1rem !important;
  }
  
  .form-label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    appearance: none !important;
  }
  
  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }
  
  /* Modal optimizations */
  .modal {
    margin: 0 !important;
    max-height: 100vh !important;
    border-radius: 1rem 1rem 0 0 !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    transform: none !important;
  }
  
  .modal-content {
    max-height: 80vh !important;
    overflow-y: auto !important;
    padding: 1.5rem !important;
  }
  
  .modal-header {
    padding: 1rem 1.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    position: sticky !important;
    top: 0 !important;
    background: white !important;
    z-index: 10 !important;
  }
  
  /* Table optimizations */
  .table-container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  .table {
    min-width: 100% !important;
    font-size: 0.875rem !important;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem !important;
    white-space: nowrap !important;
  }
  
  .table-responsive {
    display: block !important;
  }
  
  .table-responsive thead {
    display: none !important;
  }
  
  .table-responsive tbody,
  .table-responsive tr,
  .table-responsive td {
    display: block !important;
    width: 100% !important;
  }
  
  .table-responsive tr {
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.75rem !important;
    padding: 1rem !important;
    background: white !important;
  }
  
  .table-responsive td {
    border: none !important;
    padding: 0.25rem 0 !important;
    text-align: left !important;
  }
  
  .table-responsive td:before {
    content: attr(data-label) ": " !important;
    font-weight: 600 !important;
    color: #6b7280 !important;
  }
}

/* ========================================
   TOUCH-SPECIFIC OPTIMIZATIONS
======================================== */

.touch-enabled {
  /* Improve touch targets */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.touch-enabled .selectable {
  -webkit-user-select: text;
  user-select: text;
}

.touch-enabled .touchable {
  cursor: pointer;
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  transition: background-color 0.15s ease;
}

.touch-enabled .touchable:active {
  background-color: rgba(59, 130, 246, 0.05) !important;
  transform: scale(0.98);
}

/* ========================================
   SWIPE GESTURE STYLES
======================================== */

.swipe-container {
  position: relative;
  overflow: hidden;
}

.swipe-content {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-actions {
  position: absolute;
  top: 0;
  right: -100%;
  bottom: 0;
  width: 200px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: space-around;
  transition: right 0.3s ease;
  z-index: 5;
}

.show-quick-actions .quick-actions {
  right: 0;
}

.show-quick-actions .swipe-content {
  transform: translateX(-200px);
}

.quick-action-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.quick-action-button:active {
  transform: scale(0.9);
}

.quick-action-edit {
  background: #3b82f6;
}

.quick-action-delete {
  background: #ef4444;
}

.quick-action-print {
  background: #10b981;
}

/* ========================================
   PULL-TO-REFRESH STYLES
======================================== */

.pull-to-refresh-indicator {
  position: fixed;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: white;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.pull-to-refresh-active {
  top: 20px !important;
}

/* ========================================
   MOBILE NAVIGATION STYLES
======================================== */

.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-nav-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-menu {
  position: fixed;
  top: 0;
  left: -100%;
  bottom: 0;
  width: 280px;
  background: white;
  z-index: 1002;
  transition: left 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-menu.active {
  left: 0;
}

.mobile-nav-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.mobile-nav-items {
  padding: 1rem 0;
}

.mobile-nav-item {
  display: block;
  padding: 0.75rem 1.5rem;
  color: #374151;
  text-decoration: none;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.mobile-nav-item:hover,
.mobile-nav-item:focus {
  background: #f3f4f6;
  color: #1f2937;
}

.mobile-nav-item.active {
  background: #dbeafe;
  color: #1d4ed8;
  border-left: 3px solid #3b82f6;
}

/* ========================================
   MOBILE-SPECIFIC UTILITIES
======================================== */

.mobile-only {
  display: block !important;
}

.mobile-hidden {
  display: none !important;
}

.mobile-full-width {
  width: 100% !important;
}

.mobile-text-center {
  text-align: center !important;
}

.mobile-text-small {
  font-size: 0.875rem !important;
}

.mobile-spacing-tight {
  padding: 0.5rem !important;
  margin: 0.25rem !important;
}

.mobile-spacing-loose {
  padding: 1.5rem !important;
  margin: 1rem !important;
}

/* ========================================
   TABLET-SPECIFIC STYLES
======================================== */

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: 1fr 1fr !important;
  }
  
  .tablet-grid-3 {
    grid-template-columns: 1fr 1fr 1fr !important;
  }
  
  .tablet-navigation {
    position: relative !important;
    height: auto !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  .main-content {
    padding-bottom: 1rem !important;
  }
}

/* ========================================
   DESKTOP AND ABOVE
======================================== */

@media screen and (min-width: 1025px) {
  .mobile-only {
    display: none !important;
  }
  
  .mobile-hidden {
    display: block !important;
  }
  
  .desktop-grid-4 {
    grid-template-columns: 1fr 1fr 1fr 1fr !important;
  }
  
  .desktop-grid-5 {
    grid-template-columns: repeat(5, 1fr) !important;
  }
}

/* ========================================
   DARK MODE SUPPORT
======================================== */

@media (prefers-color-scheme: dark) {
  .mobile-nav-menu {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .mobile-nav-header {
    background: #111827;
    border-bottom-color: #374151;
  }
  
  .mobile-nav-item {
    color: #d1d5db;
    border-bottom-color: #374151;
  }
  
  .mobile-nav-item:hover,
  .mobile-nav-item:focus {
    background: #374151;
    color: #f9fafb;
  }
  
  .navigation {
    background: #1f2937 !important;
    border-top-color: #374151 !important;
  }
  
  .header {
    background: #1f2937 !important;
    border-bottom-color: #374151 !important;
    color: #f9fafb !important;
  }
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
======================================== */

.mobile-focus:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

.mobile-sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .button-primary,
  .button-secondary {
    border: 2px solid currentColor !important;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    border: 2px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .swipe-content,
  .quick-actions,
  .mobile-nav-menu,
  .mobile-nav-overlay,
  .pull-to-refresh-indicator {
    transition: none !important;
  }
  
  .touchable:active {
    transform: none !important;
  }
}

/* ========================================
   PRINT STYLES FOR MOBILE
======================================== */

@media print {
  .navigation,
  .mobile-nav-menu,
  .mobile-nav-overlay,
  .pull-to-refresh-indicator,
  .quick-actions {
    display: none !important;
  }
  
  .main-content {
    padding: 0 !important;
  }
  
  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
    margin: 0.5rem 0 !important;
  }
}