import React from 'react';
import { useMCPPrinting, type HealthIndicators } from '../../hooks/useMCPPrinting';
import { Badge } from './badge';
import { Wifi, WifiOff, Printer, Server, AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';

interface ConnectionStatusProps {
  className?: string;
  showDetails?: boolean;
  compact?: boolean;
}

interface StatusIndicatorProps {
  status: string;
  color: string;
  message: string;
  icon: React.ReactNode;
  details?: { [key: string]: any };
  compact?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ 
  status, 
  color, 
  message, 
  icon, 
  details, 
  compact = false 
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'online':
      case 'connected':
      case 'healthy':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'offline':
      case 'disconnected':
      case 'critical':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'degraded':
      case 'warning':
      case 'partial':
        return <AlertTriangle className="h-3 w-3 text-amber-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-1">
        {getStatusIcon()}
        <div 
          className="w-2 h-2 rounded-full" 
          style={{ backgroundColor: color }}
          title={message}
        />
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 p-2 rounded-lg border bg-card">
      <div className="flex items-center gap-1">
        {icon}
        <div 
          className="w-2 h-2 rounded-full" 
          style={{ backgroundColor: color }}
        />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-xs font-medium text-card-foreground truncate">
          {message}
        </p>
        {details && (
          <p className="text-[10px] text-muted-foreground">
            {typeof details === 'object' && 'online' in details && 'total' in details
              ? `${details.online}/${details.total} online`
              : typeof details === 'object' && 'active_connections' in details
              ? `${details.active_connections} connections`
              : ''
            }
          </p>
        )}
      </div>
    </div>
  );
};

export const MCPConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  className = '', 
  showDetails = true, 
  compact = false 
}) => {
  const { useHealthIndicators, isConnected, connectionError, location } = useMCPPrinting();
  
  const { data: indicators, error, isLoading } = useHealthIndicators();

  // If MCP server is not reachable, show disconnected state
  if (connectionError || !isConnected) {
    const disconnectedIndicators: HealthIndicators = {
      mcp_server: {
        status: 'offline',
        color: '#ef4444',
        message: 'MCP Server Offline'
      },
      printer_connectivity: {
        status: 'unknown',
        color: '#6b7280',
        message: 'Printer Status Unknown',
        details: { online: 0, total: 0 }
      },
      frontend_connection: {
        status: 'disconnected',
        color: '#ef4444',
        message: 'Disconnected from MCP',
        details: { active_connections: 0 }
      },
      overall: {
        status: 'critical',
        color: '#ef4444',
        message: 'System Critical'
      }
    };

    return (
      <div className={`space-y-2 ${className}`}>
        {compact ? (
          <div className="flex items-center gap-2">
            <Badge variant="destructive" className="text-[10px] py-0.5 px-1.5">
              MCP Offline
            </Badge>
          </div>
        ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
            <StatusIndicator
              status={disconnectedIndicators.mcp_server.status}
              color={disconnectedIndicators.mcp_server.color}
              message={disconnectedIndicators.mcp_server.message}
              icon={<Server className="h-3 w-3 text-muted-foreground" />}
            />
            <StatusIndicator
              status={disconnectedIndicators.printer_connectivity.status}
              color={disconnectedIndicators.printer_connectivity.color}
              message={disconnectedIndicators.printer_connectivity.message}
              icon={<Printer className="h-3 w-3 text-muted-foreground" />}
              details={disconnectedIndicators.printer_connectivity.details}
            />
            <StatusIndicator
              status={disconnectedIndicators.frontend_connection.status}
              color={disconnectedIndicators.frontend_connection.color}
              message={disconnectedIndicators.frontend_connection.message}
              icon={<WifiOff className="h-3 w-3 text-muted-foreground" />}
              details={disconnectedIndicators.frontend_connection.details}
            />
            <StatusIndicator
              status={disconnectedIndicators.overall.status}
              color={disconnectedIndicators.overall.color}
              message={disconnectedIndicators.overall.message}
              icon={<AlertTriangle className="h-3 w-3 text-muted-foreground" />}
            />
          </div>
        )}
        {showDetails && !compact && (
          <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
            <p>Connection Error: {connectionError || 'Unable to reach MCP server'}</p>
            <p>Location: {location}</p>
          </div>
        )}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        {compact ? (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-[10px] py-0.5 px-1.5">
              Loading...
            </Badge>
          </div>
        ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-12 bg-muted rounded-lg animate-pulse" />
            ))}
          </div>
        )}
      </div>
    );
  }

  if (error || !indicators?.success) {
    return (
      <div className={`space-y-2 ${className}`}>
        {compact ? (
          <div className="flex items-center gap-2">
            <Badge variant="destructive" className="text-[10px] py-0.5 px-1.5">
              Error
            </Badge>
          </div>
        ) : (
          <div className="text-center py-4">
            <AlertTriangle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
            <p className="text-xs text-muted-foreground">
              Unable to load connection status
            </p>
          </div>
        )}
      </div>
    );
  }

  const healthIndicators = indicators.indicators;

  if (compact) {
    const overallStatus = healthIndicators.overall.status;
    const statusColor = overallStatus === 'healthy' ? 'default' : 
                       overallStatus === 'critical' ? 'destructive' : 'secondary';
    
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant={statusColor} className="text-[10px] py-0.5 px-1.5">
          MCP {overallStatus}
        </Badge>
        <div className="flex items-center gap-1">
          <StatusIndicator
            status={healthIndicators.mcp_server.status}
            color={healthIndicators.mcp_server.color}
            message={healthIndicators.mcp_server.message}
            icon={<Server className="h-3 w-3" />}
            compact={true}
          />
          <StatusIndicator
            status={healthIndicators.printer_connectivity.status}
            color={healthIndicators.printer_connectivity.color}
            message={healthIndicators.printer_connectivity.message}
            icon={<Printer className="h-3 w-3" />}
            details={healthIndicators.printer_connectivity.details}
            compact={true}
          />
          <StatusIndicator
            status={healthIndicators.frontend_connection.status}
            color={healthIndicators.frontend_connection.color}
            message={healthIndicators.frontend_connection.message}
            icon={<Wifi className="h-3 w-3" />}
            details={healthIndicators.frontend_connection.details}
            compact={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Printing System Status</h3>
        <Badge 
          variant={healthIndicators.overall.status === 'healthy' ? 'default' : 
                  healthIndicators.overall.status === 'critical' ? 'destructive' : 'secondary'}
          className="text-xs"
        >
          {healthIndicators.overall.message}
        </Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        <StatusIndicator
          status={healthIndicators.mcp_server.status}
          color={healthIndicators.mcp_server.color}
          message={healthIndicators.mcp_server.message}
          icon={<Server className="h-4 w-4 text-muted-foreground" />}
        />
        
        <StatusIndicator
          status={healthIndicators.printer_connectivity.status}
          color={healthIndicators.printer_connectivity.color}
          message={healthIndicators.printer_connectivity.message}
          icon={<Printer className="h-4 w-4 text-muted-foreground" />}
          details={healthIndicators.printer_connectivity.details}
        />
        
        <StatusIndicator
          status={healthIndicators.frontend_connection.status}
          color={healthIndicators.frontend_connection.color}
          message={healthIndicators.frontend_connection.message}
          icon={<Wifi className="h-4 w-4 text-muted-foreground" />}
          details={healthIndicators.frontend_connection.details}
        />
        
        <StatusIndicator
          status={healthIndicators.overall.status}
          color={healthIndicators.overall.color}
          message="System Health"
          icon={
            healthIndicators.overall.status === 'healthy' ? 
            <CheckCircle className="h-4 w-4 text-green-500" /> :
            healthIndicators.overall.status === 'critical' ?
            <XCircle className="h-4 w-4 text-red-500" /> :
            <AlertTriangle className="h-4 w-4 text-amber-500" />
          }
        />
      </div>

      {showDetails && (
        <div className="text-xs text-muted-foreground space-y-1 bg-muted p-3 rounded-lg">
          <div className="flex justify-between">
            <span>Location:</span>
            <span className="font-medium">{location}</span>
          </div>
          <div className="flex justify-between">
            <span>Last Updated:</span>
            <span className="font-medium">
              {new Date(indicators.timestamp).toLocaleTimeString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Connection:</span>
            <span className={`font-medium ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

// Export for use in navigation or other components
export const MCPConnectionIndicator: React.FC<{ className?: string }> = ({ className = '' }) => {
  return <MCPConnectionStatus compact={true} showDetails={false} className={className} />;
};