import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Printer,
  Hash,
  Image,
  MessageSquare,
  Clock,
  Package,
  AlertCircle,
  Search,
  X,
  History,
  ArrowUpDown
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { QuantityProgressBar } from './QuantityProgressBar'
import { QuantityStateBadges } from './QuantityStateBadges'
import { QuickTransitionButtons } from './QuickTransitionButtons'
import { QuantityTransitionModal } from './QuantityTransitionModal'
import { CTNumberDisplay } from './CTNumberDisplay'
import { transformToQuantityData } from '@/utils/quantityTransformers'
import { useState, useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { transitionEvents } from '@/services/QuantityTransitionService'

interface OrderCardProps {
  order: OrderLineWithDetails
  onPrintLabel: () => void
  onManageCT: () => void
  onViewImages: () => void
  onProcurementQuery: () => void
  onWhatsApp: () => void
  onRequestCancel: () => void
  onViewHistory: () => void
  onDesignLabel?: () => void
}

// ETA status color mapping
const etaStatusColors = {
  rfq: 'bg-gray-100 text-gray-700',
  first: 'bg-green-100 text-green-700',
  second: 'bg-yellow-100 text-yellow-700',
  third: 'bg-red-100 text-red-700',
  final: 'bg-red-100 text-red-700',
  overdue: 'bg-red-200 text-red-800'
}

// Status badge colors
const statusColors = {
  pending: 'bg-gray-100 text-gray-700',
  qc: 'bg-blue-100 text-blue-700',
  completed: 'bg-green-100 text-green-700',
  cancelled: 'bg-red-100 text-red-700'
}

export function OrderCard({
  order,
  onPrintLabel,
  onManageCT,
  onViewImages,
  onProcurementQuery,
  onWhatsApp,
  onRequestCancel,
  onViewHistory
}: OrderCardProps) {
  const queryClient = useQueryClient()
  const [showTransitionModal, setShowTransitionModal] = useState(false)
  const [forceUpdate, setForceUpdate] = useState(0)

  // Calculate in progress and on hold quantities directly from order data
  const quantities = order.quantities
  const totalInProgress = (quantities?.in_kitting_packing || 0) + (quantities?.in_screening_qc || 0)
  const totalOnHold = (quantities?.on_hold_kitting || 0) + (quantities?.on_hold_qc || 0)

  // Listen for transition events to force immediate updates
  useEffect(() => {
    const handleTransition = (event: CustomEvent) => {
      if (event.detail.orderLineId === order.id) {
        console.log('🔄 Transition event received for order:', order.uid)
        setForceUpdate(prev => prev + 1)
        // Also invalidate queries
        queryClient.invalidateQueries({ queryKey: ['orders'] })
      }
    }

    transitionEvents.addEventListener('quantity-transition', handleTransition as EventListener)
    
    return () => {
      transitionEvents.removeEventListener('quantity-transition', handleTransition as EventListener)
    }
  }, [order.id, order.uid, queryClient])

  return (
    <Card key={`order-${order.id}-${forceUpdate}`} className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-2 pt-3">
        <div className="flex items-start justify-between">
          {/* Left side - UID, Part Number */}
          <div className="flex items-center space-x-3">
            <div className="text-base font-bold text-gray-900">
              {order.uid || 'A001'}
            </div>
            <div className="text-xs text-gray-600">
              {order.customer_part_number || 'L12345-601'}
            </div>
          </div>

          {/* Right side - Status Badge */}
          <div className="flex items-center">
            <Badge variant="secondary" className={cn(
              order.quantities?.in_screening_qc ? statusColors.qc : statusColors.pending
            )}>
              {order.quantities?.in_screening_qc ? 'QC' : 'Pending'}
            </Badge>
          </div>
        </div>

        {/* Title/Description */}
        <div className="mt-2">
          <h3 className="text-sm font-medium text-gray-900">
            {order.bpi_description || 'Laptop 840 G10 LCD'}
          </h3>
          <p className="text-xs text-gray-500 mt-0.5">
            {order.customer_description || 'EliteBook 840 G10 LCD Screen 14"'}
          </p>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 pt-2">
        {/* Key Dates and Info */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-4">
            <div>
              <span className="text-gray-500">PO Date:</span>
              <span className="ml-1 font-medium">{order.po_date || '150124'}</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-500">ETA:</span>
              <div className={cn(
                "ml-1 px-1.5 py-0.5 rounded-full text-[10px] font-medium inline-flex items-center",
                etaStatusColors[order.currentEtaStatus || 'rfq'],
                order.isBlinking && "animate-pulse"
              )}>
                <Clock className="h-2.5 w-2.5 mr-0.5" />
                {order.current_eta || '220124'}
              </div>
            </div>
          </div>
          {order.po_number && (
            <div className="text-xs text-gray-500">
              PO: {order.po_number}
            </div>
          )}
        </div>

        {/* Progressive Quantity Visualization */}
        <QuantityProgressBar order={order} showDetails={false} className="my-2" />

        {/* Quantity State Distribution Badges */}
        <QuantityStateBadges quantities={order.quantities} compact={true} className="my-2" orderLineId={order.id} />

        {/* Quick Transition Buttons */}
        <QuickTransitionButtons order={order} compact={true} className="my-2" />

        {/* CT Number Display */}
        <CTNumberDisplay orderLineId={order.id} compact={true} className="my-2" />

        {/* Quick Status Indicators */}
        <div className="flex items-center justify-end space-x-2">
          {totalInProgress > 0 && (
            <Badge variant="outline" className="text-[10px] text-orange-600 border-orange-200 py-0.5 px-1.5">
              <Package className="h-2.5 w-2.5 mr-0.5" />
              {totalInProgress} In Progress
            </Badge>
          )}
          {totalOnHold > 0 && (
            <Badge variant="outline" className="text-[10px] text-red-600 border-red-200 py-0.5 px-1.5">
              <AlertCircle className="h-2.5 w-2.5 mr-0.5" />
              {totalOnHold} On Hold
            </Badge>
          )}
        </div>

        {/* Global Pending for HP Display */}
        {order.globalPendingForHP !== undefined && order.globalPendingForHP > 0 && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-2">
            <span className="text-sm font-medium text-amber-800">
              Global Pending: {order.globalPendingForHP} left
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="grid grid-cols-4 gap-1 pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onPrintLabel}
            className="text-xs px-2"
            title="Print Label"
          >
            <Printer className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onManageCT}
            className="text-xs px-2"
            title="CT Numbers"
          >
            <Hash className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowTransitionModal(true)}
            className="text-xs px-2"
            title="Move Quantity"
          >
            <ArrowUpDown className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onViewImages}
            className="text-xs px-2"
            title="View Images"
          >
            <Image className="h-3 w-3" />
          </Button>
        </div>

        {/* Secondary Action Buttons */}
        <div className="grid grid-cols-4 gap-1">
          <Button
            size="sm"
            variant="outline"
            onClick={onWhatsApp}
            className="text-xs px-2"
            title="WhatsApp"
          >
            <MessageSquare className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onProcurementQuery}
            className="text-xs px-2"
            title="Procurement"
          >
            <Search className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onViewHistory}
            className="text-xs px-2"
            title="View History"
          >
            <History className="h-3 w-3" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onRequestCancel}
            className="text-xs px-2"
            title="Request Cancel"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>

      {/* Quantity Transition Modal */}
      <QuantityTransitionModal
        isOpen={showTransitionModal}
        onClose={() => setShowTransitionModal(false)}
        orderLineId={order.id}
        orderUid={order.uid || 'Unknown'}
        customerPartNumber={order.customer_part_number || 'Unknown'}
        quantities={transformToQuantityData(order.quantities)}
      />
    </Card>
  )
}