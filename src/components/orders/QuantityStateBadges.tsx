import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface QuantityStateBadgesProps {
  quantities: any
  className?: string
  compact?: boolean
  orderLineId?: string
}

interface StateBadge {
  key: string
  label: string
  value: number
  color: string
  bgColor: string
  borderColor: string
  priority: number
}

export function QuantityStateBadges({ quantities, className, compact = false, orderLineId }: QuantityStateBadgesProps) {
  if (!quantities) return null
  
  // Use updated_at timestamp if available for forcing re-renders
  const updateKey = quantities.updated_at || Date.now()

  // Define all possible state badges with their styling
  const stateBadges: StateBadge[] = [
    {
      key: 'pending_procurement',
      label: 'Pending',
      value: quantities.pending_procurement || 0,
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      priority: 1
    },
    {
      key: 'awaiting_kitting_packing',
      label: 'Awaiting Kit',
      value: quantities.awaiting_kitting_packing || 0,
      color: 'text-orange-700',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      priority: 2
    },
    {
      key: 'in_kitting_packing',
      label: 'In Kitting',
      value: quantities.in_kitting_packing || 0,
      color: 'text-orange-800',
      bgColor: 'bg-orange-100',
      borderColor: 'border-orange-300',
      priority: 3
    },
    {
      key: 'on_hold_kitting',
      label: 'Hold Kit',
      value: quantities.on_hold_kitting || 0,
      color: 'text-yellow-800',
      bgColor: 'bg-yellow-100',
      borderColor: 'border-yellow-300',
      priority: 10
    },
    {
      key: 'kitted_awaiting_qc',
      label: 'Awaiting QC',
      value: quantities.kitted_awaiting_qc || 0,
      color: 'text-purple-700',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      priority: 4
    },
    {
      key: 'in_screening_qc',
      label: 'In QC',
      value: quantities.in_screening_qc || 0,
      color: 'text-purple-800',
      bgColor: 'bg-purple-100',
      borderColor: 'border-purple-300',
      priority: 5
    },
    {
      key: 'on_hold_qc',
      label: 'Hold QC',
      value: quantities.on_hold_qc || 0,
      color: 'text-red-800',
      bgColor: 'bg-red-100',
      borderColor: 'border-red-300',
      priority: 11
    },
    {
      key: 'qc_passed_ready_invoice',
      label: 'Ready',
      value: quantities.qc_passed_ready_invoice || 0,
      color: 'text-green-700',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      priority: 6
    },
    {
      key: 'qc_rejected',
      label: 'Rejected',
      value: quantities.qc_rejected || 0,
      color: 'text-red-700',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      priority: 12
    },
    {
      key: 'invoiced',
      label: 'Invoiced',
      value: quantities.invoiced || 0,
      color: 'text-emerald-700',
      bgColor: 'bg-emerald-50',
      borderColor: 'border-emerald-200',
      priority: 7
    },
    {
      key: 'shipped_delivered',
      label: 'Shipped',
      value: quantities.shipped_delivered || 0,
      color: 'text-emerald-800',
      bgColor: 'bg-emerald-100',
      borderColor: 'border-emerald-300',
      priority: 8
    },
    {
      key: 'cancelled',
      label: 'Cancelled',
      value: quantities.cancelled || 0,
      color: 'text-gray-700',
      bgColor: 'bg-gray-100',
      borderColor: 'border-gray-300',
      priority: 9
    }
  ]

  // Filter out badges with zero values and sort by priority
  const activeBadges = stateBadges
    .filter(badge => badge.value > 0)
    .sort((a, b) => a.priority - b.priority)

  if (activeBadges.length === 0) return null

  // In compact mode, show only the most important badges
  const badgesToShow = compact ? activeBadges.slice(0, 4) : activeBadges

  return (
    <div key={`badges-${orderLineId}-${updateKey}`} className={cn("flex flex-wrap gap-1", className)}>
      {badgesToShow.map((badge) => (
        <Badge
          key={`${badge.key}-${updateKey}`}
          variant="outline"
          className={cn(
            "text-[10px] px-1.5 py-0.5",
            badge.color,
            badge.bgColor,
            badge.borderColor,
            "border",
            "transition-all duration-200"
          )}
        >
          <span className="font-medium">{badge.value}</span>
          <span className="ml-1">{badge.label}</span>
        </Badge>
      ))}
      {compact && activeBadges.length > 4 && (
        <Badge
          variant="outline"
          className="text-[10px] px-1.5 py-0.5 text-gray-600 bg-gray-50 border-gray-200"
        >
          +{activeBadges.length - 4} more
        </Badge>
      )}
    </div>
  )
}

// Enhanced version with category grouping
export function QuantityStateBadgesGrouped({ quantities, className }: QuantityStateBadgesProps) {
  if (!quantities) return null

  const procurementTotal = (quantities.pending_procurement || 0) + (quantities.requested_from_stock || 0)
  const kittingTotal = (quantities.awaiting_kitting_packing || 0) + (quantities.in_kitting_packing || 0)
  const qcTotal = (quantities.kitted_awaiting_qc || 0) + (quantities.in_screening_qc || 0)
  const readyTotal = quantities.qc_passed_ready_invoice || 0
  const holdTotal = (quantities.on_hold_kitting || 0) + (quantities.on_hold_qc || 0)
  const completedTotal = (quantities.invoiced || 0) + (quantities.shipped_delivered || 0)

  const groups = [
    { label: 'Pending', value: procurementTotal, color: 'blue' },
    { label: 'Kitting', value: kittingTotal, color: 'orange' },
    { label: 'QC', value: qcTotal, color: 'purple' },
    { label: 'Ready', value: readyTotal, color: 'green' },
    { label: 'Hold', value: holdTotal, color: 'red' },
    { label: 'Complete', value: completedTotal, color: 'emerald' }
  ].filter(g => g.value > 0)

  if (groups.length === 0) return null

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {groups.map((group) => (
        <Badge
          key={group.label}
          variant="outline"
          className={cn(
            "text-[10px] px-1.5 py-0.5",
            `text-${group.color}-700`,
            `bg-${group.color}-50`,
            `border-${group.color}-200`,
            "border"
          )}
        >
          <span className="font-medium">{group.value}</span>
          <span className="ml-1">{group.label}</span>
        </Badge>
      ))}
    </div>
  )
}