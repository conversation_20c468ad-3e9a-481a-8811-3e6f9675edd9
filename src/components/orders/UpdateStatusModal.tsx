import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowUpDown, 
  Package, 
  AlertCircle, 
  CheckCircle2,
  Loader2,
  Clock,
  User
} from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { useQuantityTracking } from '@/hooks/useQuantityTracking'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'

interface UpdateStatusModalProps {
  isOpen: boolean
  onClose: () => void
  order: OrderLineWithDetails
  onStatusUpdated?: (orderUid: string, newStatus: string) => void
}

// Define the workflow stages in order
const WORKFLOW_STAGES = [
  { id: 'pending_procurement', label: 'Pending Procurement', color: 'bg-gray-100 text-gray-700' },
  { id: 'in_kitting', label: 'In Kitting/Packing', color: 'bg-blue-100 text-blue-700' },
  { id: 'kitted_awaiting_qc', label: 'Awaiting QC', color: 'bg-yellow-100 text-yellow-700' },
  { id: 'in_qc', label: 'In QC', color: 'bg-orange-100 text-orange-700' },
  { id: 'qc_passed', label: 'QC Passed - Ready for Invoice', color: 'bg-green-100 text-green-700' },
  { id: 'invoiced', label: 'Invoiced', color: 'bg-purple-100 text-purple-700' },
  { id: 'shipped', label: 'Shipped/Delivered', color: 'bg-emerald-100 text-emerald-700' }
] as const

export function UpdateStatusModal({ 
  isOpen, 
  onClose, 
  order, 
  onStatusUpdated 
}: UpdateStatusModalProps) {
  const [selectedStage, setSelectedStage] = useState<string>('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [note, setNote] = useState('')
  
  const { user } = useAuth()
  const { quantities, isLoading } = useQuantityTracking(order.id)

  // Determine current stage based on quantities
  const getCurrentStage = () => {
    if (!quantities) return 'pending_procurement'
    
    if (quantities.shippedDelivered > 0) return 'shipped'
    if (quantities.invoiced > 0) return 'invoiced'
    if (quantities.screeningQcPassedReadyForInvoice > 0) return 'qc_passed'
    if (quantities.inScreeningQc > 0) return 'in_qc'
    if (quantities.kittedPackedAwaitingScreeningQc > 0) return 'kitted_awaiting_qc'
    if (quantities.inKittingPacking > 0) return 'in_kitting'
    
    return 'pending_procurement'
  }

  const currentStage = getCurrentStage()
  const currentStageIndex = WORKFLOW_STAGES.findIndex(stage => stage.id === currentStage)

  // Reset when modal opens
  useState(() => {
    if (isOpen) {
      setSelectedStage('')
      setNote('')
      setIsUpdating(false)
    }
  }, [isOpen])

  const handleUpdateStatus = async () => {
    if (!selectedStage || !user?.id) return

    setIsUpdating(true)
    
    try {
      // TODO: Implement actual status update logic
      // This would typically call a hook like useUpdateOrderStatus
      console.log('🔄 Updating order status:', {
        orderUid: order.uid,
        fromStage: currentStage,
        toStage: selectedStage,
        note,
        updatedBy: user.id
      })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      console.log('✅ Order status updated successfully')
      
      // Notify parent component
      onStatusUpdated?.(order.uid || 'Unknown', selectedStage)
      
      // Close modal
      onClose()
      
    } catch (error) {
      console.error('❌ Failed to update order status:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'pending_procurement': return <Clock className="h-4 w-4" />
      case 'in_kitting': return <Package className="h-4 w-4" />
      case 'kitted_awaiting_qc': return <ArrowUpDown className="h-4 w-4" />
      case 'in_qc': return <AlertCircle className="h-4 w-4" />
      case 'qc_passed': return <CheckCircle2 className="h-4 w-4" />
      case 'invoiced': return <Package className="h-4 w-4" />
      case 'shipped': return <CheckCircle2 className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600">Loading order status...</span>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <ArrowUpDown className="h-5 w-5" />
            <span>Update Order Status - {order.uid}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Part Number:</span> {order.customer_part_number}
              </div>
              <div>
                <span className="font-medium">Order Quantity:</span> {order.order_quantity}
              </div>
              <div className="col-span-2">
                <span className="font-medium">Description:</span> {order.bpi_description}
              </div>
            </div>
          </div>

          {/* Current Status */}
          <div className="space-y-3">
            <Label>Current Status</Label>
            <div className="flex items-center space-x-3">
              {getStageIcon(currentStage)}
              <Badge className={cn(
                WORKFLOW_STAGES.find(s => s.id === currentStage)?.color || 'bg-gray-100 text-gray-700'
              )}>
                {WORKFLOW_STAGES.find(s => s.id === currentStage)?.label || 'Unknown'}
              </Badge>
              <span className="text-sm text-gray-600">
                Step {currentStageIndex + 1} of {WORKFLOW_STAGES.length}
              </span>
            </div>
          </div>

          {/* Available Status Updates */}
          <div className="space-y-3">
            <Label>Update to New Status</Label>
            <div className="grid grid-cols-1 gap-2">
              {WORKFLOW_STAGES.map((stage, index) => {
                const isCurrent = stage.id === currentStage
                const isPast = index < currentStageIndex
                const isNext = index === currentStageIndex + 1
                const isFuture = index > currentStageIndex + 1
                
                return (
                  <div
                    key={stage.id}
                    className={cn(
                      "flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors",
                      isCurrent && "border-blue-300 bg-blue-50",
                      isPast && "border-gray-200 bg-gray-50 opacity-60",
                      isNext && "border-green-300 bg-green-50 hover:bg-green-100",
                      isFuture && "border-gray-200 bg-gray-50 opacity-40",
                      selectedStage === stage.id && "border-blue-500 bg-blue-100"
                    )}
                    onClick={() => {
                      if (!isPast && !isCurrent) {
                        setSelectedStage(stage.id)
                      }
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      {getStageIcon(stage.id)}
                      <div>
                        <div className="font-medium">{stage.label}</div>
                        <div className="text-xs text-gray-600">
                          {isCurrent && "Current Status"}
                          {isPast && "Already Completed"}
                          {isNext && "Next Logical Step"}
                          {isFuture && `Skip ${index - currentStageIndex} steps`}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {isCurrent && (
                        <Badge variant="outline" className="text-xs">Current</Badge>
                      )}
                      {isPast && (
                        <CheckCircle2 className="h-4 w-4 text-gray-400" />
                      )}
                      {isNext && (
                        <Badge variant="outline" className="text-xs bg-green-100 text-green-700">
                          Recommended
                        </Badge>
                      )}
                      {selectedStage === stage.id && (
                        <div className="h-4 w-4 rounded-full bg-blue-600" />
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Note Section */}
          {selectedStage && (
            <div className="space-y-3">
              <Label htmlFor="status-note">Status Update Note (Optional)</Label>
              <textarea
                id="status-note"
                placeholder="Add a note about this status change..."
                value={note}
                onChange={(e) => setNote(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
                rows={3}
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500 flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Updated by: {user?.email || 'Unknown'}</span>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleUpdateStatus}
                disabled={!selectedStage || isUpdating}
                className={cn(
                  selectedStage && "bg-blue-600 hover:bg-blue-700"
                )}
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    Update Status
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}