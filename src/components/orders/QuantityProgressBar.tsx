import { useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { OrderLineWithDetails } from '@/hooks/useOrders'

interface QuantityProgressBarProps {
  order: OrderLineWithDetails
  showDetails?: boolean
  className?: string
}

interface QuantityStage {
  name: string
  value: number
  color: string
  bgColor: string
  category: 'procurement' | 'kitting' | 'qc' | 'final'
  displayName: string
}

export function QuantityProgressBar({ order, showDetails = false, className }: QuantityProgressBarProps) {
  const quantities = order.quantities
  
  const stages: QuantityStage[] = useMemo(() => {
    if (!quantities) return []

    return [
      {
        name: 'pending_procurement',
        value: quantities.pending_procurement || 0,
        color: 'text-blue-700',
        bgColor: 'bg-blue-500',
        category: 'procurement',
        displayName: 'Pending Procurement'
      },
      {
        name: 'requested_from_stock',
        value: quantities.requested_from_stock || 0,
        color: 'text-blue-600',
        bgColor: 'bg-blue-400',
        category: 'procurement',
        displayName: 'From Stock'
      },
      {
        name: 'awaiting_kitting_packing',
        value: quantities.awaiting_kitting_packing || 0,
        color: 'text-orange-700',
        bgColor: 'bg-orange-500',
        category: 'kitting',
        displayName: 'Awaiting Kitting'
      },
      {
        name: 'in_kitting_packing',
        value: quantities.in_kitting_packing || 0,
        color: 'text-orange-800',
        bgColor: 'bg-orange-600',
        category: 'kitting',
        displayName: 'In Kitting'
      },
      {
        name: 'on_hold_kitting',
        value: quantities.on_hold_kitting || 0,
        color: 'text-yellow-700',
        bgColor: 'bg-yellow-500',
        category: 'kitting',
        displayName: 'Hold - Kitting'
      },
      {
        name: 'kitted_awaiting_qc',
        value: quantities.kitted_awaiting_qc || 0,
        color: 'text-purple-700',
        bgColor: 'bg-purple-500',
        category: 'qc',
        displayName: 'Awaiting QC'
      },
      {
        name: 'in_screening_qc',
        value: quantities.in_screening_qc || 0,
        color: 'text-purple-800',
        bgColor: 'bg-purple-600',
        category: 'qc',
        displayName: 'In QC'
      },
      {
        name: 'on_hold_qc',
        value: quantities.on_hold_qc || 0,
        color: 'text-red-700',
        bgColor: 'bg-red-500',
        category: 'qc',
        displayName: 'Hold - QC'
      },
      {
        name: 'qc_passed_ready_invoice',
        value: quantities.qc_passed_ready_invoice || 0,
        color: 'text-green-700',
        bgColor: 'bg-green-500',
        category: 'final',
        displayName: 'Ready Invoice'
      },
      {
        name: 'qc_rejected',
        value: quantities.qc_rejected || 0,
        color: 'text-red-800',
        bgColor: 'bg-red-600',
        category: 'final',
        displayName: 'QC Rejected'
      },
      {
        name: 'invoiced',
        value: quantities.invoiced || 0,
        color: 'text-emerald-700',
        bgColor: 'bg-emerald-500',
        category: 'final',
        displayName: 'Invoiced'
      },
      {
        name: 'shipped_delivered',
        value: quantities.shipped_delivered || 0,
        color: 'text-emerald-800',
        bgColor: 'bg-emerald-600',
        category: 'final',
        displayName: 'Delivered'
      },
      {
        name: 'cancelled',
        value: quantities.cancelled || 0,
        color: 'text-gray-700',
        bgColor: 'bg-gray-500',
        category: 'final',
        displayName: 'Cancelled'
      }
    ].filter(stage => stage.value > 0)
  }, [quantities])

  const totalQuantity = quantities?.total_order_quantity || order.order_quantity || 0
  const activeStages = stages.filter(stage => stage.value > 0)
  
  // Calculate completion percentage
  const completedQuantity = (quantities?.shipped_delivered || 0) + (quantities?.cancelled || 0)
  const completionPercentage = totalQuantity > 0 ? (completedQuantity / totalQuantity) * 100 : 0

  // Get current stage info - prioritize pending states over highest quantity
  const getCurrentStageInfo = () => {
    // Priority order: pending states first, then active work states
    const pendingProcurement = quantities?.pending_procurement || 0
    const awaitingKitting = quantities?.awaiting_kitting_packing || 0
    const inKitting = quantities?.in_kitting_packing || 0
    const awaitingQC = quantities?.kitted_awaiting_qc || 0
    const inQC = quantities?.in_screening_qc || 0
    const readyInvoice = quantities?.qc_passed_ready_invoice || 0

    // Show pending procurement if it exists
    if (pendingProcurement > 0) {
      return {
        name: 'pending_procurement',
        value: pendingProcurement,
        color: 'text-blue-700',
        displayName: 'Pending Procurement'
      }
    }

    // Show awaiting kitting if it exists
    if (awaitingKitting > 0) {
      return {
        name: 'awaiting_kitting_packing',
        value: awaitingKitting,
        color: 'text-orange-700',
        displayName: 'Awaiting Kitting'
      }
    }

    // Show in kitting if it exists
    if (inKitting > 0) {
      return {
        name: 'in_kitting_packing',
        value: inKitting,
        color: 'text-orange-800',
        displayName: 'In Kitting'
      }
    }

    // Show awaiting QC if it exists
    if (awaitingQC > 0) {
      return {
        name: 'kitted_awaiting_qc',
        value: awaitingQC,
        color: 'text-purple-700',
        displayName: 'Awaiting QC'
      }
    }

    // Show in QC if it exists
    if (inQC > 0) {
      return {
        name: 'in_screening_qc',
        value: inQC,
        color: 'text-purple-800',
        displayName: 'In QC'
      }
    }

    // Show ready for invoice if it exists
    if (readyInvoice > 0) {
      return {
        name: 'qc_passed_ready_invoice',
        value: readyInvoice,
        color: 'text-green-700',
        displayName: 'Ready Invoice'
      }
    }

    // Fallback to highest quantity stage
    return activeStages.reduce((max, current) =>
      current.value > max.value ? current : max, activeStages[0]
    )
  }

  const currentStage = getCurrentStageInfo()

  if (!quantities || totalQuantity === 0) {
    return (
      <div className={cn("text-xs text-gray-500", className)}>
        No quantity data
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
        {activeStages.map((stage, index) => {
          const width = (stage.value / totalQuantity) * 100
          return (
            <div
              key={stage.name}
              className={cn(
                stage.bgColor,
                "h-full inline-block transition-all duration-300"
              )}
              style={{ width: `${width}%` }}
              title={`${stage.displayName}: ${stage.value} units`}
            />
          )
        })}
      </div>

      {/* Summary Info */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center space-x-2">
          {currentStage && (
            <Badge
              variant="outline"
              className={cn("text-xs px-1.5 py-0.5", currentStage.color)}
            >
              {currentStage.displayName}: {currentStage.value}
            </Badge>
          )}
          {completionPercentage > 0 && (
            <span className="text-green-600 font-medium">
              {completionPercentage.toFixed(0)}% Complete
            </span>
          )}
        </div>
        <span className="text-gray-600">
          {totalQuantity} total
        </span>
      </div>

      {/* Detailed Breakdown */}
      {showDetails && activeStages.length > 0 && (
        <div className="space-y-1">
          <h4 className="text-xs font-medium text-gray-700">Quantity Breakdown:</h4>
          <div className="grid grid-cols-2 gap-1 text-xs">
            {activeStages.map((stage) => (
              <div key={stage.name} className="flex items-center justify-between">
                <span className={cn("truncate", stage.color)}>
                  {stage.displayName}
                </span>
                <span className="font-medium ml-1">
                  {stage.value}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Hold/Issue Alerts */}
      {((quantities.on_hold_kitting || 0) > 0 || (quantities.on_hold_qc || 0) > 0 || (quantities.qc_rejected || 0) > 0) && (
        <div className="flex items-center space-x-2 text-xs">
          {(quantities.on_hold_kitting || 0) > 0 && (
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
              🔒 {quantities.on_hold_kitting} On Hold (Kitting)
            </Badge>
          )}
          {(quantities.on_hold_qc || 0) > 0 && (
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">
              🔒 {quantities.on_hold_qc} On Hold (QC)
            </Badge>
          )}
          {(quantities.qc_rejected || 0) > 0 && (
            <Badge variant="outline" className="bg-red-50 text-red-800 border-red-400">
              ❌ {quantities.qc_rejected} Rejected
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}

// Helper hook for quantity stage analysis
export function useQuantityAnalysis(order: OrderLineWithDetails) {
  return useMemo(() => {
    const quantities = order.quantities
    if (!quantities) return null

    const totalQuantity = quantities.total_order_quantity || order.order_quantity || 0
    const inProgress = (quantities.in_kitting_packing || 0) + (quantities.in_screening_qc || 0)
    const onHold = (quantities.on_hold_kitting || 0) + (quantities.on_hold_qc || 0)
    const completed = (quantities.shipped_delivered || 0) + (quantities.cancelled || 0)
    const readyForNextStage = (quantities.awaiting_kitting_packing || 0) + (quantities.kitted_awaiting_qc || 0) + (quantities.qc_passed_ready_invoice || 0)

    return {
      totalQuantity,
      inProgress,
      onHold,
      completed,
      readyForNextStage,
      completionPercentage: totalQuantity > 0 ? (completed / totalQuantity) * 100 : 0,
      hasIssues: onHold > 0 || (quantities.qc_rejected || 0) > 0,
      currentStage: getCurrentStage(quantities),
      nextAction: getNextAction(quantities)
    }
  }, [order])
}

function getCurrentStage(quantities: any): string {
  if (quantities.in_kitting_packing > 0) return 'In Kitting/Packing'
  if (quantities.in_screening_qc > 0) return 'In Quality Control'
  if (quantities.awaiting_kitting_packing > 0) return 'Awaiting Kitting'
  if (quantities.kitted_awaiting_qc > 0) return 'Awaiting QC'
  if (quantities.qc_passed_ready_invoice > 0) return 'Ready for Invoice'
  if (quantities.invoiced > 0) return 'Invoiced'
  if (quantities.shipped_delivered > 0) return 'Completed'
  if (quantities.pending_procurement > 0) return 'Pending Procurement'
  return 'Unknown'
}

function getNextAction(quantities: any): string {
  if (quantities.awaiting_kitting_packing > 0) return 'Start Kitting'
  if (quantities.kitted_awaiting_qc > 0) return 'Start QC'
  if (quantities.qc_passed_ready_invoice > 0) return 'Create Invoice'
  if (quantities.on_hold_kitting > 0) return 'Resolve Kitting Hold'
  if (quantities.on_hold_qc > 0) return 'Resolve QC Hold'
  if (quantities.qc_rejected > 0) return 'Review Rejection'
  if (quantities.invoiced > 0) return 'Prepare Shipment'
  return 'No Action Required'
}