import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { QuantityData, QuantityState } from '@/hooks/useQuantityTracking'

interface ProgressiveQuantityBarProps {
  quantities: QuantityData
  className?: string
  showLabels?: boolean
  compact?: boolean
}

interface QuantitySegment {
  state: QuantityState
  label: string
  value: number
  color: string
  category: 'procurement' | 'kitting' | 'qc' | 'final'
}

export function ProgressiveQuantityBar({ 
  quantities, 
  className, 
  showLabels = true,
  compact = false 
}: ProgressiveQuantityBarProps) {
  // Define the quantity segments in workflow order
  const segments: QuantitySegment[] = [
    {
      state: 'pending_procurement_arrangement',
      label: 'Pending Procurement',
      value: quantities.pendingProcurementArrangement,
      color: 'bg-slate-400',
      category: 'procurement'
    },
    {
      state: 'requested_from_stock',
      label: 'Requested from Stock',
      value: quantities.requestedFromStock,
      color: 'bg-blue-400',
      category: 'procurement'
    },
    {
      state: 'awaiting_kitting_packing',
      label: 'Awaiting Kitting',
      value: quantities.awaitingKittingPacking,
      color: 'bg-yellow-400',
      category: 'kitting'
    },
    {
      state: 'in_kitting_packing',
      label: 'In Kitting',
      value: quantities.inKittingPacking,
      color: 'bg-orange-500',
      category: 'kitting'
    },
    {
      state: 'on_hold_at_kitting_packing',
      label: 'Hold - Kitting',
      value: quantities.onHoldAtKittingPacking,
      color: 'bg-red-400',
      category: 'kitting'
    },
    {
      state: 'kitted_packed_awaiting_screening_qc',
      label: 'Awaiting QC',
      value: quantities.kittedPackedAwaitingScreeningQc,
      color: 'bg-purple-400',
      category: 'qc'
    },
    {
      state: 'in_screening_qc',
      label: 'In QC',
      value: quantities.inScreeningQc,
      color: 'bg-purple-600',
      category: 'qc'
    },
    {
      state: 'on_hold_at_screening_qc',
      label: 'Hold - QC',
      value: quantities.onHoldAtScreeningQc,
      color: 'bg-red-500',
      category: 'qc'
    },
    {
      state: 'screening_qc_passed_ready_for_invoice',
      label: 'Ready for Invoice',
      value: quantities.screeningQcPassedReadyForInvoice,
      color: 'bg-green-500',
      category: 'final'
    },
    {
      state: 'screening_qc_rejected',
      label: 'QC Rejected',
      value: quantities.screeningQcRejected,
      color: 'bg-red-600',
      category: 'final'
    },
    {
      state: 'invoiced',
      label: 'Invoiced',
      value: quantities.invoiced,
      color: 'bg-blue-600',
      category: 'final'
    },
    {
      state: 'shipped_delivered',
      label: 'Shipped',
      value: quantities.shippedDelivered,
      color: 'bg-green-700',
      category: 'final'
    },
    {
      state: 'cancelled',
      label: 'Cancelled',
      value: quantities.cancelled,
      color: 'bg-gray-500',
      category: 'final'
    }
  ]

  // Filter segments that have non-zero values
  const activeSegments = segments.filter(segment => segment.value > 0)
  const totalQuantity = quantities.totalOrderQuantity

  // Calculate completion percentage
  const completedQuantity = quantities.shippedDelivered + quantities.cancelled
  const completionPercentage = totalQuantity > 0 ? Math.round((completedQuantity / totalQuantity) * 100) : 0

  if (compact) {
    return (
      <div className={cn("space-y-1", className)}>
        {/* Compact Progress Bar */}
        <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
          {activeSegments.map((segment) => {
            const widthPercentage = totalQuantity > 0 ? (segment.value / totalQuantity) * 100 : 0
            return (
              <div
                key={segment.state}
                className={cn(segment.color, "transition-all duration-300")}
                style={{ width: `${widthPercentage}%` }}
                title={`${segment.label}: ${segment.value}`}
              />
            )
          })}
        </div>
        
        {/* Compact Summary */}
        <div className="flex justify-between items-center text-xs text-gray-600">
          <span>{completedQuantity}/{totalQuantity} completed</span>
          <span>{completionPercentage}%</span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center text-sm">
          <span className="font-medium">Progress</span>
          <span className="text-gray-600">{completedQuantity}/{totalQuantity} ({completionPercentage}%)</span>
        </div>
        
        <div className="flex h-3 bg-gray-200 rounded-full overflow-hidden">
          {activeSegments.map((segment) => {
            const widthPercentage = totalQuantity > 0 ? (segment.value / totalQuantity) * 100 : 0
            return (
              <div
                key={segment.state}
                className={cn(segment.color, "transition-all duration-300 hover:opacity-80")}
                style={{ width: `${widthPercentage}%` }}
                title={`${segment.label}: ${segment.value} items`}
              />
            )
          })}
        </div>
      </div>

      {showLabels && (
        <div className="space-y-2">
          {/* Category Groups */}
          {['procurement', 'kitting', 'qc', 'final'].map(category => {
            const categorySegments = activeSegments.filter(s => s.category === category)
            if (categorySegments.length === 0) return null

            return (
              <div key={category} className="space-y-1">
                <div className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                  {category}
                </div>
                <div className="flex flex-wrap gap-1">
                  {categorySegments.map(segment => (
                    <Badge 
                      key={segment.state}
                      variant="secondary"
                      className={cn(
                        "text-xs px-2 py-1 text-white border-0",
                        segment.color
                      )}
                    >
                      {segment.label}: {segment.value}
                    </Badge>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Summary component for quick status overview
export function QuantitySummary({ quantities }: { quantities: QuantityData }) {
  const inProgress = quantities.inKittingPacking + quantities.inScreeningQc
  const onHold = quantities.onHoldAtKittingPacking + quantities.onHoldAtScreeningQc
  const completed = quantities.shippedDelivered + quantities.cancelled
  const pending = quantities.pendingProcurementArrangement + quantities.requestedFromStock + quantities.awaitingKittingPacking

  return (
    <div className="grid grid-cols-2 gap-2 text-xs">
      <div className="flex justify-between">
        <span className="text-gray-600">Pending:</span>
        <span className="font-medium">{pending}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">In Progress:</span>
        <span className="font-medium text-orange-600">{inProgress}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">On Hold:</span>
        <span className="font-medium text-red-600">{onHold}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">Completed:</span>
        <span className="font-medium text-green-600">{completed}</span>
      </div>
    </div>
  )
}

// Stage indicator for current workflow stage
export function WorkflowStageIndicator({ quantities }: { quantities: QuantityData }) {
  // Determine the primary stage based on where most quantity is
  const stages = [
    { name: 'Procurement', value: quantities.pendingProcurementArrangement + quantities.requestedFromStock, color: 'bg-blue-500' },
    { name: 'Kitting', value: quantities.awaitingKittingPacking + quantities.inKittingPacking + quantities.onHoldAtKittingPacking, color: 'bg-orange-500' },
    { name: 'QC', value: quantities.kittedPackedAwaitingScreeningQc + quantities.inScreeningQc + quantities.onHoldAtScreeningQc, color: 'bg-purple-500' },
    { name: 'Final', value: quantities.screeningQcPassedReadyForInvoice + quantities.invoiced + quantities.shippedDelivered + quantities.cancelled, color: 'bg-green-500' }
  ]

  const primaryStage = stages.reduce((max, stage) => stage.value > max.value ? stage : max, stages[0])

  return (
    <div className="flex items-center space-x-2">
      <div className={cn("w-2 h-2 rounded-full", primaryStage.color)} />
      <span className="text-xs font-medium text-gray-700">{primaryStage.name}</span>
      <span className="text-xs text-gray-500">({primaryStage.value} items)</span>
    </div>
  )
}