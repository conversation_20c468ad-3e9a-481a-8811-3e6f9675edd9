import { useState, useMemo, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { 
  PlayCircle, 
  CheckCircle2, 
  Package, 
  Truck,
  ArrowRight,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { useQuantityTracking } from '@/hooks/useQuantityTracking'
import { toast } from 'sonner'

interface QuickTransitionButtonsProps {
  order: OrderLineWithDetails
  className?: string
  compact?: boolean
}

interface QuickTransition {
  label: string
  icon: React.ReactNode
  fromState: string
  toState: string
  available: boolean
  quantity: number
  color: string
}

export function QuickTransitionButtons({ order, className, compact = false }: QuickTransitionButtonsProps) {
  const [loadingTransition, setLoadingTransition] = useState<string | null>(null)
  const { transitionQuantity } = useQuantityTracking()
  
  const quantities = order.quantities
  if (!quantities) return null

  // Determine available quick transitions based on current quantities
  const quickTransitions = useMemo((): QuickTransition[] => {
    const transitions: QuickTransition[] = []

    // Start Kitting
    if (quantities.awaiting_kitting_packing > 0) {
      transitions.push({
        label: 'Start Kitting',
        icon: <PlayCircle className="h-3.5 w-3.5" />,
        fromState: 'awaiting_kitting_packing',
        toState: 'in_kitting_packing',
        available: true,
        quantity: Math.min(quantities.awaiting_kitting_packing, 5), // Max 5 at a time
        color: 'bg-orange-600 hover:bg-orange-700 text-white'
      })
    }

    // Complete Kitting (Move to QC)
    if (quantities.in_kitting_packing > 0) {
      transitions.push({
        label: 'Move to QC',
        icon: <CheckCircle2 className="h-3.5 w-3.5" />,
        fromState: 'in_kitting_packing',
        toState: 'kitted_packed_awaiting_screening_qc',
        available: true,
        quantity: quantities.in_kitting_packing,
        color: 'bg-purple-600 hover:bg-purple-700 text-white'
      })
    }

    // Start QC
    if (quantities.kitted_awaiting_qc > 0) {
      transitions.push({
        label: 'Start QC',
        icon: <PlayCircle className="h-3.5 w-3.5" />,
        fromState: 'kitted_packed_awaiting_screening_qc',
        toState: 'in_screening_qc',
        available: true,
        quantity: Math.min(quantities.kitted_awaiting_qc, 5), // Max 5 at a time
        color: 'bg-purple-600 hover:bg-purple-700 text-white'
      })
    }

    // Pass QC (Ready for Invoice)
    if (quantities.in_screening_qc > 0) {
      transitions.push({
        label: 'Ready for Invoice',
        icon: <CheckCircle2 className="h-3.5 w-3.5" />,
        fromState: 'in_screening_qc',
        toState: 'screening_qc_passed_ready_for_invoice',
        available: true,
        quantity: quantities.in_screening_qc,
        color: 'bg-green-600 hover:bg-green-700 text-white'
      })
    }

    // Create Invoice
    if (quantities.qc_passed_ready_invoice > 0) {
      transitions.push({
        label: 'Create Invoice',
        icon: <Package className="h-3.5 w-3.5" />,
        fromState: 'screening_qc_passed_ready_for_invoice',
        toState: 'invoiced',
        available: true,
        quantity: quantities.qc_passed_ready_invoice,
        color: 'bg-emerald-600 hover:bg-emerald-700 text-white'
      })
    }

    // Ship Order
    if (quantities.invoiced > 0) {
      transitions.push({
        label: 'Ship',
        icon: <Truck className="h-3.5 w-3.5" />,
        fromState: 'invoiced',
        toState: 'shipped_delivered',
        available: true,
        quantity: quantities.invoiced,
        color: 'bg-emerald-700 hover:bg-emerald-800 text-white'
      })
    }

    return transitions
  }, [quantities])

  const handleQuickTransition = useCallback(async (transition: QuickTransition) => {
    if (!order.id) {
      toast.error('Order ID not found')
      return
    }

    const transitionKey = `${transition.fromState}-${transition.toState}`
    setLoadingTransition(transitionKey)

    try {
      const result = await transitionQuantity({
        orderLineId: order.id,
        fromState: transition.fromState,
        toState: transition.toState,
        quantity: transition.quantity,
        reason: `Quick transition: ${transition.label}`
      })

      if (result.success) {
        toast.success(`${transition.label} - ${transition.quantity} units moved`)
      } else {
        toast.error(result.message || 'Transition failed')
      }
    } catch (error) {
      console.error('Quick transition error:', error)
      toast.error('Failed to perform transition')
    } finally {
      setLoadingTransition(null)
    }
  }, [order.id, transitionQuantity])

  if (quickTransitions.length === 0) {
    return null
  }

  // In compact mode, show only the most important transition
  const transitionsToShow = compact ? quickTransitions.slice(0, 1) : quickTransitions

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {transitionsToShow.map((transition, index) => {
        const transitionKey = `${transition.fromState}-${transition.toState}`
        const isLoading = loadingTransition === transitionKey
        
        return (
          <Button
            key={index}
            size="sm"
            onClick={() => handleQuickTransition(transition)}
            disabled={!transition.available || isLoading}
            className={cn(
              "text-xs px-2 py-1",
              transition.color
            )}
            title={`${transition.label} (${transition.quantity} units)`}
          >
            {isLoading ? (
              <Loader2 className="h-3.5 w-3.5 animate-spin" />
            ) : (
              transition.icon
            )}
            {!compact && (
              <span className="ml-1">{transition.label}</span>
            )}
          </Button>
        )
      })}
      
      {compact && quickTransitions.length > 1 && (
        <Button
          size="sm"
          variant="outline"
          className="text-xs px-2 py-1"
          title="More transitions available"
          disabled
        >
          <ArrowRight className="h-3.5 w-3.5" />
        </Button>
      )}
    </div>
  )
}