import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Card, CardHeader, CardTitle } from '@/components/ui/card'
import { Printer, Loader2, Eye, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'
import { QUICK_PRINT_TEMPLATES } from '@/types/labelDesigner'
import { cn } from '@/lib/utils'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { usePrinterConfig } from '@/hooks/usePrinterConfig'
import { generateQuickPrintZPL, downloadZPLFile, generateLabelaryPreviewUrl, LABEL_TEMPLATES } from '@/utils/labelGeneration'

interface QuickPrintModalProps {
  isOpen: boolean
  onClose: () => void
}

export function QuickPrintModal({ isOpen, onClose }: QuickPrintModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [quantity, setQuantity] = useState('1')
  const [selectedPrinter, setSelectedPrinter] = useState('')
  const [isPrinting, setIsPrinting] = useState(false)
  const [isGeneratingZPL, setIsGeneratingZPL] = useState(false)

  // MCP Printing hooks
  const { 
    useZebraPrinters, 
    printZebraLabel, 
    isConnected: isMCPConnected 
  } = useMCPPrinting()
  
  const { getActivePrinters, getDefaultPrinter } = usePrinterConfig()

  // Available printers (from MCP discovery and configuration fallback)
  const { data: mcpZebraPrinters = [] } = useZebraPrinters()
  const configPrinters = getActivePrinters()
  const defaultPrinter = getDefaultPrinter()
  
  // Combine MCP discovered printers with configured printers
  const availablePrinters = mcpZebraPrinters.length > 0 ? mcpZebraPrinters : configPrinters
  const defaultPrinterId = mcpZebraPrinters.length > 0 
    ? mcpZebraPrinters[0]?.id 
    : defaultPrinter?.id

  // Reset when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedTemplate('')
      setQuantity('1')
      setSelectedPrinter(defaultPrinterId || '')
      setIsPrinting(false)
      setIsGeneratingZPL(false)
    }
  }, [isOpen, defaultPrinterId])

  const handlePreview = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a label template')
      return
    }

    setIsGeneratingZPL(true)
    
    try {
      // Generate sample ZPL for preview
      const qty = parseInt(quantity)
      const zplContent = await generateQuickPrintZPL(selectedTemplate, qty)
      const template = LABEL_TEMPLATES[selectedTemplate] || LABEL_TEMPLATES['quick-generic']
      
      // Open Labelary.com preview in new tab
      const labelaryUrl = generateLabelaryPreviewUrl(zplContent, template)
      window.open(labelaryUrl, '_blank')
      
    } catch (error) {
      console.error('Failed to generate preview:', error)
      toast.error('Failed to generate preview')
    } finally {
      setIsGeneratingZPL(false)
    }
  }

  const handlePrint = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a label template')
      return
    }

    if (!selectedPrinter) {
      toast.error('Please select a printer')
      return
    }

    const qty = parseInt(quantity)
    if (isNaN(qty) || qty < 1) {
      toast.error('Please enter a valid quantity')
      return
    }

    setIsPrinting(true)
    
    try {
      // Generate ZPL for the quick print labels
      const zplContent = await generateQuickPrintZPL(selectedTemplate, qty)
      
      // Use MCP printing system to print labels
      const printResult = await printZebraLabel(selectedPrinter, zplContent, {
        template_name: selectedTemplate,
        quick_print: true,
        quantity: qty,
        created_by: 'quick-print-user', // Could be enhanced with actual user context
        print_type: 'quick_print'
      })
      
      if (printResult.success) {
        console.log('🖨️ Successfully queued quick print job:', printResult.job_id)
        toast.success(`${qty} ${selectedTemplate} label(s) sent to printer`)
        
        // Close modal after successful print
        setTimeout(() => {
          onClose()
        }, 1000)
      } else {
        console.error('❌ Quick print job failed:', printResult.error)
        toast.error(`Print failed: ${printResult.error}`)
        
        // Fallback: Download ZPL file if MCP printing fails
        console.log('📁 Falling back to ZPL file download')
        downloadZPLFile(zplContent, `Quick_Print_${selectedTemplate}_${new Date().toISOString().split('T')[0]}.zpl`)
        toast.info('ZPL file downloaded as fallback')
      }
      
    } catch (error) {
      console.error('Failed to print quick labels:', error)
      toast.error('Failed to generate or print labels')
      
      // Fallback: Try to generate and download ZPL file
      try {
        const zplContent = await generateQuickPrintZPL(selectedTemplate, qty)
        downloadZPLFile(zplContent, `Quick_Print_${selectedTemplate}_${new Date().toISOString().split('T')[0]}.zpl`)
        toast.info('Generated fallback ZPL file due to print error')
      } catch (fallbackError) {
        console.error('Failed to generate fallback ZPL:', fallbackError)
        toast.error('Unable to generate labels')
      }
    } finally {
      setIsPrinting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Quick Print Labels</DialogTitle>
              <DialogDescription>
                Select a pre-designed label template for quick printing
              </DialogDescription>
            </div>
            
            {/* MCP Connection Status */}
            <MCPConnectionStatus compact={true} showDetails={false} className="text-xs" />
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* Template Grid */}
          <div className="space-y-2">
            <Label className="text-sm">Select Template</Label>
            <div className="grid grid-cols-1 gap-2 max-h-[200px] overflow-y-auto">
              {QUICK_PRINT_TEMPLATES.map(template => (
                <Card
                  key={template.id}
                  className={cn(
                    'cursor-pointer transition-all hover:shadow-md',
                    selectedTemplate === template.id 
                      ? 'ring-2 ring-primary border-primary' 
                      : 'hover:border-gray-400'
                  )}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm font-medium">
                      {template.name}
                    </CardTitle>
                    <p className="text-xs text-muted-foreground mt-1">
                      Category: {template.category}
                    </p>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>

          {/* Printer & Quantity Controls */}
          <div className="grid grid-cols-2 gap-4">
            {/* Printer Selection */}
            <div className="space-y-2">
              <Label htmlFor="printer-select">
                Target Printer {isMCPConnected ? '(MCP)' : '(Config)'}
              </Label>
              <select
                id="printer-select"
                value={selectedPrinter}
                onChange={(e) => setSelectedPrinter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">Select Printer...</option>
                {availablePrinters.map((printer: any) => (
                  <option key={printer.id} value={printer.id}>
                    {printer.name} ({printer.ip_address || printer.ipAddress})
                    {mcpZebraPrinters.length > 0 && (
                      printer.health?.status === 'online' ? ' ✅' : 
                      printer.health?.status === 'offline' ? ' ❌' : ' ⚠️'
                    )}
                  </option>
                ))}
              </select>
              {!isMCPConnected && (
                <p className="text-xs text-amber-600">
                  ⚠️ MCP server offline - using static printer configuration
                </p>
              )}
            </div>

            {/* Quantity */}
            <div className="space-y-2">
              <Label htmlFor="quantity" className="text-sm">Quantity</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                max="100"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Warning for MCP offline */}
          {!isMCPConnected && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium">MCP Printing Server Offline</p>
                  <p className="text-xs">
                    Using fallback printer configuration. ZPL files will be downloaded if printing fails.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Print Preview & Actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              {selectedTemplate && (
                <span>
                  Ready to print {quantity} {selectedTemplate} label{parseInt(quantity) > 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handlePreview}
                disabled={!selectedTemplate || isGeneratingZPL}
              >
                {isGeneratingZPL ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </>
                )}
              </Button>

              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              <Button 
                onClick={handlePrint}
                disabled={!selectedTemplate || !selectedPrinter || isPrinting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isPrinting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Printing...
                  </>
                ) : (
                  <>
                    <Printer className="h-4 w-4 mr-2" />
                    Print {quantity} Label{parseInt(quantity) > 1 ? 's' : ''}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}