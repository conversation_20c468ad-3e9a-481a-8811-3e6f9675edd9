import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useQuantityTracking, QuantityState, QuantityData } from '@/hooks/useQuantityTracking'
import { toast } from 'sonner'
import { Loader2, ArrowRight, AlertTriangle, Package, FileText } from 'lucide-react'

interface QuantityTransitionModalProps {
  isOpen: boolean
  onClose: () => void
  orderLineId: string
  orderUid: string
  customerPartNumber: string
  quantities: QuantityData
  initialFromState?: QuantityState
  initialToState?: QuantityState
}

export function QuantityTransitionModal({
  isOpen,
  onClose,
  orderLineId,
  orderUid,
  customerPartNumber,
  quantities,
  initialFromState,
  initialToState
}: QuantityTransitionModalProps) {
  const { transitionQuantity, getAvailableTransitions, getStateDisplayName } = useQuantityTracking()
  
  const [fromState, setFromState] = useState<QuantityState | ''>('')
  const [toState, setToState] = useState<QuantityState | ''>('')
  const [quantity, setQuantity] = useState<string>('')
  const [reason, setReason] = useState('')
  const [rejectionReason, setRejectionReason] = useState('')
  const [ctNumbers, setCTNumbers] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [availableToStates, setAvailableToStates] = useState<QuantityState[]>([])

  // Get available from states (states with non-zero quantities)
  const availableFromStates: QuantityState[] = [
    { state: 'pending_procurement_arrangement' as QuantityState, value: quantities.pendingProcurementArrangement },
    { state: 'requested_from_stock' as QuantityState, value: quantities.requestedFromStock },
    { state: 'awaiting_kitting_packing' as QuantityState, value: quantities.awaitingKittingPacking },
    { state: 'in_kitting_packing' as QuantityState, value: quantities.inKittingPacking },
    { state: 'on_hold_at_kitting_packing' as QuantityState, value: quantities.onHoldAtKittingPacking },
    { state: 'kitted_packed_awaiting_screening_qc' as QuantityState, value: quantities.kittedPackedAwaitingScreeningQc },
    { state: 'in_screening_qc' as QuantityState, value: quantities.inScreeningQc },
    { state: 'on_hold_at_screening_qc' as QuantityState, value: quantities.onHoldAtScreeningQc },
    { state: 'screening_qc_passed_ready_for_invoice' as QuantityState, value: quantities.screeningQcPassedReadyForInvoice },
    { state: 'screening_qc_rejected' as QuantityState, value: quantities.screeningQcRejected },
    { state: 'invoiced' as QuantityState, value: quantities.invoiced }
  ].filter(item => item.value > 0).map(item => item.state)

  // Load available transitions when from state changes
  useEffect(() => {
    if (fromState) {
      getAvailableTransitions(fromState).then(setAvailableToStates)
    } else {
      setAvailableToStates([])
    }
  }, [fromState, getAvailableTransitions])

  // Initialize form with provided values
  useEffect(() => {
    if (isOpen) {
      setFromState(initialFromState || '')
      setToState(initialToState || '')
      setQuantity('')
      setReason('')
      setRejectionReason('')
      setCTNumbers('')
    }
  }, [isOpen, initialFromState, initialToState])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!fromState || !toState || !quantity) {
      toast.error('Please fill in all required fields')
      return
    }

    const quantityNum = parseInt(quantity)
    if (isNaN(quantityNum) || quantityNum <= 0) {
      toast.error('Please enter a valid quantity')
      return
    }

    // Get current quantity for validation
    const currentQuantity = getCurrentQuantity(fromState)
    if (quantityNum > currentQuantity) {
      toast.error(`Cannot move ${quantityNum} items. Only ${currentQuantity} available in ${getStateDisplayName(fromState)}`)
      return
    }

    setIsSubmitting(true)

    try {
      const ctNumbersArray = ctNumbers.trim() ? ctNumbers.split(/[,\s]+/).filter(ct => ct.length > 0) : undefined
      
      const result = await transitionQuantity({
        orderLineId,
        fromState,
        toState,
        quantity: quantityNum,
        reason: reason.trim() || undefined,
        rejectionReason: rejectionReason.trim() || undefined,
        ctNumbers: ctNumbersArray,
        metadata: {
          orderUid,
          customerPartNumber,
          timestamp: new Date().toISOString()
        }
      })

      if (result.success) {
        toast.success(`Successfully moved ${quantityNum} items to ${getStateDisplayName(toState)}`)
        onClose()
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('Transition failed:', error)
      toast.error('Failed to transition quantity')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getCurrentQuantity = (state: QuantityState): number => {
    switch (state) {
      case 'pending_procurement_arrangement': return quantities.pendingProcurementArrangement
      case 'requested_from_stock': return quantities.requestedFromStock
      case 'awaiting_kitting_packing': return quantities.awaitingKittingPacking
      case 'in_kitting_packing': return quantities.inKittingPacking
      case 'on_hold_at_kitting_packing': return quantities.onHoldAtKittingPacking
      case 'kitted_packed_awaiting_screening_qc': return quantities.kittedPackedAwaitingScreeningQc
      case 'in_screening_qc': return quantities.inScreeningQc
      case 'on_hold_at_screening_qc': return quantities.onHoldAtScreeningQc
      case 'screening_qc_passed_ready_for_invoice': return quantities.screeningQcPassedReadyForInvoice
      case 'screening_qc_rejected': return quantities.screeningQcRejected
      case 'invoiced': return quantities.invoiced
      default: return 0
    }
  }

  const isQCRejection = toState === 'screening_qc_rejected'
  const maxQuantity = fromState ? getCurrentQuantity(fromState) : 0

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Move Quantity</span>
          </DialogTitle>
          <DialogDescription>
            Move quantity between workflow states for order {orderUid} - {customerPartNumber}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Order Summary */}
          <div className="bg-white border rounded p-3">
            <Label className="text-xs font-semibold text-blue-900 mb-2 block">
              Order Information
            </Label>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Order:</span> <span className="font-medium">{orderUid}</span>
              </div>
              <div>
                <span className="text-gray-600">Total Quantity:</span> <span className="font-medium">{quantities.totalOrderQuantity}</span>
              </div>
            </div>
          </div>

          {/* From State */}
          <div className="space-y-2">
            <Label htmlFor="fromState">From State *</Label>
            <Select value={fromState} onValueChange={(value) => setFromState(value as QuantityState)}>
              <SelectTrigger>
                <SelectValue placeholder="Select source state" />
              </SelectTrigger>
              <SelectContent>
                {availableFromStates.map(state => (
                  <SelectItem key={state} value={state}>
                    <div className="flex justify-between items-center w-full">
                      <span>{getStateDisplayName(state)}</span>
                      <Badge variant="secondary" className="ml-2">
                        {getCurrentQuantity(state)} available
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* To State */}
          <div className="space-y-2">
            <Label htmlFor="toState">To State *</Label>
            <Select 
              value={toState} 
              onValueChange={(value) => setToState(value as QuantityState)}
              disabled={!fromState || availableToStates.length === 0}
            >
              <SelectTrigger>
                <SelectValue placeholder={!fromState ? "Select from state first" : "Select destination state"} />
              </SelectTrigger>
              <SelectContent>
                {availableToStates.map(state => (
                  <SelectItem key={state} value={state}>
                    {getStateDisplayName(state)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <div className="flex space-x-2">
              <Input
                id="quantity"
                type="number"
                min="1"
                max={maxQuantity}
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                placeholder="Enter quantity to move"
                className="flex-1"
              />
              {fromState && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setQuantity(maxQuantity.toString())}
                  disabled={maxQuantity === 0}
                >
                  Max ({maxQuantity})
                </Button>
              )}
            </div>
          </div>

          {/* Transition Visualization */}
          {fromState && toState && (
            <div className="flex items-center justify-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <Badge variant="secondary" className="px-3 py-1">
                {getStateDisplayName(fromState)}
              </Badge>
              <ArrowRight className="h-4 w-4 text-blue-600" />
              <Badge variant="secondary" className="px-3 py-1">
                {getStateDisplayName(toState)}
              </Badge>
            </div>
          )}

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Optional reason for this transition"
              rows={2}
            />
          </div>

          {/* QC Rejection Reason (only for QC rejections) */}
          {isQCRejection && (
            <div className="space-y-2">
              <Label htmlFor="rejectionReason" className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span>Rejection Reason *</span>
              </Label>
              <Textarea
                id="rejectionReason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Detailed reason for QC rejection (required)"
                rows={3}
                required={isQCRejection}
              />
            </div>
          )}

          {/* CT Numbers */}
          <div className="bg-white border rounded p-3">
            <Label htmlFor="ctNumbers" className="flex items-center space-x-2 text-xs font-semibold text-blue-900 mb-2">
              <FileText className="h-4 w-4" />
              <span>CT Numbers (Optional)</span>
            </Label>
            <Input
              id="ctNumbers"
              value={ctNumbers}
              onChange={(e) => setCTNumbers(e.target.value)}
              placeholder="Enter CT numbers to track with this transition"
              className="text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              Example: CT12345678901, CT98765432109 (comma or space separated)
            </p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !fromState || !toState || !quantity}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Move Quantity
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}