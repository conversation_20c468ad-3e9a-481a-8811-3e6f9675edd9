import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Title } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { 
  Search, 
  Copy, 
  Mail, 
  MessageSquare,
  Clock,
  Package,
  AlertCircle,
  CheckCircle2,

  Globe,
  FileText,
  Loader2
} from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { cn } from '@/lib/utils'

interface ProcurementQueryModalProps {
  isOpen: boolean
  onClose: () => void
  order: OrderLineWithDetails
}

// Procurement query templates
const QUERY_TEMPLATES = {
  eta_update: {
    title: 'ETA Update Request',
    template: `Subject: ETA Update Required - {customer_part_number}

Hi Team,

We need an updated ETA for the following order:

Order Details:
- Order UID: {uid}
- Part Number: {customer_part_number}
- Description: {description}
- Quantity Ordered: {order_quantity}
- Current ETA: {current_eta}
- PO Date: {po_date}

Current Status: We are {pending_quantity} units short and need to understand the revised delivery schedule.

Could you please provide:
1. Updated ETA for the pending quantities
2. Confirmation of availability
3. Any potential delays or issues

Please respond at your earliest convenience.

Best regards,
{user_name}`
  },
  
  availability_check: {
    title: 'Stock Availability Check',
    template: `Subject: Stock Check - {customer_part_number}

Hello,

Please confirm current stock availability for:

Part Details:
- Part Number: {customer_part_number}
- Description: {description}
- Required Quantity: {pending_quantity}
- Urgency: {urgency_level}

Order Reference:
- Order UID: {uid}
- PO Date: {po_date}
- Customer: {customer_name}

Please advise:
- Current stock levels
- Expected replenishment date if out of stock
- Alternative part numbers if available

Awaiting your response.

Thanks,
{user_name}`
  },
  
  expedite_request: {
    title: 'Expedite Request',
    template: `Subject: URGENT - Expedite Request for {customer_part_number}

Hi Team,

We have an urgent requirement for the following part:

URGENT ORDER DETAILS:
- Order UID: {uid}
- Part Number: {customer_part_number}
- Description: {description}
- Required Quantity: {pending_quantity}
- Original ETA: {current_eta}
- Customer: {customer_name}

REASON FOR EXPEDITE:
This order is critical for our {customer_name} delivery schedule. Any delay will impact our customer relationship.

REQUEST:
Please expedite this order and provide:
1. Earliest possible delivery date
2. Any additional costs for expedited processing
3. Confirmation of expedite approval

Please treat this as HIGH PRIORITY.

Urgent response needed.

Best regards,
{user_name}`
  },
  
  substitute_inquiry: {
    title: 'Substitute Part Inquiry',
    template: `Subject: Alternative/Substitute Parts - {customer_part_number}

Hello,

We are experiencing delays with the following part and need substitute options:

Original Part:
- Part Number: {customer_part_number}
- Description: {description}
- Required Quantity: {pending_quantity}
- Order UID: {uid}

Please provide:
1. Compatible substitute part numbers
2. Availability and pricing for substitutes
3. Technical specifications comparison
4. Lead times for alternative parts

Customer approval will be required for any substitutions.

Please send substitute options ASAP.

Regards,
{user_name}`
  }
}

export function ProcurementQueryModal({ isOpen, onClose, order }: ProcurementQueryModalProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<keyof typeof QUERY_TEMPLATES>('eta_update')
  const [generatedQuery, setGeneratedQuery] = useState('')
  const [isCopied, setIsCopied] = useState(false)
  const [urgencyLevel, setUrgencyLevel] = useState<'normal' | 'high' | 'urgent'>('normal')

  // Generate query when template or urgency changes
  useState(() => {
    if (isOpen) {
      generateQuery()
    }
  }, [isOpen, selectedTemplate, urgencyLevel])

  const generateQuery = () => {
    const template = QUERY_TEMPLATES[selectedTemplate].template
    
    // Calculate pending quantity (mock calculation)
    const pendingQuantity = order.quantities?.pending_procurement || 5
    
    // Replace placeholders with actual order data
    const query = template
      .replace(/\{uid\}/g, order.uid || 'Unknown')
      .replace(/\{customer_part_number\}/g, order.customer_part_number || 'Unknown')
      .replace(/\{description\}/g, order.bpi_description || 'Unknown')
      .replace(/\{order_quantity\}/g, String(order.order_quantity || 0))
      .replace(/\{pending_quantity\}/g, String(pendingQuantity))
      .replace(/\{current_eta\}/g, order.current_eta || 'TBD')
      .replace(/\{po_date\}/g, order.po_date || 'Unknown')
      .replace(/\{customer_name\}/g, order.customer?.name || 'Unknown Customer')
      .replace(/\{urgency_level\}/g, urgencyLevel.toUpperCase())
      .replace(/\{user_name\}/g, '[Your Name]')

    setGeneratedQuery(query)
  }

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedQuery)
      setIsCopied(true)
      setTimeout(() => setIsCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-300'
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-300'
      case 'normal': return 'bg-blue-100 text-blue-700 border-blue-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const getPendingQuantity = () => {
    return order.quantities?.pending_procurement || Math.max(1, (order.order_quantity || 10) - 5)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5" />
            <span>Procurement Query Generator - {order.uid}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Part Number:</span> {order.customer_part_number}
              </div>
              <div>
                <span className="font-medium">Order Quantity:</span> {order.order_quantity}
              </div>
              <div>
                <span className="font-medium">Pending Quantity:</span> 
                <Badge variant="outline" className="ml-2 bg-orange-50 text-orange-700">
                  {getPendingQuantity()} units needed
                </Badge>
              </div>
              <div>
                <span className="font-medium">Current ETA:</span> {order.current_eta || 'TBD'}
              </div>
              <div className="col-span-2">
                <span className="font-medium">Description:</span> {order.bpi_description}
              </div>
            </div>
          </div>

          {/* Template Selection */}
          <div className="space-y-3">
            <Label>Query Template</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(QUERY_TEMPLATES).map(([key, template]) => (
                <div
                  key={key}
                  className={cn(
                    "p-3 border rounded-lg cursor-pointer transition-colors",
                    selectedTemplate === key 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-200 bg-white hover:bg-gray-50"
                  )}
                  onClick={() => setSelectedTemplate(key as keyof typeof QUERY_TEMPLATES)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "h-3 w-3 rounded-full",
                        selectedTemplate === key ? "bg-blue-600" : "bg-gray-300"
                      )} />
                      <span className="font-medium">{template.title}</span>
                    </div>
                    {key === 'expedite_request' && (
                      <Badge variant="outline" className="bg-red-50 text-red-700 text-xs">
                        Urgent
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Urgency Level */}
          <div className="space-y-3">
            <Label>Priority Level</Label>
            <div className="flex space-x-3">
              {[
                { id: 'normal', label: 'Normal', icon: <Clock className="h-4 w-4" /> },
                { id: 'high', label: 'High Priority', icon: <AlertCircle className="h-4 w-4" /> },
                { id: 'urgent', label: 'Urgent', icon: <Package className="h-4 w-4" /> }
              ].map((level) => (
                <Button
                  key={level.id}
                  variant={urgencyLevel === level.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setUrgencyLevel(level.id as any)}
                  className={cn(
                    "flex items-center space-x-2",
                    urgencyLevel === level.id && getUrgencyColor(level.id)
                  )}
                >
                  {level.icon}
                  <span>{level.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Generated Query */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Generated Query</Label>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={generateQuery}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Regenerate
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyToClipboard}
                  className={cn(
                    isCopied && "bg-green-50 border-green-300 text-green-700"
                  )}
                >
                  {isCopied ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            <div className="border rounded-lg p-4 bg-gray-50">
              <pre className="whitespace-pre-wrap text-sm font-mono text-gray-800 overflow-x-auto">
                {generatedQuery}
              </pre>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Mail className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">Quick Actions</span>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Mail className="h-4 w-4 mr-2" />
                  Email Procurement
                </Button>
                <Button variant="outline" size="sm">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  WhatsApp Team
                </Button>
              </div>
            </div>
            <div className="mt-2 text-sm text-blue-700">
              💡 Query copied to clipboard. Paste into your email client or communication tool.
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500 flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Query template: {QUERY_TEMPLATES[selectedTemplate].title}</span>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button onClick={handleCopyToClipboard}>
                <Copy className="h-4 w-4 mr-2" />
                Copy & Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}