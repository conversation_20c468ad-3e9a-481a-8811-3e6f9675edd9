import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  History, 
  User, 
  Clock, 
  Package, 
  Hash,
  MessageSquare,
  ArrowUpDown,
  RefreshCw,
  AlertCircle,
  CheckCircle2,
  Printer,
  Upload,
  Download,
  Loader2
} from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { cn } from '@/lib/utils'

interface AuditLogEntry {
  id: string
  timestamp: string
  action: string
  actor: string
  actor_email: string
  description: string
  metadata?: Record<string, any>
  category: 'status' | 'ct' | 'quantity' | 'document' | 'communication' | 'system'
}

interface ViewHistoryModalProps {
  isOpen: boolean
  onClose: () => void
  order: OrderLineWithDetails
}

// Mock audit log data - in real implementation this would come from useOrderAuditLog hook
const generateMockAuditLog = (orderUid: string): AuditLogEntry[] => {
  const now = new Date()
  return [
    {
      id: '1',
      timestamp: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      action: 'ct_assigned',
      actor: '<PERSON>',
      actor_email: '<EMAIL>',
      description: 'Assigned 3 CT numbers to order',
      metadata: { ct_numbers: ['ABCD1234567890', 'EFGH1234567890', 'IJKL1234567890'] },
      category: 'ct'
    },
    {
      id: '2',
      timestamp: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      action: 'status_updated',
      actor: 'Alice Johnson',
      actor_email: '<EMAIL>',
      description: 'Updated order status from Pending to In Kitting',
      metadata: { from_status: 'pending', to_status: 'in_kitting' },
      category: 'status'
    },
    {
      id: '3',
      timestamp: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      action: 'fai_uploaded',
      actor: 'Bob Wilson',
      actor_email: '<EMAIL>',
      description: 'Uploaded FAI document: HP_G10_FAI_v2.xlsx',
      metadata: { filename: 'HP_G10_FAI_v2.xlsx', file_size: '2.1 MB' },
      category: 'document'
    },
    {
      id: '4',
      timestamp: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
      action: 'quantity_moved',
      actor: 'System',
      actor_email: '<EMAIL>',
      description: 'Moved 5 units from Procurement to Kitting queue',
      metadata: { from_stage: 'procurement', to_stage: 'kitting', quantity: 5 },
      category: 'quantity'
    },
    {
      id: '5',
      timestamp: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      action: 'order_created',
      actor: 'Jane Doe',
      actor_email: '<EMAIL>',
      description: `Created order ${orderUid} for HP laptop parts`,
      metadata: { order_quantity: 10, customer: 'HP Inc.' },
      category: 'system'
    }
  ]
}

export function ViewHistoryModal({ isOpen, onClose, order }: ViewHistoryModalProps) {
  const [auditLog, setAuditLog] = useState<AuditLogEntry[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<string>('all')

  // Load audit log when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true)
      // Simulate API call to fetch audit log
      setTimeout(() => {
        const mockLog = generateMockAuditLog(order.uid || 'A001')
        setAuditLog(mockLog)
        setIsLoading(false)
      }, 1000)
    }
  }, [isOpen, order.uid])

  // Filter audit log entries
  const filteredLog = auditLog.filter(entry => 
    filter === 'all' || entry.category === filter
  )

  const getActionIcon = (action: string, category: string) => {
    switch (category) {
      case 'ct': return <Hash className="h-4 w-4" />
      case 'status': return <ArrowUpDown className="h-4 w-4" />
      case 'quantity': return <Package className="h-4 w-4" />
      case 'document': return <Upload className="h-4 w-4" />
      case 'communication': return <MessageSquare className="h-4 w-4" />
      case 'system': return <RefreshCw className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ct': return 'bg-blue-100 text-blue-700'
      case 'status': return 'bg-green-100 text-green-700'
      case 'quantity': return 'bg-purple-100 text-purple-700'
      case 'document': return 'bg-orange-100 text-orange-700'
      case 'communication': return 'bg-cyan-100 text-cyan-700'
      case 'system': return 'bg-gray-100 text-gray-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    } else {
      const diffMins = Math.floor(diffMs / (1000 * 60))
      return `${Math.max(1, diffMins)} minute${diffMins > 1 ? 's' : ''} ago`
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <History className="h-5 w-5" />
            <span>Order History - {order.uid}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Order Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Part Number:</span> {order.customer_part_number}
              </div>
              <div>
                <span className="font-medium">Order Quantity:</span> {order.order_quantity}
              </div>
              <div className="col-span-2">
                <span className="font-medium">Description:</span> {order.bpi_description}
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Filter by:</span>
            <div className="flex flex-wrap gap-1">
              {[
                { id: 'all', label: 'All Activities', count: auditLog.length },
                { id: 'status', label: 'Status Changes', count: auditLog.filter(e => e.category === 'status').length },
                { id: 'ct', label: 'CT Numbers', count: auditLog.filter(e => e.category === 'ct').length },
                { id: 'quantity', label: 'Quantities', count: auditLog.filter(e => e.category === 'quantity').length },
                { id: 'document', label: 'Documents', count: auditLog.filter(e => e.category === 'document').length },
                { id: 'communication', label: 'Communications', count: auditLog.filter(e => e.category === 'communication').length },
                { id: 'system', label: 'System', count: auditLog.filter(e => e.category === 'system').length }
              ].map(filterOption => (
                <Button
                  key={filterOption.id}
                  variant={filter === filterOption.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(filterOption.id)}
                  className="text-xs"
                  disabled={filterOption.count === 0}
                >
                  {filterOption.label} ({filterOption.count})
                </Button>
              ))}
            </div>
          </div>

          {/* Audit Log Timeline */}
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                <span className="ml-3 text-gray-600">Loading order history...</span>
              </div>
            ) : filteredLog.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <History className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>No history entries found for the selected filter.</p>
              </div>
            ) : (
              filteredLog.map((entry, index) => (
                <div
                  key={entry.id}
                  className={cn(
                    "flex items-start space-x-4 p-3 rounded-lg border transition-colors",
                    index === 0 ? "bg-blue-50 border-blue-200" : "bg-white border-gray-200 hover:bg-gray-50"
                  )}
                >
                  {/* Icon */}
                  <div className={cn(
                    "flex-shrink-0 p-2 rounded-full",
                    getCategoryColor(entry.category)
                  )}>
                    {getActionIcon(entry.action, entry.category)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900">
                          {entry.description}
                        </p>
                        <Badge 
                          variant="outline" 
                          className={cn("text-xs", getCategoryColor(entry.category))}
                        >
                          {entry.category}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatTimestamp(entry.timestamp)}
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-600">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{entry.actor}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(entry.timestamp).toLocaleString()}</span>
                      </div>
                    </div>

                    {/* Metadata */}
                    {entry.metadata && Object.keys(entry.metadata).length > 0 && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                        <div className="font-medium text-gray-700 mb-1">Details:</div>
                        <div className="space-y-0.5">
                          {Object.entries(entry.metadata).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
                              <span className="font-medium">
                                {Array.isArray(value) ? value.join(', ') : String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Summary Stats */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900">Activity Summary</span>
              </div>
              <div className="text-sm text-blue-700">
                Total: {auditLog.length} activities over {
                  auditLog.length > 0 
                    ? Math.ceil((new Date().getTime() - new Date(auditLog[auditLog.length - 1].timestamp).getTime()) / (1000 * 60 * 60 * 24))
                    : 0
                } days
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export History
            </Button>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}