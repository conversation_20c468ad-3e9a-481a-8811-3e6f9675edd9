import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON>h, Eye, Copy } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCTNumbers } from '@/hooks/useCTNumbers'
import { useQuantityTracking } from '@/hooks/useQuantityTracking'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'

interface CTNumberDisplayProps {
  orderLineId: string
  compact?: boolean
  className?: string
  showActions?: boolean
}

// Map database quantity fields to display names
const quantityStateDisplay: Record<string, { name: string; color: string }> = {
  'pending_procurement': { name: 'Pending Procurement', color: 'bg-yellow-50 text-yellow-700 border-yellow-200' },
  'requested_from_stock': { name: 'Requested', color: 'bg-orange-50 text-orange-700 border-orange-200' },
  'awaiting_kitting_packing': { name: 'Awaiting Kitting', color: 'bg-purple-50 text-purple-700 border-purple-200' },
  'in_kitting_packing': { name: 'In Kitting', color: 'bg-blue-50 text-blue-700 border-blue-200' },
  'kitted_awaiting_qc': { name: 'Awaiting QC', color: 'bg-indigo-50 text-indigo-700 border-indigo-200' },
  'in_screening_qc': { name: 'In QC', color: 'bg-cyan-50 text-cyan-700 border-cyan-200' },
  'qc_passed_ready_invoice': { name: 'Ready for Invoice', color: 'bg-green-50 text-green-700 border-green-200' },
  'invoiced': { name: 'Invoiced', color: 'bg-emerald-50 text-emerald-700 border-emerald-200' },
  'shipped_delivered': { name: 'Shipped', color: 'bg-gray-50 text-gray-700 border-gray-200' }
}

export function CTNumberDisplay({
  orderLineId,
  compact = false,
  className,
  showActions = false
}: CTNumberDisplayProps) {
  const queryClient = useQueryClient()
  const { data: ctNumbers = [], isLoading, error, refetch: refetchCTNumbers } = useCTNumbers(orderLineId)
  const { quantities, loadQuantities } = useQuantityTracking(orderLineId)
  const [lastUpdate, setLastUpdate] = React.useState(Date.now())
  
  // Function to determine current quantity state
  const getCurrentQuantityState = () => {
    if (!quantities) return null
    
    // Check states in reverse order (from final to initial)
    if (quantities.shippedDelivered > 0) return 'shipped_delivered'
    if (quantities.invoiced > 0) return 'invoiced'
    if (quantities.screeningQcPassedReadyForInvoice > 0) return 'qc_passed_ready_invoice'
    if (quantities.inScreeningQc > 0) return 'in_screening_qc'
    if (quantities.kittedPackedAwaitingScreeningQc > 0) return 'kitted_awaiting_qc'
    if (quantities.inKittingPacking > 0) return 'in_kitting_packing'
    if (quantities.awaitingKittingPacking > 0) return 'awaiting_kitting_packing'
    if (quantities.requestedFromStock > 0) return 'requested_from_stock'
    if (quantities.pendingProcurementArrangement > 0) return 'pending_procurement'
    
    return null
  }
  
  const currentState = getCurrentQuantityState()

  // Subscribe to CT number changes and refresh quantities
  React.useEffect(() => {
    if (!orderLineId) return

    console.log('🔍 CTNumberDisplay Debug:', {
      orderLineId,
      ctNumbers: ctNumbers?.length || 0,
      isLoading,
      error: error?.message,
      quantities,
      lastUpdate: new Date(lastUpdate).toISOString()
    })

    // Set up real-time subscription for CT number changes
    const channel = supabase
      .channel(`ct-display-${orderLineId}-${Date.now()}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ct_numbers',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('🔄 CT number changed, refreshing all data:', payload)
          // Force refresh everything
          setLastUpdate(Date.now())
          await Promise.all([
            refetchCTNumbers(),
            loadQuantities(orderLineId),
            queryClient.invalidateQueries({ queryKey: ['orders'] }),
            queryClient.invalidateQueries({ queryKey: ['order-quantities', orderLineId] })
          ])
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_line_quantities',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('📊 Quantities changed, refreshing all data:', payload)
          // Force refresh everything
          setLastUpdate(Date.now())
          await Promise.all([
            refetchCTNumbers(),
            loadQuantities(orderLineId),
            queryClient.invalidateQueries({ queryKey: ['orders'] }),
            queryClient.invalidateQueries({ queryKey: ['order-quantities', orderLineId] })
          ])
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quantity_logs',
          filter: `order_line_id=eq.${orderLineId}`
        },
        async (payload) => {
          console.log('📝 Quantity log created, refreshing:', payload)
          // Slight delay to ensure database consistency
          setTimeout(async () => {
            setLastUpdate(Date.now())
            await Promise.all([
              refetchCTNumbers(),
              loadQuantities(orderLineId),
              queryClient.invalidateQueries({ queryKey: ['orders'] })
            ])
          }, 100)
        }
      )
      .subscribe((status) => {
        console.log(`📡 CTNumberDisplay subscription status for ${orderLineId}:`, status)
      })

    return () => {
      console.log(`🔌 Cleaning up CTNumberDisplay subscription for ${orderLineId}`)
      supabase.removeChannel(channel)
    }
  }, [orderLineId, loadQuantities, refetchCTNumbers, queryClient])

  const handleCopyAll = async () => {
    if (ctNumbers.length === 0) return
    
    const ctText = ctNumbers.map(ct => ct.ct_number).join('\n')
    try {
      await navigator.clipboard.writeText(ctText)
      toast.success(`Copied ${ctNumbers.length} CT numbers to clipboard`)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
      toast.error('Failed to copy CT numbers')
    }
  }

  const formatCTForDisplay = (ctNumber: string) => {
    // Return CT number without any dashes or separators
    return ctNumber
  }

  if (isLoading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <Hash className="h-3 w-3 text-gray-400 animate-pulse" />
        <span className="text-xs text-gray-400">Loading CTs...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <Hash className="h-3 w-3 text-red-400" />
        <span className="text-xs text-red-400">Error loading CTs</span>
      </div>
    )
  }

  if (ctNumbers.length === 0) {
    // Show debug info in development, hide in production
    if (process.env.NODE_ENV === 'development') {
      return (
        <div className={cn("flex items-center space-x-2", className)}>
          <Hash className="h-3 w-3 text-gray-400" />
          <span className="text-xs text-gray-400">No CTs found for {orderLineId}</span>
        </div>
      )
    }
    return null // Don't show anything if no CT numbers in production
  }

  if (compact) {
    return (
      <div key={`ct-display-${orderLineId}-${lastUpdate}`} className={cn("flex items-center justify-between", className)}>
        <div className="flex items-center space-x-2">
          <Hash className="h-3 w-3 text-blue-600" />
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
            {ctNumbers.length} CT{ctNumbers.length === 1 ? '' : 's'} Assigned
          </Badge>
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyAll}
              className="h-5 w-5 p-0 hover:bg-blue-50"
              title="Copy all CT numbers"
            >
              <Copy className="h-3 w-3 text-blue-600" />
            </Button>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Hash className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-900">
            CT Numbers ({ctNumbers.length})
          </span>
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyAll}
              className="text-xs px-2 py-1 h-6"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy All
            </Button>
          </div>
        )}
      </div>

      {/* CT Numbers Grid */}
      <div className="grid grid-cols-1 gap-1">
        {ctNumbers.slice(0, compact ? 3 : 10).map((ct, index) => (
          <div
            key={ct.id}
            className="flex items-center justify-between p-2 bg-gray-50 rounded border text-xs"
          >
            <div className="flex items-center space-x-2 flex-1">
              <Badge
                variant="outline"
                className="bg-blue-100 text-blue-700 border-blue-300 text-xs px-1 py-0 font-semibold"
              >
                #{index + 1}
              </Badge>
              <code className="font-mono text-xs font-bold text-gray-900 truncate">
                {formatCTForDisplay(ct.ct_number)}
              </code>
            </div>
            
            <div className="flex items-center space-x-1">
              {currentState && quantityStateDisplay[currentState] && (
                <Badge
                  variant="outline"
                  className={cn(
                    "text-[10px] px-1 py-0",
                    quantityStateDisplay[currentState].color
                  )}
                >
                  {quantityStateDisplay[currentState].name}
                </Badge>
              )}
              
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs px-1 py-0",
                  ct.status === 'assigned' && "bg-green-50 text-green-700 border-green-200",
                  ct.status === 'in_use' && "bg-blue-50 text-blue-700 border-blue-200",
                  ct.status === 'completed' && "bg-gray-50 text-gray-700 border-gray-200"
                )}
              >
                {ct.status === 'assigned' ? 'Active' : 
                 ct.status === 'in_use' ? 'In Use' : 'Complete'}
              </Badge>
            </div>
          </div>
        ))}
        
        {ctNumbers.length > (compact ? 3 : 10) && (
          <div className="text-xs text-gray-500 text-center py-1">
            +{ctNumbers.length - (compact ? 3 : 10)} more CT numbers
          </div>
        )}
      </div>
    </div>
  )
}
