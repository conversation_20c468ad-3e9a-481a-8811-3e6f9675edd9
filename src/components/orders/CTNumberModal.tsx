import { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Hash,
  Plus,
  Copy,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Wand2,
  FileText,
  Clock,
  Shuffle,
  Loader2,
  Scan,
  Zap,
  Printer,
  Eye,
  X,

  Play,
  Save,
  ArrowRight,
  Package,
  SkipForward
} from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { useCTNumbers, useSaveCTNumbers, useCTDuplicateCheck } from '@/hooks/useCTNumbers'
import { useAuth } from '@/hooks/useAuth'
import { useQuantityTracking } from '@/hooks/useQuantityTracking'
import { 
  validateCTN<PERSON>ber, 
  validateCTBatch, 
  extractCTNumbers, 
  formatCTForDisplay,
  generateCTNumber,
  CTGenerationOptions
} from '@/utils/ctNumberValidation'
import { useBarcodeScanner, validateCTBarcode } from '@/hooks/useBarcodeScanner'
import {
  generateCTLabelsZPL,
  downloadZPLFile,
  LABEL_TEMPLATES,
  generateLabelaryPreviewUrl
} from '@/utils/labelGeneration'
import { usePrinterConfig } from '@/hooks/usePrinterConfig'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface CTNumberModalProps {
  isOpen: boolean
  onClose: () => void
  orderLine: OrderLineWithDetails
  onSave: (ctNumbers: string[]) => void
  mode?: 'assignment' | 'print'
}

export function CTNumberModal({ isOpen, onClose, orderLine, onSave, mode = 'assignment' }: CTNumberModalProps) {
  // State management
  const [ctInput, setCTInput] = useState('')
  const [ctNumbers, setCTNumbers] = useState<string[]>([])
  const [showGenerator, setShowGenerator] = useState(false)
  const [duplicateWarnings, setDuplicateWarnings] = useState<Record<string, string>>({})
  const [generateCount, setGenerateCount] = useState(1)
  const [scannerEnabled, setScannerEnabled] = useState(true)
  const [recentScans, setRecentScans] = useState<string[]>([])
  const [showPrintSection, setShowPrintSection] = useState(false)
  const [savedCTNumbers, setSavedCTNumbers] = useState<string[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState('default-ct-label')
  const [selectedPrinter, setSelectedPrinter] = useState('')

  const [isGeneratingZPL, setIsGeneratingZPL] = useState(false)
  const [isPrinting, setIsPrinting] = useState(false)

  const [isTransitioning, setIsTransitioning] = useState(false)
  
  // Mode-specific state
  const [newlyAssignedCTs, setNewlyAssignedCTs] = useState<string[]>([])
  const [selectedExistingCTs, setSelectedExistingCTs] = useState<string[]>([])
  const [isInPrintMode, setIsInPrintMode] = useState(mode === 'print')
  const [printSelectionMode, setPrintSelectionMode] = useState<'new_only' | 'existing_only' | 'custom'>('new_only')
  const [customSelectedCTs, setCustomSelectedCTs] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState<'input' | 'list' | 'print'>('input')
  
  const inputRef = useRef<HTMLInputElement>(null)

  // Hooks
  const { user } = useAuth()
  const { data: existingCTs = [] } = useCTNumbers(orderLine.id)
  const saveCTMutation = useSaveCTNumbers()
  const duplicateCheckMutation = useCTDuplicateCheck()
  const { getActivePrinters, getDefaultPrinter } = usePrinterConfig()
  const {
    useZebraPrinters,
    printZebraLabel
  } = useMCPPrinting()
  const { transitionQuantity } = useQuantityTracking(orderLine.id)

  // Barcode scanner
  const { isScanning } = useBarcodeScanner({
    enabled: scannerEnabled && isOpen,
    onScan: handleBarcodeScan,
    minLength: 10,
    maxLength: 20,
    scanTimeout: 150,
    validateFormat: validateCTBarcode
  })

  // Available printers
  const { data: mcpZebraPrinters = [] } = useZebraPrinters()
  const configPrinters = getActivePrinters()
  const defaultPrinter = getDefaultPrinter()
  
  const availablePrinters = mcpZebraPrinters.length > 0 ? mcpZebraPrinters : configPrinters
  const defaultPrinterId = mcpZebraPrinters.length > 0 
    ? mcpZebraPrinters[0]?.id 
    : defaultPrinter?.id

  // Reset when modal opens
  useEffect(() => {
    if (isOpen) {
      setCTInput('')
      setCTNumbers(existingCTs.map(ct => ct.ct_number))
      setShowGenerator(false)
      setDuplicateWarnings({})
      setGenerateCount(1)
      setRecentScans([])
      setScannerEnabled(true)
      setShowPrintSection(false)
      setSavedCTNumbers([])
      

      setIsTransitioning(false)
      
      const smartTemplate = getSmartTemplateSelection()
      setSelectedTemplate(smartTemplate.recommended)
      setSelectedPrinter(defaultPrinterId || '')
      
      setIsInPrintMode(mode === 'print')
      setNewlyAssignedCTs([])
      setActiveTab(mode === 'print' ? 'print' : 'input')
      
      if (mode === 'print') {
        setSelectedExistingCTs([])
        setCustomSelectedCTs([])
        setPrintSelectionMode('existing_only')
        
        if (existingCTs.length > 0) {
          setShowPrintSection(true)
        }
      } else {
        setSelectedExistingCTs([])
        setCustomSelectedCTs([])
        setPrintSelectionMode('new_only')
      }
    }
  }, [isOpen, existingCTs, defaultPrinterId, mode])

  // Core functions (simplified from Option 1)
  async function handleBarcodeScan(scannedCT: string) {
    const validation = validateCTNumber(scannedCT)
    if (!validation.isValid) return

    const formattedCT = validation.formatted
    if (ctNumbers.includes(formattedCT)) return

    setRecentScans(prev => [formattedCT, ...prev.slice(0, 2)])

    try {
      const duplicateChecks = await duplicateCheckMutation.mutateAsync([formattedCT])
      const check = duplicateChecks[0]
      
      if (check?.isDuplicate && check.warningMessage) {
        setDuplicateWarnings(prev => ({ ...prev, [formattedCT]: check.warningMessage! }))
      }
      
      setCTNumbers(prev => [...prev, formattedCT])
      setActiveTab('list') // Auto-switch to list view
    } catch (error) {
      setCTNumbers(prev => [...prev, formattedCT])
      setActiveTab('list')
    }
  }

  const handleInputChange = (value: string) => {
    setCTInput(value)
  }

  const handleAddCTs = async () => {
    if (!ctInput.trim()) return

    const extracted = extractCTNumbers(ctInput)
    const validation = validateCTBatch(extracted)
    
    if (validation.validCTs.length > 0) {
      try {
        const duplicateChecks = await duplicateCheckMutation.mutateAsync(validation.validCTs)
        const warnings: Record<string, string> = {}
        
        duplicateChecks.forEach((check, index) => {
          const ct = validation.validCTs[index]
          if (check.isDuplicate && check.warningMessage) {
            warnings[ct] = check.warningMessage
          }
        })
        
        setDuplicateWarnings(prev => ({ ...prev, ...warnings }))
        setCTNumbers(prev => [...prev, ...validation.validCTs])
        setCTInput('')
        setActiveTab('list') // Auto-switch to list view
      } catch (error) {
        setCTNumbers(prev => [...prev, ...validation.validCTs])
        setCTInput('')
        setActiveTab('list')
      }
    }
  }

  const handleRemoveCT = (index: number) => {
    const ctToRemove = ctNumbers[index]
    setCTNumbers(prev => prev.filter((_, i) => i !== index))
    
    if (ctToRemove && duplicateWarnings[ctToRemove]) {
      setDuplicateWarnings(prev => {
        const updated = { ...prev }
        delete updated[ctToRemove]
        return updated
      })
    }
  }

  const handleGenerateCT = (options: CTGenerationOptions) => {
    const requiredQuantity = orderLine.order_quantity || 0
    const currentCount = ctNumbers.length
    const remainingNeeded = Math.max(0, requiredQuantity - currentCount)
    const willExceedRequired = generateCount > remainingNeeded && remainingNeeded > 0
    
    if (willExceedRequired && remainingNeeded > 0) {
      const proceed = window.confirm(
        `⚠️ Generating ${generateCount} CTs, but only ${remainingNeeded} more needed. Proceed?`
      )
      if (!proceed) return
    }
    
    const newCTs: string[] = []
    const countToGenerate = Math.min(generateCount, 50)
    
    for (let i = 0; i < countToGenerate; i++) {
      const newCT = generateCTNumber(options)
      newCTs.push(newCT)
    }
    
    setCTNumbers(prev => [...prev, ...newCTs])
    setShowGenerator(false)
    setActiveTab('list') // Auto-switch to list view
  }

  const handleCopyAll = async () => {
    const ctText = ctNumbers.join('\\n')
    try {
      await navigator.clipboard.writeText(ctText)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const getCTStatus = (ctNumber: string) => {
    if (newlyAssignedCTs.includes(ctNumber)) {
      return { type: 'new', icon: 'NEW', label: 'New', color: 'text-green-600' }
    }
    if (existingCTs.some(ct => ct.ct_number === ctNumber)) {
      return { type: 'existing', icon: 'SAVED', label: 'Existing', color: 'text-blue-600' }
    }
    return { type: 'pending', icon: 'WAIT', label: 'Pending', color: 'text-amber-600' }
  }

  const getSelectedCTsForPrinting = () => {
    if (isInPrintMode) {
      return customSelectedCTs
    } else {
      switch (printSelectionMode) {
        case 'new_only':
          return newlyAssignedCTs
        case 'existing_only':
          return selectedExistingCTs
        case 'custom':
          return customSelectedCTs
        default:
          return newlyAssignedCTs
      }
    }
  }

  const handleSelectAllCTs = () => {
    if (isInPrintMode) {
      const existingCTNumbers = existingCTs.map(ct => ct.ct_number)
      setCustomSelectedCTs(existingCTNumbers)
    } else {
      setCustomSelectedCTs([...ctNumbers])
      setPrintSelectionMode('custom')
    }
  }

  const handleSelectNoneCTs = () => {
    setCustomSelectedCTs([])
  }

  const handleToggleCTSelection = (ctNumber: string) => {
    setCustomSelectedCTs(prev => {
      if (prev.includes(ctNumber)) {
        return prev.filter(ct => ct !== ctNumber)
      } else {
        return [...prev, ctNumber]
      }
    })
    if (!isInPrintMode) {
      setPrintSelectionMode('custom')
    }
  }

  const getPrintSelectionSummary = () => {
    const selectedCTs = getSelectedCTsForPrinting()
    const count = selectedCTs.length

    if (count === 0) {
      return { message: 'No CTs selected', color: 'text-gray-500' }
    }

    if (isInPrintMode) {
      return { 
        message: `${count} CT${count === 1 ? '' : 's'} selected`, 
        color: 'text-blue-600' 
      }
    } else {
      const newCount = selectedCTs.filter(ct => newlyAssignedCTs.includes(ct)).length
      const existingCount = count - newCount

      if (newCount > 0 && existingCount > 0) {
        return { 
          message: `${newCount} new + ${existingCount} existing (${count} total)`, 
          color: 'text-green-600' 
        }
      } else if (newCount > 0) {
        return { 
          message: `${newCount} new CT${newCount === 1 ? '' : 's'}`, 
          color: 'text-green-600' 
        }
      } else {
        return { 
          message: `${existingCount} existing CT${existingCount === 1 ? '' : 's'}`, 
          color: 'text-blue-600' 
        }
      }
    }
  }

  const getSmartTemplateSelection = () => {
    const hasCTs = existingCTs.length > 0 || newlyAssignedCTs.length > 0
    const selectedCTs = getSelectedCTsForPrinting()

    if (hasCTs && selectedCTs.length > 0) {
      return {
        recommended: 'default-ct-label',
        category: 'ct_labels',
        explanation: 'CT label templates'
      }
    } else {
      return {
        recommended: 'hp-external',
        category: 'quick_print',
        explanation: 'Quick print templates'
      }
    }
  }

  const getAvailableTemplatesByCategory = () => {
    const smartSelection = getSmartTemplateSelection()
    
    if (smartSelection.category === 'ct_labels') {
      return [
        { id: 'default-ct-label', name: 'Default CT', recommended: true },
        { id: 'compact-ct-label', name: 'Compact CT', recommended: false },
        { id: 'hp-ct-label', name: 'HP CT', recommended: false },
        { id: 'hp-external', name: 'HP External', recommended: false },
        { id: 'fragile', name: 'Fragile', recommended: false }
      ]
    } else {
      return [
        { id: 'hp-external', name: 'HP External', recommended: true },
        { id: 'fragile', name: 'Fragile', recommended: false },
        { id: 'priority', name: 'Priority', recommended: false },
        { id: 'qc-hold', name: 'QC Hold', recommended: false },
        { id: 'default-ct-label', name: 'Default CT', recommended: false }
      ]
    }
  }

  const handleSave = async () => {
    if (ctNumbers.length === 0 || !user?.id) return

    try {
      const existingCTNumbers = existingCTs.map(ct => ct.ct_number)
      const newCTNumbers = ctNumbers.filter(ct => !existingCTNumbers.includes(ct))
      
      if (newCTNumbers.length > 0) {
        await saveCTMutation.mutateAsync({
          orderLineId: orderLine.id,
          ctNumbers: newCTNumbers,
          assignedBy: user.id
        })
      }
      
      setNewlyAssignedCTs(newCTNumbers)
      
      const selectedForPrinting = getSelectedCTsForPrinting()
      if (selectedForPrinting.length > 0) {
        setSavedCTNumbers(selectedForPrinting)
      } else {
        setSavedCTNumbers(newCTNumbers)
      }
      
      setShowPrintSection(true)
      setActiveTab('print')
      onSave(ctNumbers)
    } catch (error) {
      console.error('Failed to save CT numbers:', error)
    }
  }


  
  const handlePreviewLabels = async () => {
    const selectedCTsForPrint = getSelectedCTsForPrinting()
    
    if (selectedCTsForPrint.length === 0) return
    
    setIsGeneratingZPL(true)
    
    try {
      const zplContent = await generateCTLabelsZPL(selectedCTsForPrint, orderLine, selectedTemplate, selectedCTsForPrint.length)
      const template = LABEL_TEMPLATES[selectedTemplate]
      
      const labelaryUrl = generateLabelaryPreviewUrl(zplContent, template)
      window.open(labelaryUrl, '_blank')
      
    } catch (error) {
      console.error('Failed to generate preview:', error)
    } finally {
      setIsGeneratingZPL(false)
    }
  }
  


  const performAutomaticTransition = async () => {
    // CRITICAL FIX: Use correct CT source based on context
    const ctsToTransition = isInPrintMode 
      ? getSelectedCTsForPrinting() 
      : (newlyAssignedCTs.length > 0 ? newlyAssignedCTs : savedCTNumbers)
    
    if (!user?.id || ctsToTransition.length === 0) {
      console.log('No CTs to transition or user not authenticated')
      return
    }

    console.log('🔄 Starting automatic transition:', {
      isInPrintMode,
      ctsToTransition,
      ctQuantity: ctsToTransition.length,
      orderLineId: orderLine.id,
      orderUid: orderLine.uid,
      currentQuantities: orderLine.quantities
    })

    setIsTransitioning(true)
    
    try {
      const ctQuantity = ctsToTransition.length
      
      // Determine the appropriate transition based on current state
      // Check where the majority of quantities currently are
      let fromState = 'awaiting_kitting_packing'
      let toState = 'in_kitting_packing'
      let reason = `Auto transition after ${isInPrintMode ? 'print' : 'CT assignment'} - ${ctQuantity} units`
      
      if (orderLine.quantities) {
        const q = orderLine.quantities
        
        // Log the actual quantities object to debug
        console.log('Current quantities object:', q)
        
        // CRITICAL FIX: Check pending_procurement FIRST for new orders
        if ((q.pending_procurement || 0) > 0 && (q.pending_procurement || 0) >= ctQuantity) {
          // Handle items in procurement - this requires a 3-step transition
          console.log('🔄 Starting 3-step procurement transition for', ctQuantity, 'units')
          
          // Step 1: Move from pending_procurement to requested_from_stock
          fromState = 'pending_procurement_arrangement'
          toState = 'requested_from_stock'
          reason = `Auto transition step 1: Procurement to Stock Request - ${ctQuantity} units`
          
          const step1Transition = await transitionQuantity({
            orderLineId: orderLine.id,
            fromState: fromState as any,
            toState: toState as any,
            quantity: ctQuantity,
            reason: reason,
            ctNumbers: ctsToTransition,
            metadata: {
              trigger: isInPrintMode ? 'print_completion' : 'ct_assignment_completion',
              assigned_ct_count: ctQuantity,
              workflow_type: 'automatic',
              print_mode: isInPrintMode,
              step: 'procurement_to_stock_request',
              step_number: 1,
              total_steps: 3,
              timestamp: new Date().toISOString()
            }
          })
          
          if (!step1Transition.success) {
            toast.error(step1Transition.message || 'Failed to move from procurement')
            console.error('Step 1 transition failed:', step1Transition)
            setIsTransitioning(false)
            setTimeout(() => onClose(), 2000)
            return
          }

          // Show single progress toast instead of multiple step toasts
          toast.loading(`Processing workflow transition... (Step 1/3)`, { id: 'workflow-progress' })
          
          // Step 2: Move from requested_from_stock to awaiting_kitting_packing
          fromState = 'requested_from_stock'
          toState = 'awaiting_kitting_packing'
          reason = `Auto transition step 2: Stock to Awaiting Kitting - ${ctQuantity} units`
          
          const step2Transition = await transitionQuantity({
            orderLineId: orderLine.id,
            fromState: fromState as any,
            toState: toState as any,
            quantity: ctQuantity,
            reason: reason,
            ctNumbers: ctsToTransition,
            metadata: {
              trigger: 'auto_progression',
              assigned_ct_count: ctQuantity,
              workflow_type: 'automatic',
              print_mode: isInPrintMode,
              step: 'stock_to_awaiting_kitting',
              step_number: 2,
              total_steps: 3,
              timestamp: new Date().toISOString()
            }
          })
          
          if (!step2Transition.success) {
            toast.error(step2Transition.message || 'Failed to move to awaiting kitting', { id: 'workflow-progress' })
            console.error('Step 2 transition failed:', step2Transition)
            setIsTransitioning(false)
            setTimeout(() => onClose(), 2000)
            return
          }

          // Update progress toast
          toast.loading(`Processing workflow transition... (Step 2/3)`, { id: 'workflow-progress' })
          
          // Step 3: Move from awaiting_kitting_packing to in_kitting_packing
          fromState = 'awaiting_kitting_packing'
          toState = 'in_kitting_packing'
          reason = `Auto transition step 3: Start Active Kitting - ${ctQuantity} units`
          
          const step3Transition = await transitionQuantity({
            orderLineId: orderLine.id,
            fromState: fromState as any,
            toState: toState as any,
            quantity: ctQuantity,
            reason: reason,
            ctNumbers: ctsToTransition,
            metadata: {
              trigger: 'auto_progression',
              assigned_ct_count: ctQuantity,
              workflow_type: 'automatic',
              print_mode: isInPrintMode,
              step: 'awaiting_to_active_kitting',
              step_number: 3,
              total_steps: 3,
              timestamp: new Date().toISOString()
            }
          })
          
          if (step3Transition.success) {
            toast.success(`Successfully moved ${ctQuantity} units to active kitting!`, { id: 'workflow-progress' })
            setTimeout(() => onClose(), 2000)
          } else {
            toast.error(step3Transition.message || 'Failed to move to active kitting', { id: 'workflow-progress' })
            console.error('Step 3 transition failed:', step3Transition)
            setTimeout(() => onClose(), 2000)
          }
          
          setIsTransitioning(false)
          return // Exit after handling procurement case
          
        } else if ((q.awaiting_kitting_packing || 0) > 0 && (q.awaiting_kitting_packing || 0) >= ctQuantity) {
          fromState = 'awaiting_kitting_packing'
          toState = 'in_kitting_packing'
          reason = `Auto transition to kitting after ${isInPrintMode ? 'label print' : 'CT assignment'} - ${ctQuantity} units`
        } else if ((q.in_kitting_packing || 0) > 0 && (q.in_kitting_packing || 0) >= ctQuantity) {
          fromState = 'in_kitting_packing'
          toState = 'kitted_packed_awaiting_screening_qc'  // Use full state name for RPC
          reason = `Auto transition - ${ctQuantity} units completed kitting`
        } else if ((q.kitted_awaiting_qc || 0) > 0 && (q.kitted_awaiting_qc || 0) >= ctQuantity) {
          fromState = 'kitted_packed_awaiting_screening_qc'  // Use full state name for RPC
          toState = 'in_screening_qc'
          reason = `Auto transition to QC - ${ctQuantity} units`
        } else if ((q.in_screening_qc || 0) > 0 && (q.in_screening_qc || 0) >= ctQuantity) {
          fromState = 'in_screening_qc'
          toState = 'screening_qc_passed_ready_for_invoice'  // Use full state name for RPC
          reason = `Auto transition - ${ctQuantity} units passed QC`
        } else {
          // Check if there are any quantities at all
          const totalQuantity = q.total_order_quantity || orderLine.order_quantity || 0
          
          // If this is a new order with no quantities initialized, we might need to handle differently
          if (totalQuantity > 0 && (!q.awaiting_kitting_packing && !q.pending_procurement)) {
            // Assume items are in pending procurement if no other state has quantities
            toast.error(`Items appear to be in procurement stage. Cannot auto-transition from this state.`)
          } else {
            toast.error(`Cannot auto-transition ${ctQuantity} units. Insufficient quantity in any single state.`)
          }
          
          console.error('Quantity state mismatch:', {
            quantities: q,
            ctQuantity,
            orderQuantity: orderLine.order_quantity
          })
          setIsTransitioning(false)
          return
        }
      } else {
        // No quantities object at all - this shouldn't happen if the order exists
        toast.error('Order quantities not initialized. Please refresh and try again.')
        console.error('No quantities object found for order:', orderLine)
        setIsTransitioning(false)
        return
      }
      
      // For all other states (not pending_procurement), do a single transition
      console.log('📊 Transition details:', {
        fromState,
        toState,
        quantity: ctQuantity,
        reason,
        orderUid: orderLine.uid
      })
      
      const transitionResult = await transitionQuantity({
        orderLineId: orderLine.id,
        fromState: fromState as any,
        toState: toState as any,
        quantity: ctQuantity,
        reason: reason,
        ctNumbers: ctsToTransition,
        metadata: {
          trigger: isInPrintMode ? 'print_completion' : 'ct_assignment_completion',
          assigned_ct_count: ctQuantity,
          workflow_type: 'automatic',
          print_mode: isInPrintMode,
          timestamp: new Date().toISOString()
        }
      })
      
      console.log('✅ Transition result:', transitionResult)
      
      if (transitionResult.success) {
        toast.success(`Moved ${ctQuantity} units from ${fromState.replace(/_/g, ' ')} to ${toState.replace(/_/g, ' ')}`)
        setTimeout(() => {
          onClose()
        }, 1500)
      } else {
        toast.error(transitionResult.message || `Failed to move quantities`)
        console.error('Transition failed:', transitionResult)
        setTimeout(() => {
          onClose()
        }, 2000)
      }
      
    } catch (error) {
      console.error('Failed to perform automatic workflow transition:', error)
      toast.error('Failed to perform automatic transition')
      setTimeout(() => {
        onClose()
      }, 2000)
    } finally {
      setIsTransitioning(false)
    }
  }



  // NEW: Three clear action handlers that preserve all existing functionality
  const handlePrintOnly = async () => {
    const selectedCTsForPrint = getSelectedCTsForPrinting()

    if (!selectedPrinter || selectedCTsForPrint.length === 0) return

    setIsPrinting(true)

    try {
      const zplContent = await generateCTLabelsZPL(selectedCTsForPrint, orderLine, selectedTemplate, selectedCTsForPrint.length)

      const printResult = await printZebraLabel(selectedPrinter, zplContent, {
        order_uid: orderLine.uid,
        ct_numbers: selectedCTsForPrint,
        template_name: selectedTemplate,
        created_by: user?.email || 'unknown',
        part_number: orderLine.customer_part_number,
        description: orderLine.bpi_description,
        selection_mode: printSelectionMode,
        print_mode: isInPrintMode ? 'reprint' : 'new_assignment'
      })

      if (printResult.success) {
        toast.success(`Successfully printed ${selectedCTsForPrint.length} label${selectedCTsForPrint.length === 1 ? '' : 's'}`)
      } else {
        // Download ZPL as fallback
        downloadZPLFile(zplContent, `CT_Labels_${orderLine.uid}_${new Date().toISOString().split('T')[0]}.zpl`)
        toast.info(`Print failed - ZPL file downloaded as backup`)
      }

      // Close modal after print-only operation
      setTimeout(() => onClose(), 1500)

    } catch (error) {
      console.error('Failed to print labels:', error)

      try {
        const zplContent = await generateCTLabelsZPL(selectedCTsForPrint, orderLine, selectedTemplate, selectedCTsForPrint.length)
        downloadZPLFile(zplContent, `CT_Labels_${orderLine.uid}_${new Date().toISOString().split('T')[0]}.zpl`)
        toast.error('Print failed - ZPL file downloaded as backup')
      } catch (fallbackError) {
        console.error('Failed to generate fallback ZPL:', fallbackError)
        toast.error('Failed to print or generate labels')
      }

      setTimeout(() => onClose(), 2000)
    } finally {
      setIsPrinting(false)
    }
  }

  const handlePrintAndProgress = async () => {
    const selectedCTsForPrint = getSelectedCTsForPrinting()

    if (!selectedPrinter || selectedCTsForPrint.length === 0) return

    setIsPrinting(true)

    try {
      const zplContent = await generateCTLabelsZPL(selectedCTsForPrint, orderLine, selectedTemplate, selectedCTsForPrint.length)

      const printResult = await printZebraLabel(selectedPrinter, zplContent, {
        order_uid: orderLine.uid,
        ct_numbers: selectedCTsForPrint,
        template_name: selectedTemplate,
        created_by: user?.email || 'unknown',
        part_number: orderLine.customer_part_number,
        description: orderLine.bpi_description,
        selection_mode: printSelectionMode,
        print_mode: isInPrintMode ? 'reprint' : 'new_assignment'
      })

      if (printResult.success) {
        toast.success(`Successfully printed ${selectedCTsForPrint.length} label${selectedCTsForPrint.length === 1 ? '' : 's'}`)
      } else {
        // Download ZPL as fallback
        downloadZPLFile(zplContent, `CT_Labels_${orderLine.uid}_${new Date().toISOString().split('T')[0]}.zpl`)
        toast.info(`Print failed - ZPL downloaded, proceeding with workflow progression`)
      }

      // Always proceed with auto-progression (preserves existing logic)
      await performAutomaticTransition()

    } catch (error) {
      console.error('Failed to print labels:', error)

      try {
        const zplContent = await generateCTLabelsZPL(selectedCTsForPrint, orderLine, selectedTemplate, selectedCTsForPrint.length)
        downloadZPLFile(zplContent, `CT_Labels_${orderLine.uid}_${new Date().toISOString().split('T')[0]}.zpl`)

        // Still proceed with auto-progression even on print error (preserves existing logic)
        await performAutomaticTransition()
      } catch (fallbackError) {
        console.error('Failed to generate fallback ZPL:', fallbackError)
        toast.error('Failed to print labels and progress workflow')
        setTimeout(() => onClose(), 2000)
      }
    } finally {
      setIsPrinting(false)
    }
  }

  const handleSkipPrintAndProgress = async () => {
    const selectedCTsForPrint = getSelectedCTsForPrinting()

    if (selectedCTsForPrint.length === 0) return

    // Proceed directly with auto-progression without printing
    await performAutomaticTransition()
  }
  
  const inputValidation = validateCTNumber(ctInput.trim())
  const requiredQuantity = orderLine.order_quantity || 0
  const currentCount = ctNumbers.length
  const isComplete = currentCount >= requiredQuantity

  // Debug logging
  console.log('CTNumberModal render - isOpen:', isOpen, 'mode:', mode, 'orderLine:', orderLine?.uid)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-[95vw] max-h-[90vh] h-[90vh] p-0 desktop-modal-override" showCloseButton={false}>
        <div className="flex flex-col h-full">
          {/* Super Compact Header */}
          <DialogHeader className="px-3 py-2 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center space-x-2">
                <div className={cn(
                  "p-1.5 rounded",
                  isInPrintMode ? "bg-blue-600 text-white" : "bg-indigo-600 text-white"
                )}>
                  {isInPrintMode ? (
                    <Printer className="h-3 w-3" />
                  ) : (
                    <Hash className="h-3 w-3" />
                  )}
                </div>
                <div>
                  <h1 className="text-sm font-bold text-gray-900">
                    {isInPrintMode ? 'Print CT Labels' : 'CT Management'}
                  </h1>
                  <p className="text-xs text-gray-600">
                    {orderLine.uid} • {orderLine.customer_part_number}
                  </p>
                </div>
              </DialogTitle>
              
              <div className="flex items-center space-x-2">
                <MCPConnectionStatus compact={true} showDetails={false} />
                <Badge variant={isComplete ? "default" : "outline"} className="text-xs px-2 py-0.5">
                  {currentCount}/{requiredQuantity}
                </Badge>
                <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex">
            {/* Tab Navigation */}
            <div className="w-16 bg-gray-50 border-r flex flex-col">
              <Button
                variant={activeTab === 'input' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('input')}
                className="h-12 w-12 m-1 p-0 flex flex-col items-center justify-center"
                disabled={isInPrintMode}
              >
                <Plus className="h-4 w-4" />
                <span className="text-xs mt-0.5">Add</span>
              </Button>
              
              <Button
                variant={activeTab === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('list')}
                className="h-12 w-12 m-1 p-0 flex flex-col items-center justify-center"
              >
                <Hash className="h-4 w-4" />
                <span className="text-xs mt-0.5">List</span>
                {ctNumbers.length > 0 && (
                  <Badge variant="outline" className="text-xs -mt-1 px-1 py-0 h-4 min-w-0">
                    {ctNumbers.length}
                  </Badge>
                )}
              </Button>

              <Button
                variant={activeTab === 'print' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab('print')}
                className="h-12 w-12 m-1 p-0 flex flex-col items-center justify-center"
                disabled={!showPrintSection && !isInPrintMode}
              >
                <Printer className="h-4 w-4" />
                <span className="text-xs mt-0.5">Print</span>
              </Button>
            </div>

            {/* Main Content */}
            <div className="flex-1 p-3 overflow-y-auto">
              
              {/* Order Info Bar */}
              <div className="bg-gray-50 border rounded p-2 mb-3">
                <div className="grid grid-cols-4 gap-3 text-xs">
                  <div>
                    <span className="text-gray-500 font-medium">Part</span>
                    <p className="font-semibold text-gray-900 truncate">{orderLine.customer_part_number}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 font-medium">Qty</span>
                    <p className="font-semibold text-gray-900">{orderLine.order_quantity}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 font-medium">Progress</span>
                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                      <div 
                        className={cn(
                          "h-1.5 rounded-full transition-all",
                          isComplete ? "bg-green-500" : "bg-blue-500"
                        )}
                        style={{ width: `${Math.min((currentCount / requiredQuantity) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-500 font-medium">Status</span>
                    <p className={cn(
                      "font-semibold text-xs",
                      isComplete ? "text-green-600" : "text-orange-600"
                    )}>
                      {isComplete ? "Complete" : `Need ${requiredQuantity - currentCount}`}
                    </p>
                  </div>
                </div>
              </div>

              {/* Tab Content */}
              {activeTab === 'input' && !isInPrintMode && (
                <div className="space-y-3">
                  {/* Scanner Section */}
                  <div className="bg-white border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold">Scanner</h3>
                      <Button
                        variant={scannerEnabled ? "default" : "outline"}
                        size="sm"
                        onClick={() => setScannerEnabled(!scannerEnabled)}
                        className="text-xs px-2 py-1 h-6"
                      >
                        <Scan className="h-3 w-3 mr-1" />
                        {scannerEnabled ? 'On' : 'Off'}
                      </Button>
                    </div>
                    
                    {isScanning && (
                      <div className="flex items-center justify-center p-2 bg-blue-50 border border-blue-200 rounded text-xs mb-2">
                        <Zap className="h-3 w-3 text-blue-600 animate-pulse mr-1" />
                        <span className="text-blue-700">Scanning...</span>
                      </div>
                    )}
                    
                    {recentScans.length > 0 && (
                      <div className="mb-2">
                        <div className="flex flex-wrap gap-1">
                          {recentScans.map((scan, index) => (
                            <span key={index} className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-mono">
                              {formatCTForDisplay(scan)}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Manual Entry */}
                  <div className="bg-white border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold">Manual Entry</h3>
                      <Button 
                        variant="outline"
                        size="sm"
                        onClick={() => setShowGenerator(!showGenerator)}
                        className="text-xs px-2 py-1 h-6"
                      >
                        <Wand2 className="h-3 w-3 mr-1" />
                        Generate
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <div className="flex space-x-2">
                        <Input
                          ref={inputRef}
                          placeholder="CT number..."
                          value={ctInput}
                          onChange={(e) => handleInputChange(e.target.value)}
                          className={cn(
                            "font-mono text-sm h-7 flex-1",
                            ctInput && !inputValidation.isValid && "border-red-300"
                          )}
                        />
                        <Button 
                          onClick={handleAddCTs} 
                          disabled={!inputValidation.isValid}
                          size="sm"
                          className="text-xs px-3 h-7"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      {ctInput && (
                        <div className={cn(
                          "p-2 rounded text-xs",
                          inputValidation.isValid 
                            ? "bg-green-50 border border-green-200 text-green-700" 
                            : "bg-red-50 border border-red-200 text-red-700"
                        )}>
                          {inputValidation.isValid ? (
                            <div className="flex items-center">
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              <span>Valid: {formatCTForDisplay(inputValidation.formatted)}</span>
                            </div>
                          ) : (
                            <div>
                              {inputValidation.errors.map((error, i) => (
                                <div key={i} className="flex items-center">
                                  <XCircle className="h-3 w-3 mr-1" />
                                  <span>{error}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Generator */}
                  {showGenerator && (
                    <div className="bg-blue-50 border border-blue-200 rounded p-3">
                      <h4 className="text-sm font-semibold text-blue-900 mb-2">Generator</h4>
                      
                      <div className="mb-3">
                        <div className="flex items-center space-x-2">
                          <Label className="text-xs text-blue-800">Generate Quantity:</Label>
                          <Input
                            type="number"
                            min="1"
                            max="50"
                            value={generateCount}
                            onChange={(e) => setGenerateCount(Math.max(1, Math.min(50, parseInt(e.target.value) || 1)))}
                            className="w-20 text-sm h-8 font-semibold text-center"
                          />
                          <span className="text-xs text-gray-600">CTs</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setGenerateCount(Math.max(1, requiredQuantity - currentCount))}
                            className="text-xs px-2 h-6"
                          >
                            Fill ({Math.max(0, requiredQuantity - currentCount)})
                          </Button>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">How many CT numbers to generate (1-50)</p>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-2">
                        <Button
                          variant="outline"
                          onClick={() => handleGenerateCT({ strategy: 'fai_master' })}
                          className="text-xs p-2 h-12 flex flex-col items-center justify-center"
                        >
                          <FileText className="h-3 w-3 mb-1 text-blue-600" />
                          <span>FAI</span>
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleGenerateCT({ strategy: 'last_used' })}
                          className="text-xs p-2 h-12 flex flex-col items-center justify-center"
                        >
                          <Clock className="h-3 w-3 mb-1 text-green-600" />
                          <span>Last</span>
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => handleGenerateCT({ strategy: 'random_suffix', randomLength: 4 })}
                          className="text-xs p-2 h-12 flex flex-col items-center justify-center"
                        >
                          <Shuffle className="h-3 w-3 mb-1 text-purple-600" />
                          <span>Random</span>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'list' && (
                <div className="space-y-3">
                  {/* CT List Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold">CT Numbers ({ctNumbers.length})</h3>
                    <div className="flex space-x-1">
                      {ctNumbers.length > 0 && (
                        <Button variant="outline" size="sm" onClick={handleCopyAll} className="text-xs px-2 py-1 h-6">
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* CT Numbers Grid - Compact */}
                  <div className="border rounded bg-gray-50 max-h-80 overflow-y-auto">
                    {ctNumbers.length > 0 ? (
                      <div className="grid grid-cols-2 gap-1 p-2">
                        {ctNumbers.map((ct, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-white rounded border text-xs"
                          >
                            <div className="flex items-center space-x-2 flex-1">
                              <Badge
                                variant="outline"
                                className="bg-blue-100 text-blue-700 border-blue-300 text-xs px-2 py-0.5 font-semibold"
                              >
                                CT{index + 1}
                              </Badge>
                              <div className="flex flex-col flex-1 min-w-0">
                                <code className="font-mono text-xs font-bold text-gray-900 truncate">
                                  {formatCTForDisplay(ct)}
                                </code>
                                <Badge 
                                  variant="outline" 
                                  className={cn(
                                    "text-xs px-1 py-0 w-fit mt-0.5",
                                    getCTStatus(ct).color === 'text-green-600' && "bg-green-50 text-green-700 border-green-200",
                                    getCTStatus(ct).color === 'text-blue-600' && "bg-blue-50 text-blue-700 border-blue-200",
                                    getCTStatus(ct).color === 'text-amber-600' && "bg-amber-50 text-amber-700 border-amber-200"
                                  )}
                                >
                                  {getCTStatus(ct).label}
                                </Badge>
                                {duplicateWarnings[ct] && (
                                  <div className="flex items-center mt-1 p-1 bg-orange-50 border border-orange-200 rounded">
                                    <AlertTriangle className="h-3 w-3 text-orange-500 mr-1" />
                                    <span className="text-xs text-orange-700 truncate">
                                      Duplicate
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveCT(index)}
                              className="h-5 w-5 p-0 hover:bg-red-50 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-8 text-center text-gray-500">
                        <Hash className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">No CT numbers added yet</p>
                        <p className="text-xs">Use scanner or manual entry</p>
                      </div>
                    )}
                  </div>

                  {/* Print Mode CT Selection */}
                  {isInPrintMode && existingCTs.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-sm font-semibold text-blue-900">Select CTs ({existingCTs.length} available)</h3>
                        <div className="flex space-x-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={handleSelectAllCTs}
                            className="text-xs px-2 py-1 h-6"
                          >
                            All
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={handleSelectNoneCTs}
                            className="text-xs px-2 py-1 h-6"
                          >
                            None
                          </Button>
                        </div>
                      </div>

                      <div className="max-h-48 overflow-y-auto border border-gray-200 rounded bg-white">
                        <div className="grid grid-cols-3 gap-1 p-2">
                          {existingCTs.map((ct, index) => (
                            <label 
                              key={ct.ct_number} 
                              className={cn(
                                "flex items-center space-x-2 cursor-pointer p-2 rounded border transition-all text-xs",
                                customSelectedCTs.includes(ct.ct_number)
                                  ? "bg-blue-100 border-blue-400"
                                  : "bg-gray-50 border-gray-200 hover:bg-blue-50"
                              )}
                            >
                              <input
                                type="checkbox"
                                checked={customSelectedCTs.includes(ct.ct_number)}
                                onChange={() => handleToggleCTSelection(ct.ct_number)}
                                className="w-3 h-3 rounded text-blue-600"
                              />
                              <div className="flex-1 min-w-0">
                                <code className="font-mono text-xs font-bold block truncate">
                                  {formatCTForDisplay(ct.ct_number)}
                                </code>
                                <div className="text-xs text-gray-500">#{index + 1}</div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div className="mt-2 p-2 bg-white rounded border">
                        <div className={cn("text-xs font-semibold", getPrintSelectionSummary().color)}>
                          {getPrintSelectionSummary().message}
                        </div>
                      </div>
                    </div>
                  )}

                  {isInPrintMode && existingCTs.length === 0 && (
                    <div className="bg-amber-50 border border-amber-200 rounded p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-amber-600" />
                        <h3 className="text-sm font-semibold text-amber-900">No CTs Available</h3>
                      </div>
                      <p className="text-xs text-amber-700">
                        No CT numbers assigned yet. Please assign CT numbers first.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'print' && (showPrintSection || isInPrintMode) && (
                <div className="space-y-3">
                  {/* Print Status */}
                  <div className="bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-300 rounded p-3">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="bg-blue-600 rounded p-1.5">
                          <Printer className="h-3 w-3 text-white" />
                        </div>
                        <div>
                          <h3 className="text-sm font-bold text-blue-900">
                            {isInPrintMode ? 'Select & Print Labels' : 'CTs Saved - Ready to Print'}
                          </h3>
                          <p className="text-xs text-blue-700">
                            {isInPrintMode ? 'Choose CTs to reprint' : 'Print labels now'}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-green-100 text-green-800 text-xs">
                        ✅ {isInPrintMode ? customSelectedCTs.length : savedCTNumbers.length} Ready
                      </Badge>
                    </div>

                    {/* Clear Action Buttons - Replace confusing radio buttons */}
                    <div className="bg-white border rounded p-3 mb-3">
                      <Label className="text-xs font-semibold text-blue-900 mb-3 block">
                        Choose Action
                      </Label>
                      <div className="grid grid-cols-1 gap-3">
                        {/* Button 1: Print Only */}
                        <Button
                          onClick={handlePrintOnly}
                          disabled={!selectedPrinter || isPrinting || isTransitioning || getSelectedCTsForPrinting().length === 0}
                          size="sm"
                          variant="outline"
                          className="h-14 flex flex-col items-center justify-center hover:bg-gray-50 transition-all duration-200"
                        >
                          {isPrinting ? (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <Loader2 className="h-4 w-4 animate-spin text-gray-600" />
                                <span className="font-semibold text-gray-900">Printing...</span>
                              </div>
                              <span className="text-xs text-gray-600">
                                Processing {getSelectedCTsForPrinting().length} label{getSelectedCTsForPrinting().length === 1 ? '' : 's'}
                              </span>
                            </>
                          ) : (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <Printer className="h-4 w-4 text-gray-600" />
                                <span className="font-semibold text-gray-900">Print Labels Only</span>
                              </div>
                              <span className="text-xs text-gray-600">
                                Print {getSelectedCTsForPrinting().length} label{getSelectedCTsForPrinting().length === 1 ? '' : 's'} • No quantity movement
                              </span>
                            </>
                          )}
                        </Button>

                        {/* Button 2: Print & Move to Kitting */}
                        <Button
                          onClick={handlePrintAndProgress}
                          disabled={!selectedPrinter || isPrinting || isTransitioning || getSelectedCTsForPrinting().length === 0}
                          size="sm"
                          className="h-14 flex flex-col items-center justify-center bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                          {isPrinting || isTransitioning ? (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="font-semibold">
                                  {isPrinting ? 'Printing...' : 'Moving Quantities...'}
                                </span>
                              </div>
                              <span className="text-xs text-blue-100">
                                {isPrinting ? 'Processing labels' : 'Updating workflow stages'}
                              </span>
                            </>
                          ) : (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <Printer className="h-4 w-4" />
                                <ArrowRight className="h-3 w-3" />
                                <Package className="h-4 w-4" />
                                <span className="font-semibold">Print & Move to Kitting</span>
                              </div>
                              <span className="text-xs text-blue-100">
                                Print {getSelectedCTsForPrinting().length} label{getSelectedCTsForPrinting().length === 1 ? '' : 's'} • Auto-move quantities to next stage
                              </span>
                            </>
                          )}
                        </Button>

                        {/* Button 3: Skip Print & Move to Kitting */}
                        <Button
                          onClick={handleSkipPrintAndProgress}
                          disabled={isTransitioning || getSelectedCTsForPrinting().length === 0}
                          size="sm"
                          variant="secondary"
                          className="h-14 flex flex-col items-center justify-center hover:bg-gray-200 transition-all duration-200"
                        >
                          {isTransitioning ? (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <Loader2 className="h-4 w-4 animate-spin text-gray-600" />
                                <span className="font-semibold text-gray-900">Moving Quantities...</span>
                              </div>
                              <span className="text-xs text-gray-600">
                                Updating workflow stages
                              </span>
                            </>
                          ) : (
                            <>
                              <div className="flex items-center space-x-2 mb-1">
                                <SkipForward className="h-4 w-4 text-gray-600" />
                                <Package className="h-4 w-4 text-gray-600" />
                                <span className="font-semibold text-gray-900">Move to Kitting (No Print)</span>
                              </div>
                              <span className="text-xs text-gray-600">
                                Move {getSelectedCTsForPrinting().length} unit{getSelectedCTsForPrinting().length === 1 ? '' : 's'} to next stage • Skip printing
                              </span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    {/* Print Configuration */}
                    <div className="bg-white border rounded p-3 mb-3">
                      <Label className="text-xs font-semibold text-blue-900 mb-2 block">
                        Print Configuration
                      </Label>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label className="text-xs font-medium mb-1 block text-gray-700">Template</Label>
                          <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select template" />
                            </SelectTrigger>
                            <SelectContent>
                              {getAvailableTemplatesByCategory().map((template) => (
                                <SelectItem key={template.id} value={template.id}>
                                  <div className="flex items-center space-x-2">
                                    {template.recommended && <span className="text-yellow-500">⭐</span>}
                                    <span>{template.name}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label className="text-xs font-medium mb-1 block text-gray-700">Printer</Label>
                          <Select value={selectedPrinter} onValueChange={setSelectedPrinter}>
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select printer" />
                            </SelectTrigger>
                            <SelectContent>
                              {availablePrinters.map((printer: any) => (
                                <SelectItem key={printer.id} value={printer.id}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{printer.name}</span>
                                    {mcpZebraPrinters.length > 0 && (
                                      <span className="ml-2">
                                        {printer.health?.status === 'online' ? '✅' : '❌'}
                                      </span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    
                    {/* Print Preview */}
                    <div className="bg-white border rounded p-2 mb-3">
                      <div className={cn("text-xs font-semibold mb-1", getPrintSelectionSummary().color)}>
                        {getPrintSelectionSummary().message}
                      </div>
                      {getSelectedCTsForPrinting().length > 0 && (
                        <div className="text-xs text-gray-600">
                          {getSelectedCTsForPrinting().slice(0, 5).map(ct => formatCTForDisplay(ct)).join(', ')}
                          {getSelectedCTsForPrinting().length > 5 && ` +${getSelectedCTsForPrinting().length - 5} more`}
                        </div>
                      )}
                    </div>
                    
                    {/* Preview Action */}
                    <div className="flex items-center justify-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePreviewLabels}
                        disabled={isGeneratingZPL || getSelectedCTsForPrinting().length === 0}
                        className="text-xs px-3 py-1 h-7"
                      >
                        {isGeneratingZPL ? (
                          <>
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Eye className="h-3 w-3 mr-1" />
                            Preview Labels
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Bottom Action Bar */}
            <div className="border-t bg-gray-50 p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MCPConnectionStatus compact={true} showDetails={false} />
                  <div className="text-xs text-gray-500">
                    {isInPrintMode ? (
                      existingCTs.length > 0 ? (
                        <span className="text-blue-600 font-medium">Ready to print</span>
                      ) : (
                        <span className="text-amber-600">No CTs available</span>
                      )
                    ) : (
                      ctNumbers.length > 0 ? (
                        <span className={isComplete ? "text-green-600 font-medium" : ""}>
                          {isComplete ? "Ready to save & print" : `Need ${requiredQuantity - currentCount} more`}
                        </span>
                      ) : (
                        <span>Add CT numbers to begin</span>
                      )
                    )}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={onClose} className="text-xs px-3 py-1 h-7">
                    Cancel
                  </Button>
                  
                  {!showPrintSection && !isInPrintMode && (
                    <Button 
                      onClick={handleSave} 
                      disabled={ctNumbers.length === 0 || saveCTMutation.isPending}
                      size="sm"
                      className={cn(
                        "text-xs px-3 py-1 h-7",
                        isComplete && "bg-green-600 hover:bg-green-700"
                      )}
                    >
                      {saveCTMutation.isPending ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-3 w-3 mr-1" />
                          Save {ctNumbers.length}
                        </>
                      )}
                    </Button>
                  )}

                  {isInPrintMode && !showPrintSection && (
                    <Button 
                      onClick={() => setShowPrintSection(true)} 
                      disabled={existingCTs.length === 0 || customSelectedCTs.length === 0}
                      size="sm"
                      className="bg-blue-600 text-white text-xs px-3 py-1 h-7"
                    >
                      <Play className="h-3 w-3 mr-1" />
                      {customSelectedCTs.length === 0 
                        ? `Select from ${existingCTs.length}`
                        : `Print ${customSelectedCTs.length}`
                      }
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}