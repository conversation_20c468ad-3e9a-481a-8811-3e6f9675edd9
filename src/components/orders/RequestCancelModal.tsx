import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  AlertTriangle, 
  MessageSquare,
  Mail,
  User,
  Clock,
  Package,
  DollarSign,
  FileText,
  Loader2,
  CheckCircle2,
  XCircle
} from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'

interface RequestCancelModalProps {
  isOpen: boolean
  onClose: () => void
  order: OrderLineWithDetails
  onCancelRequested?: (orderUid: string, reason: string) => void
}

// Cancellation reason options
const CANCEL_REASONS = [
  {
    id: 'customer_request',
    label: 'Customer Requested Cancellation',
    description: 'Customer no longer needs this order',
    severity: 'high'
  },
  {
    id: 'duplicate_order',
    label: 'Duplicate Order',
    description: 'Order was placed multiple times by mistake',
    severity: 'medium'
  },
  {
    id: 'wrong_specifications',
    label: 'Wrong Specifications',
    description: 'Incorrect part number or specifications',
    severity: 'medium'
  },
  {
    id: 'delivery_delay',
    label: 'Unacceptable Delivery Delay',
    description: 'Supplier cannot meet required timeline',
    severity: 'high'
  },
  {
    id: 'budget_constraints',
    label: 'Budget/Cost Issues',
    description: 'Cost exceeds approved budget',
    severity: 'medium'
  },
  {
    id: 'technical_obsolete',
    label: 'Technical Obsolescence',
    description: 'Part is no longer compatible or needed',
    severity: 'low'
  },
  {
    id: 'supplier_issues',
    label: 'Supplier Reliability Issues',
    description: 'Problems with supplier quality or delivery',
    severity: 'high'
  },
  {
    id: 'other',
    label: 'Other Reason',
    description: 'Please specify in the details below',
    severity: 'medium'
  }
] as const

export function RequestCancelModal({ 
  isOpen, 
  onClose, 
  order, 
  onCancelRequested 
}: RequestCancelModalProps) {
  const [selectedReason, setSelectedReason] = useState('')
  const [customReason, setCustomReason] = useState('')
  const [additionalDetails, setAdditionalDetails] = useState('')
  const [urgencyLevel, setUrgencyLevel] = useState<'standard' | 'urgent'>('standard')
  const [notifyStakeholders, setNotifyStakeholders] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const { user } = useAuth()

  // Reset form when modal opens
  useState(() => {
    if (isOpen) {
      setSelectedReason('')
      setCustomReason('')
      setAdditionalDetails('')
      setUrgencyLevel('standard')
      setNotifyStakeholders(true)
      setIsSubmitting(false)
    }
  }, [isOpen])

  const selectedReasonData = CANCEL_REASONS.find(r => r.id === selectedReason)

  const handleSubmitCancellation = async () => {
    if (!selectedReason || !user?.id) return

    setIsSubmitting(true)

    try {
      const cancellationData = {
        orderUid: order.uid,
        reason: selectedReason === 'other' ? customReason : selectedReasonData?.label,
        reasonCategory: selectedReason,
        additionalDetails,
        urgencyLevel,
        requestedBy: user.id,
        requestedByEmail: user.email,
        notifyStakeholders,
        orderDetails: {
          partNumber: order.customer_part_number,
          description: order.bpi_description,
          quantity: order.order_quantity,
          customer: order.customer?.name,
          poDate: order.po_date,
          currentETA: order.current_eta
        },
        impact: {
          financialImpact: calculateFinancialImpact(),
          timelineImpact: calculateTimelineImpact(),
          customerImpact: selectedReasonData?.severity || 'medium'
        }
      }

      console.log('📋 Submitting cancellation request:', cancellationData)

      // TODO: Implement actual cancellation request submission
      // This would typically call a hook like useSubmitCancellationRequest
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('✅ Cancellation request submitted successfully')
      
      // Notify parent component
      onCancelRequested?.(
        order.uid || 'Unknown', 
        selectedReason === 'other' ? customReason : selectedReasonData?.label || 'Unknown'
      )
      
      // Close modal
      onClose()
      
    } catch (error) {
      console.error('❌ Failed to submit cancellation request:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const calculateFinancialImpact = () => {
    // Mock calculation - in real implementation would use actual costs
    const estimatedCost = (order.order_quantity || 10) * 50 // $50 per unit estimate
    return `Estimated ~$${estimatedCost.toLocaleString()}`
  }

  const calculateTimelineImpact = () => {
    const daysFromPO = order.po_date ? 
      Math.floor((new Date().getTime() - new Date(order.po_date).getTime()) / (1000 * 60 * 60 * 24)) : 
      0
    return `${daysFromPO} days since PO date`
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-700 border-red-300'
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-300'
      case 'low': return 'bg-blue-100 text-blue-700 border-blue-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const isFormValid = () => {
    if (!selectedReason) return false
    if (selectedReason === 'other' && !customReason.trim()) return false
    return true
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <X className="h-5 w-5 text-red-600" />
            <span>Request Order Cancellation - {order.uid}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Warning Banner */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-900">Cancellation Request</h4>
                <p className="text-sm text-red-700 mt-1">
                  This will submit a formal cancellation request for review. The order will remain active until approved by management.
                </p>
              </div>
            </div>
          </div>

          {/* Order Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Part Number:</span> {order.customer_part_number}
              </div>
              <div>
                <span className="font-medium">Order Quantity:</span> {order.order_quantity}
              </div>
              <div>
                <span className="font-medium">Customer:</span> {order.customer?.name || 'Unknown'}
              </div>
              <div>
                <span className="font-medium">PO Date:</span> {order.po_date || 'Unknown'}
              </div>
              <div className="col-span-2">
                <span className="font-medium">Description:</span> {order.bpi_description}
              </div>
            </div>
          </div>

          {/* Cancellation Reason */}
          <div className="space-y-3">
            <Label>Reason for Cancellation *</Label>
            <div className="grid grid-cols-1 gap-2">
              {CANCEL_REASONS.map((reason) => (
                <div
                  key={reason.id}
                  className={cn(
                    "flex items-start justify-between p-3 border rounded-lg cursor-pointer transition-colors",
                    selectedReason === reason.id 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-200 bg-white hover:bg-gray-50"
                  )}
                  onClick={() => setSelectedReason(reason.id)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={cn(
                      "h-4 w-4 rounded-full border-2 flex-shrink-0 mt-0.5",
                      selectedReason === reason.id 
                        ? "border-blue-600 bg-blue-600" 
                        : "border-gray-300"
                    )}>
                      {selectedReason === reason.id && (
                        <div className="h-full w-full rounded-full bg-white scale-50" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">{reason.label}</div>
                      <div className="text-sm text-gray-600">{reason.description}</div>
                    </div>
                  </div>
                  
                  <Badge 
                    variant="outline" 
                    className={cn("text-xs", getSeverityColor(reason.severity))}
                  >
                    {reason.severity} impact
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Reason Input */}
          {selectedReason === 'other' && (
            <div className="space-y-2">
              <Label htmlFor="custom-reason">Please specify the reason *</Label>
              <textarea
                id="custom-reason"
                placeholder="Describe the specific reason for cancellation..."
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
                rows={3}
                required
              />
            </div>
          )}

          {/* Additional Details */}
          <div className="space-y-2">
            <Label htmlFor="additional-details">Additional Details (Optional)</Label>
            <textarea
              id="additional-details"
              placeholder="Any additional context, impacts, or special considerations..."
              value={additionalDetails}
              onChange={(e) => setAdditionalDetails(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
              rows={3}
            />
          </div>

          {/* Urgency and Notifications */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <Label>Urgency Level</Label>
              <div className="space-y-2">
                {[
                  { id: 'standard', label: 'Standard Process', desc: 'Normal review timeline' },
                  { id: 'urgent', label: 'Urgent Request', desc: 'Expedited review needed' }
                ].map((urgency) => (
                  <div
                    key={urgency.id}
                    className={cn(
                      "flex items-center space-x-3 p-2 border rounded cursor-pointer",
                      urgencyLevel === urgency.id 
                        ? "border-blue-500 bg-blue-50" 
                        : "border-gray-200 hover:bg-gray-50"
                    )}
                    onClick={() => setUrgencyLevel(urgency.id as any)}
                  >
                    <div className={cn(
                      "h-3 w-3 rounded-full",
                      urgencyLevel === urgency.id ? "bg-blue-600" : "bg-gray-300"
                    )} />
                    <div>
                      <div className="text-sm font-medium">{urgency.label}</div>
                      <div className="text-xs text-gray-600">{urgency.desc}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <Label>Notifications</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="notify-stakeholders"
                    checked={notifyStakeholders}
                    onChange={(e) => setNotifyStakeholders(e.target.checked)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="notify-stakeholders" className="text-sm">
                    Notify relevant stakeholders
                  </label>
                </div>
                <div className="text-xs text-gray-600 ml-7">
                  Will send notifications to procurement team, management, and customer if applicable
                </div>
              </div>
            </div>
          </div>

          {/* Impact Summary */}
          {selectedReason && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-3 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Cancellation Impact Summary
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-yellow-600" />
                  <div>
                    <div className="font-medium">Financial Impact</div>
                    <div className="text-yellow-700">{calculateFinancialImpact()}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <div>
                    <div className="font-medium">Timeline Impact</div>
                    <div className="text-yellow-700">{calculateTimelineImpact()}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4 text-yellow-600" />
                  <div>
                    <div className="font-medium">Severity Level</div>
                    <div className="text-yellow-700 capitalize">
                      {selectedReasonData?.severity} Impact
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500 flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Requested by: {user?.email || 'Unknown'}</span>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={handleSubmitCancellation}
                disabled={!isFormValid() || isSubmitting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting Request...
                  </>
                ) : (
                  <>
                    <X className="h-4 w-4 mr-2" />
                    Submit Cancellation Request
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}