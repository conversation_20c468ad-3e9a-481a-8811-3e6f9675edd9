import { useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { Building2, Package, TrendingUp } from 'lucide-react'
import { OrderLineWithDetails } from '@/hooks/useOrders'

interface GlobalPendingDisplayProps {
  orders: OrderLineWithDetails[]
  className?: string
}

interface CustomerPending {
  customer: string
  pending: number
  totalOrders: number
  percentage: number
}

export function GlobalPendingDisplay({ orders, className }: GlobalPendingDisplayProps) {
  const customerPendings = useMemo(() => {
    // Group orders by customer and calculate global pending
    const customerMap = new Map<string, { total: number, shipped: number, cancelled: number }>()
    
    orders.forEach(order => {
      const customer = order.customer_name || 'Unknown'
      const existing = customerMap.get(customer) || { total: 0, shipped: 0, cancelled: 0 }
      
      existing.total += order.quantities?.total_order_quantity || order.order_quantity || 0
      existing.shipped += order.quantities?.shipped_delivered || 0
      existing.cancelled += order.quantities?.cancelled || 0
      
      customerMap.set(customer, existing)
    })
    
    // Calculate pending for each customer
    const pendings: CustomerPending[] = []
    customerMap.forEach((data, customer) => {
      const pending = data.total - data.shipped - data.cancelled
      if (pending > 0) {
        pendings.push({
          customer,
          pending,
          totalOrders: data.total,
          percentage: (pending / data.total) * 100
        })
      }
    })
    
    // Sort by pending amount (highest first)
    return pendings.sort((a, b) => b.pending - a.pending)
  }, [orders])
  
  const totalGlobalPending = customerPendings.reduce((sum, cp) => sum + cp.pending, 0)
  
  if (customerPendings.length === 0) {
    return null
  }
  
  return (
    <Card className={className}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-900 flex items-center">
            <TrendingUp className="h-4 w-4 mr-2 text-orange-500" />
            Global Pending by Customer
          </h3>
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            Total: {totalGlobalPending} units
          </Badge>
        </div>
        
        <div className="space-y-2">
          {customerPendings.slice(0, 5).map((cp) => (
            <div key={cp.customer} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Building2 className="h-3.5 w-3.5 text-gray-400" />
                <span className="font-medium text-gray-900">{cp.customer}</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <div className="font-semibold text-orange-600">{cp.pending}</div>
                  <div className="text-xs text-gray-500">of {cp.totalOrders}</div>
                </div>
                <div className="w-20">
                  <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div 
                      className="bg-orange-500 h-full transition-all duration-300"
                      style={{ width: `${cp.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {customerPendings.length > 5 && (
            <div className="text-xs text-gray-500 text-center pt-1">
              +{customerPendings.length - 5} more customers
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}

// Inline display for specific customer
export function GlobalPendingBadge({ customer, orders }: { customer: string, orders: OrderLineWithDetails[] }) {
  const pending = useMemo(() => {
    let total = 0
    let shipped = 0
    let cancelled = 0
    
    orders.forEach(order => {
      if (order.customer_name === customer) {
        total += order.quantities?.total_order_quantity || order.order_quantity || 0
        shipped += order.quantities?.shipped_delivered || 0
        cancelled += order.quantities?.cancelled || 0
      }
    })
    
    return total - shipped - cancelled
  }, [customer, orders])
  
  if (pending <= 0) return null
  
  return (
    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
      <Package className="h-3 w-3 mr-1" />
      Global Pending: {pending}
    </Badge>
  )
}