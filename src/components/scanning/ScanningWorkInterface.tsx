import { useState, useRef } from 'react'
import { Scan, Camera, CheckCircle2, XCircle, PauseCircle, Volume2, VolumeX, RotateCcw, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useBarcodeScanner } from '@/hooks/useBarcodeScanner'
import { useOrders, OrderLineWithDetails } from '@/hooks/useOrders'
import { useQuantityTracking, QuantityState } from '@/hooks/useQuantityTracking'
import { getCTDetails } from '@/hooks/useCTNumbers'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import CameraCapture from './CameraCapture'

interface ScanningWorkInterfaceProps {
  workType: 'kitting' | 'qc'
  onWorkComplete?: (ctNumber: string, action: WorkAction) => void
  className?: string
}

interface WorkAction {
  type: 'complete' | 'hold' | 'reject'
  reason?: string
  note?: string
  photoUrl?: string
}

// Predefined reasons for holds and rejections
const KITTING_HOLD_REASONS = [
  'Damaged part received',
  'Missing component', 
  'Wrong part delivered',
  'Packaging damaged',
  'Quantity mismatch',
  'Awaiting supervisor check',
  'Label unclear',
  'Other (specify)'
]

const QC_REJECT_REASONS = [
  'Physical damage',
  'Wrong model/variant',
  'Quality issue',
  'Missing accessories',
  'Cosmetic defects',
  'Functional failure',
  'Label mismatch',
  'Other (specify)'
]

export default function ScanningWorkInterface({ 
  workType, 
  onWorkComplete,
  className = ''
}: ScanningWorkInterfaceProps) {
  const [currentCT, setCurrentCT] = useState<string>('')
  const [currentOrder, setCurrentOrder] = useState<OrderLineWithDetails | null>(null)
  const [workAction, setWorkAction] = useState<WorkAction | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [audioEnabled, setAudioEnabled] = useState(true)
  const [manualInput, setManualInput] = useState('')
  const [showActionButtons, setShowActionButtons] = useState(false)
  
  const cameraRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  const { data: orders } = useOrders()
  const { transitionQuantity } = useQuantityTracking()
  
  // Auto-scan mode with barcode scanner
  const { isScanning } = useBarcodeScanner({
    onScan: handleBarcodeScan,
    autoStart: true
  })

  // Play audio feedback
  const playFeedback = (type: 'success' | 'error' | 'warning') => {
    if (!audioEnabled) return
    
    // Create audio context for different sounds
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
    const audioContext = new AudioContextClass()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)
    
    // Different frequencies for different feedback types
    switch (type) {
      case 'success':
        oscillator.frequency.value = 800
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)
        break
      case 'error':
        oscillator.frequency.value = 300
        gainNode.gain.setValueAtTime(0.5, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5)
        break
      case 'warning':
        oscillator.frequency.value = 600
        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)
        break
    }
    
    oscillator.start()
    oscillator.stop(audioContext.currentTime + 0.5)
  }

  // Handle barcode scan
  async function handleBarcodeScan(scannedCode: string) {
    console.log('🔍 handleBarcodeScan called:', { scannedCode, isProcessing })
    if (isProcessing) {
      console.log('⚠️ Already processing, skipping scan')
      return
    }

    try {
      console.log('🚀 Starting scan processing')
      setIsProcessing(true)

      // Validate CT number format (14 digits, alphanumeric, uppercase)
      const ctPattern = /^[A-Z0-9]{14}$/
      if (!ctPattern.test(scannedCode.toUpperCase())) {
        console.log('❌ CT format validation failed:', scannedCode)
        playFeedback('error')
        toast.error('Invalid CT number format. Must be 14 alphanumeric characters.')
        return
      }

      const ctNumber = scannedCode.toUpperCase()

      // Get CT details and find associated order
      const ctDetails = await getCTDetails(ctNumber)
      if (!ctDetails) {
        playFeedback('error')
        toast.error('CT number not found in system')
        return
      }

      // Find order by order_line_id
      const order = orders?.find(o => o.id === ctDetails.order_line_id)
      if (!order) {
        playFeedback('error')
        toast.error(`Order not found for CT ${ctNumber}. Order Line ID: ${ctDetails.order_line_id}`)
        console.error('Order not found:', {
          ctNumber,
          orderLineId: ctDetails.order_line_id,
          orderUid: ctDetails.order_line?.uid,
          availableOrderIds: orders?.map(o => o.id)
        })
        return
      }

      // Validate CT is in correct state for this workstation
      const validStates = getValidStatesForWorkType(workType)
      const ctState = await getCurrentCTState(order, ctNumber)

      if (!validStates.includes(ctState)) {
        playFeedback('warning')
        toast.warning(`CT ${ctNumber} is not ready for ${workType}. Current state: ${ctState}`)
        console.log('State validation failed:', {
          ctNumber,
          currentState: ctState,
          validStates,
          workType
        })
        return
      }

      // Success - set up work interface
      setCurrentCT(ctNumber)
      setCurrentOrder(order)
      setShowActionButtons(true)
      playFeedback('success')
      toast.success(`CT ${ctNumber} ready for ${workType}`)

    } catch (error) {
      console.error('❌ Error processing scan:', error)
      playFeedback('error')
      toast.error('Error processing scan')
    } finally {
      console.log('🏁 Scan processing complete, resetting isProcessing')
      setIsProcessing(false)
    }
  }

  // Handle manual CT input
  function handleManualInput() {
    if (manualInput.trim()) {
      handleBarcodeScan(manualInput.trim())
      setManualInput('')
    }
  }

  // Get valid states for work type
  function getValidStatesForWorkType(type: 'kitting' | 'qc'): string[] {
    switch (type) {
      case 'kitting':
        return ['awaiting_kitting_packing', 'in_kitting_packing', 'on_hold_at_kitting_packing']
      case 'qc':
        return ['kitted_packed_awaiting_screening_qc', 'in_screening_qc', 'on_hold_at_screening_qc']
      default:
        return []
    }
  }

  // Get current CT state by checking quantity logs
  async function getCurrentCTState(order: OrderLineWithDetails, ctNumber: string): Promise<string> {
    try {
      // Query quantity_logs to find the most recent state for this CT
      const { data, error } = await supabase
        .from('quantity_logs')
        .select('to_state')
        .eq('order_line_id', order.id)
        .contains('associated_ct_numbers', [ctNumber])
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error) {
        console.error('Error fetching CT state:', error)
        // Fallback to checking order quantities
        const quantities = order.quantities
        if (quantities) {
          if (workType === 'kitting') {
            if (quantities.in_kitting_packing > 0) return 'in_kitting_packing'
            if (quantities.on_hold_kitting > 0) return 'on_hold_at_kitting_packing'
            if (quantities.awaiting_kitting_packing > 0) return 'awaiting_kitting_packing'
          } else if (workType === 'qc') {
            if (quantities.in_screening_qc > 0) return 'in_screening_qc'
            if (quantities.on_hold_qc > 0) return 'on_hold_at_screening_qc'
            if (quantities.kitted_awaiting_qc > 0) return 'kitted_packed_awaiting_screening_qc'
          }
        }
        // Final fallback
        return workType === 'kitting' ? 'awaiting_kitting_packing' : 'kitted_packed_awaiting_screening_qc'
      }

      return data.to_state
    } catch (err) {
      console.error('Failed to get CT state:', err)
      // Fallback to default
      return workType === 'kitting' ? 'awaiting_kitting_packing' : 'kitted_packed_awaiting_screening_qc'
    }
  }

  // Handle work completion
  async function handleWorkComplete(action: WorkAction) {
    if (!currentCT || !currentOrder) return
    
    try {
      setIsProcessing(true)
      
      // Determine target state based on action
      let targetState = ''
      switch (action.type) {
        case 'complete':
          targetState = workType === 'kitting' 
            ? 'kitted_packed_awaiting_screening_qc'
            : 'screening_qc_passed_ready_for_invoice'
          break
        case 'hold':
          targetState = workType === 'kitting'
            ? 'on_hold_at_kitting_packing'
            : 'on_hold_at_screening_qc'
          break
        case 'reject':
          targetState = workType === 'kitting'
            ? 'pending_procurement_arrangement' // Back to procurement
            : 'screening_qc_rejected'
          break
      }
      
      // Get current state before transition
      const fromState = await getCurrentCTState(currentOrder, currentCT)
      
      // Perform quantity transition
      const result = await transitionQuantity({
        orderLineId: currentOrder.id,
        fromState: fromState as QuantityState,
        toState: targetState as QuantityState,
        quantity: 1,
        ctNumbers: [currentCT],
        reason: action.reason || `${workType} ${action.type}`
      })
      
      if (result.success) {
        playFeedback('success')
        toast.success(`CT ${currentCT} ${action.type} successfully`)
        
        // Call completion callback
        onWorkComplete?.(currentCT, action)
        
        // Reset for next scan
        resetWorkInterface()
      } else {
        playFeedback('error')
        toast.error(result.message || 'Failed to update quantity state')
      }
      
    } catch (error) {
      console.error('Error completing work:', error)
      playFeedback('error')
      toast.error('Error completing work action')
    } finally {
      setIsProcessing(false)
    }
  }

  // Reset work interface for next scan
  function resetWorkInterface() {
    setCurrentCT('')
    setCurrentOrder(null)
    setWorkAction(null)
    setShowActionButtons(false)
    setManualInput('')
  }

  // Get work instructions for current order/work type
  function getWorkInstructions(): string[] {
    if (!currentOrder) return []
    
    // This would come from database eventually
    const defaultInstructions = {
      kitting: [
        '1. Check for physical damage',
        '2. Verify part number matches order',
        '3. Pack with anti-static protection',
        '4. Apply CT label to designated area',
        '5. Place in kitting area'
      ],
      qc: [
        '1. Compare with master image',
        '2. Check for cosmetic defects',
        '3. Verify all accessories included',
        '4. Test basic functionality',
        '5. Confirm label placement'
      ]
    }
    
    return defaultInstructions[workType] || []
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header Bar */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <h1 className="text-xl font-semibold text-gray-900">
              {workType === 'kitting' ? 'Kitting Station' : 'QC Station'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={audioEnabled ? 'default' : 'outline'}
              size="sm"
              onClick={() => setAudioEnabled(!audioEnabled)}
            >
              {audioEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            </Button>
            
            <Badge variant={isScanning ? 'default' : 'secondary'}>
              {isScanning ? 'Scanning Active' : 'Scan Paused'}
            </Badge>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Scanning Input Area */}
        <Card className="p-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
              <Scan className="w-8 h-8 text-blue-600" />
            </div>
            
            <h2 className="text-lg font-medium text-gray-900">
              {currentCT ? `Scanning: ${currentCT}` : 'Scan CT Number'}
            </h2>
            
            {/* Manual Input Fallback */}
            <div className="flex space-x-2">
              <Input
                placeholder="Manual CT entry..."
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value.toUpperCase())}
                onKeyPress={(e) => e.key === 'Enter' && handleManualInput()}
                className="text-center text-lg"
              />
              <Button onClick={handleManualInput} disabled={!manualInput.trim()}>
                <Zap className="w-4 h-4" />
              </Button>
            </div>
            
            {isProcessing && (
              <div className="space-y-2">
                <div className="text-blue-600">Processing scan...</div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('🔄 Manual reset triggered')
                    setIsProcessing(false)
                    resetWorkInterface()
                  }}
                  className="text-xs"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset if Stuck
                </Button>
              </div>
            )}
          </div>
        </Card>

        {/* Current Work Display */}
        {currentOrder && showActionButtons && (
          <div className="space-y-4">
            {/* Order Information */}
            <Card className="p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">Order</div>
                  <div className="font-medium">{currentOrder.uid}</div>
                </div>
                <div>
                  <div className="text-gray-500">Part Number</div>
                  <div className="font-medium">{currentOrder.customer_part_number}</div>
                </div>
                <div className="col-span-2">
                  <div className="text-gray-500">Description</div>
                  <div className="font-medium">{currentOrder.bpi_description}</div>
                </div>
              </div>
            </Card>

            {/* Work Instructions */}
            <Card className="p-4">
              <h3 className="font-medium text-gray-900 mb-3">Work Instructions</h3>
              <div className="space-y-2">
                {getWorkInstructions().map((instruction, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xs font-medium mt-0.5">
                      {index + 1}
                    </div>
                    <div className="flex-1 text-sm text-gray-700">{instruction}</div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Action Buttons - Large Touch Targets */}
            <div className="grid grid-cols-1 gap-4">
              <Button
                size="lg"
                className="h-16 text-lg"
                onClick={() => handleWorkComplete({ type: 'complete' })}
                disabled={isProcessing}
              >
                <CheckCircle2 className="w-6 h-6 mr-2" />
                Complete {workType === 'kitting' ? 'Kitting' : 'QC Check'}
              </Button>
              
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  size="lg"
                  className="h-14"
                  onClick={() => setWorkAction({ type: 'hold' })}
                  disabled={isProcessing}
                >
                  <PauseCircle className="w-5 h-5 mr-2" />
                  Hold
                </Button>
                
                <Button
                  variant="destructive"
                  size="lg"
                  className="h-14"
                  onClick={() => setWorkAction({ type: 'reject' })}
                  disabled={isProcessing}
                >
                  <XCircle className="w-5 h-5 mr-2" />
                  Reject
                </Button>
              </div>

              {/* QC Photo Capture */}
              {workType === 'qc' && (
                <CameraCapture
                  onPhotoCapture={() => {
                    console.log('Photo captured for CT:', currentCT)
                    // TODO: Save photo to order/CT record
                    toast.success('Photo captured and saved')
                  }}
                  trigger={
                    <Button
                      variant="outline"
                      size="lg"
                      className="h-14"
                      disabled={isProcessing}
                    >
                      <Camera className="w-5 h-5 mr-2" />
                      Capture Photo
                    </Button>
                  }
                />
              )}
            </div>

            {/* Reset Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={resetWorkInterface}
              className="w-full"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Clear & Scan Next
            </Button>
          </div>
        )}

        {/* Hold/Reject Reason Modal */}
        {workAction && (workAction.type === 'hold' || workAction.type === 'reject') && (
          <Card className="p-4 border-orange-200 bg-orange-50">
            <h3 className="font-medium text-gray-900 mb-3">
              {workAction.type === 'hold' ? 'Hold Reason' : 'Rejection Reason'}
            </h3>
            
            <div className="space-y-3">
              <Select
                value={workAction.reason}
                onValueChange={(reason) => setWorkAction({ ...workAction, reason })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select reason..." />
                </SelectTrigger>
                <SelectContent>
                  {(workAction.type === 'hold' ? KITTING_HOLD_REASONS : QC_REJECT_REASONS).map((reason) => (
                    <SelectItem key={reason} value={reason}>
                      {reason}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {workAction.reason === 'Other (specify)' && (
                <Textarea
                  placeholder="Specify reason..."
                  value={workAction.note || ''}
                  onChange={(e) => setWorkAction({ ...workAction, note: e.target.value })}
                />
              )}
              
              <div className="flex space-x-2">
                <Button
                  className="flex-1"
                  onClick={() => handleWorkComplete(workAction)}
                  disabled={!workAction.reason || isProcessing}
                >
                  Confirm {workAction.type === 'hold' ? 'Hold' : 'Rejection'}
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => setWorkAction(null)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Hidden camera elements for future photo capture */}
      <video ref={cameraRef} style={{ display: 'none' }} />
      <canvas ref={canvasRef} style={{ display: 'none' }} />
    </div>
  )
}