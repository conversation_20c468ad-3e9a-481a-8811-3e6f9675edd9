import React, { useState, useRef, useCallback } from 'react'
import { Camera, X, RotateCcw, Download, Check } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog'
import { toast } from 'sonner'

interface CameraCaptureProps {
  onPhotoCapture?: (photoDataUrl: string) => void
  disabled?: boolean
  trigger?: React.ReactNode
}

export default function CameraCapture({ 
  onPhotoCapture, 
  disabled = false,
  trigger
}: CameraCaptureProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isCameraActive, setIsCameraActive] = useState(false)
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null)
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment')
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  // Start camera stream
  const startCamera = useCallback(async () => {
    try {
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        streamRef.current = stream
        setIsCameraActive(true)
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
      toast.error('Could not access camera. Please check permissions.')
    }
  }, [facingMode])

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    setIsCameraActive(false)
  }, [])

  // Capture photo from video stream
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext('2d')

    if (!context) return

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // Draw current video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height)

    // Convert to data URL
    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8)
    setCapturedPhoto(photoDataUrl)
    
    // Stop camera after capture
    stopCamera()
    
    toast.success('Photo captured successfully')
  }, [stopCamera])

  // Flip camera (front/back)
  const flipCamera = useCallback(() => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user'
    setFacingMode(newFacingMode)
    
    if (isCameraActive) {
      stopCamera()
      setTimeout(() => startCamera(), 100)
    }
  }, [facingMode, isCameraActive, stopCamera, startCamera])

  // Save captured photo
  const savePhoto = useCallback(() => {
    if (!capturedPhoto) return

    onPhotoCapture?.(capturedPhoto)
    setIsOpen(false)
    setCapturedPhoto(null)
    toast.success('Photo saved successfully')
  }, [capturedPhoto, onPhotoCapture])

  // Download photo (for testing/backup)
  const downloadPhoto = useCallback(() => {
    if (!capturedPhoto) return

    const link = document.createElement('a')
    link.download = `qc-photo-${Date.now()}.jpg`
    link.href = capturedPhoto
    link.click()
  }, [capturedPhoto])

  // Reset and retake photo
  const retakePhoto = useCallback(() => {
    setCapturedPhoto(null)
    startCamera()
  }, [startCamera])

  // Handle dialog open/close
  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open)
    if (open) {
      startCamera()
    } else {
      stopCamera()
      setCapturedPhoto(null)
    }
  }, [startCamera, stopCamera])

  const defaultTrigger = (
    <Button
      variant="outline"
      size="lg"
      disabled={disabled}
      className="w-full"
    >
      <Camera className="w-5 h-5 mr-2" />
      Capture Photo
    </Button>
  )

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>QC Photo Capture</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {capturedPhoto ? (
            // Show captured photo with options
            <Card className="p-4">
              <div className="text-center space-y-4">
                <img
                  src={capturedPhoto}
                  alt="Captured photo"
                  className="max-w-full max-h-96 mx-auto rounded-lg border"
                />
                
                <div className="flex justify-center space-x-3">
                  <Button onClick={retakePhoto} variant="outline">
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Retake
                  </Button>
                  
                  <Button onClick={downloadPhoto} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  
                  <Button onClick={savePhoto} className="bg-green-600 hover:bg-green-700">
                    <Check className="w-4 h-4 mr-2" />
                    Save Photo
                  </Button>
                </div>
              </div>
            </Card>
          ) : (
            // Show camera interface
            <Card className="p-4">
              <div className="space-y-4">
                {/* Camera Viewfinder */}
                <div className="relative bg-black rounded-lg overflow-hidden">
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-96 object-cover"
                  />
                  
                  {/* Camera not active overlay */}
                  {!isCameraActive && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
                      <div className="text-center text-white">
                        <Camera className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <p>Camera starting...</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Capture Grid Overlay */}
                  {isCameraActive && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="grid grid-cols-3 grid-rows-3 h-full w-full opacity-30">
                        {Array.from({ length: 9 }).map((_, i) => (
                          <div key={i} className="border border-white border-opacity-50" />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Camera Controls */}
                <div className="flex justify-center space-x-4">
                  <Button
                    onClick={flipCamera}
                    variant="outline"
                    size="lg"
                    disabled={!isCameraActive}
                  >
                    <RotateCcw className="w-5 h-5 mr-2" />
                    Flip Camera
                  </Button>
                  
                  <Button
                    onClick={capturePhoto}
                    size="lg"
                    disabled={!isCameraActive}
                    className="bg-blue-600 hover:bg-blue-700 min-w-32"
                  >
                    <Camera className="w-5 h-5 mr-2" />
                    Capture
                  </Button>
                  
                  <Button
                    onClick={() => setIsOpen(false)}
                    variant="outline"
                    size="lg"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Cancel
                  </Button>
                </div>
                
                {/* Tips */}
                <div className="text-sm text-gray-600 text-center space-y-1">
                  <p>• Position part clearly in frame</p>
                  <p>• Ensure good lighting for QC comparison</p>
                  <p>• Use grid lines for proper alignment</p>
                </div>
              </div>
            </Card>
          )}
        </div>
        
        {/* Hidden canvas for photo capture */}
        <canvas ref={canvasRef} className="hidden" />
      </DialogContent>
    </Dialog>
  )
}