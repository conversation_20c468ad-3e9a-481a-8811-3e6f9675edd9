/**
 * Error Management Dashboard for Admin Interface
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Download, 
  Trash2, 
  RefreshCw,
  TrendingUp,
  Clock,
  User,
  Globe
} from 'lucide-react'
import { errorLogger, ErrorReport } from '@/utils/errorLogger'
import { useErrorRecovery } from '@/utils/errorRecovery'

export function ErrorManagement() {
  const [errors, setErrors] = useState<ErrorReport[]>([])
  const [summary, setSummary] = useState<any>(null)
  const [selectedError, setSelectedError] = useState<ErrorReport | null>(null)
  const { attemptRecovery, isRecovering } = useErrorRecovery()

  useEffect(() => {
    loadErrors()
    const interval = setInterval(loadErrors, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadErrors = () => {
    const localErrors = errorLogger.getLocalErrors()
    const errorSummary = errorLogger.getErrorSummary()
    setErrors(localErrors)
    setSummary(errorSummary)
  }

  const handleClearErrors = () => {
    errorLogger.clearLocalErrors()
    loadErrors()
  }

  const handleExportErrors = () => {
    const exportData = errorLogger.exportErrors()
    const blob = new Blob([exportData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `mini-erp-errors-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleTestRecovery = async () => {
    const testError = new Error('Test recovery mechanism')
    await attemptRecovery(testError, 'network', { source: 'manual-test' })
    loadErrors()
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'destructive'
      case 'warning': return 'warning'
      case 'info': return 'default'
      default: return 'secondary'
    }
  }

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return <XCircle className="w-4 h-4" />
      case 'warning': return <AlertTriangle className="w-4 h-4" />
      case 'info': return <CheckCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const truncateText = (text: string, maxLength: number = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              All time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Errors</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {summary?.byLevel?.error || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warnings</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">
              {summary?.byLevel?.warning || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Monitor closely
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary?.recent?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Last hour
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex flex-wrap gap-4">
        <Button onClick={loadErrors} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
        
        <Button onClick={handleExportErrors} variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Errors
        </Button>
        
        <Button 
          onClick={handleTestRecovery} 
          variant="outline"
          disabled={isRecovering}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isRecovering ? 'animate-spin' : ''}`} />
          Test Recovery
        </Button>
        
        <Button onClick={handleClearErrors} variant="destructive">
          <Trash2 className="w-4 h-4 mr-2" />
          Clear All
        </Button>
      </div>

      {/* Error List */}
      <Card>
        <CardHeader>
          <CardTitle>Error Log</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {errors.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
                <p>No errors recorded. System is healthy!</p>
              </div>
            ) : (
              errors.slice().reverse().map((error) => (
                <div
                  key={error.id}
                  className="border rounded-lg p-4 hover:bg-accent cursor-pointer transition-colors"
                  onClick={() => setSelectedError(error)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      {getLevelIcon(error.level)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge variant={getLevelColor(error.level) as any}>
                            {error.level.toUpperCase()}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {formatTimestamp(error.timestamp)}
                          </span>
                          {error.userId && (
                            <Badge variant="outline">
                              <User className="w-3 h-3 mr-1" />
                              User
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm font-medium">
                          {truncateText(error.message)}
                        </p>
                        {error.context && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Context: {JSON.stringify(error.context).substring(0, 100)}...
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {error.url && (
                        <Badge variant="outline">
                          <Globe className="w-3 h-3 mr-1" />
                          {new URL(error.url).pathname}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Detail Modal */}
      {selectedError && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-4xl max-h-[80vh] overflow-auto m-4">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  {getLevelIcon(selectedError.level)}
                  <span>Error Details</span>
                  <Badge variant={getLevelColor(selectedError.level) as any}>
                    {selectedError.level.toUpperCase()}
                  </Badge>
                </CardTitle>
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedError(null)}
                >
                  Close
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Error Message</h4>
                <p className="text-sm bg-accent p-3 rounded">{selectedError.message}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Timestamp</h4>
                  <p className="text-sm">{formatTimestamp(selectedError.timestamp)}</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Error ID</h4>
                  <p className="text-sm font-mono">{selectedError.id}</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Session ID</h4>
                  <p className="text-sm font-mono">{selectedError.sessionId}</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">User ID</h4>
                  <p className="text-sm">{selectedError.userId || 'Anonymous'}</p>
                </div>
              </div>

              {selectedError.url && (
                <div>
                  <h4 className="font-semibold mb-2">URL</h4>
                  <p className="text-sm break-all">{selectedError.url}</p>
                </div>
              )}

              {selectedError.userAgent && (
                <div>
                  <h4 className="font-semibold mb-2">User Agent</h4>
                  <p className="text-sm break-all">{selectedError.userAgent}</p>
                </div>
              )}

              {selectedError.context && (
                <div>
                  <h4 className="font-semibold mb-2">Context</h4>
                  <pre className="text-xs bg-accent p-3 rounded overflow-auto">
                    {JSON.stringify(selectedError.context, null, 2)}
                  </pre>
                </div>
              )}

              {selectedError.stack && (
                <div>
                  <h4 className="font-semibold mb-2">Stack Trace</h4>
                  <pre className="text-xs bg-accent p-3 rounded overflow-auto font-mono">
                    {selectedError.stack}
                  </pre>
                </div>
              )}

              {selectedError.componentStack && (
                <div>
                  <h4 className="font-semibold mb-2">Component Stack</h4>
                  <pre className="text-xs bg-accent p-3 rounded overflow-auto">
                    {selectedError.componentStack}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}