import { useState } from 'react'
import { useInvoiceTemplates, InvoiceTemplate, InvoiceTemplateFormData } from '@/hooks/useInvoiceTemplates'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import {
  FileText,
  Plus,
  Edit,
  Trash2,
  Star,
  Eye,
  Search,
  Filter,
  Settings,
  User,
  Calendar,
  CheckSquare,
  Square
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function InvoiceTemplateManager() {
  const {
    templates,
    isLoading,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setAsDefault,
    canCreateTemplates,
    canDeleteTemplates
  } = useInvoiceTemplates()

  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null)
  const [templateFormData, setTemplateFormData] = useState<InvoiceTemplateFormData>({
    name: '',
    customer_name: '',
    is_default: false,
    payment_terms: 'Net 30',
    notes: '',
    header_content: '',
    footer_content: '',
    template_data: {
      showQuantity: true,
      showUnitPrice: true,
      showLineTotal: true,
      showCTNumbers: true,
      logoPosition: 'top-left'
    },
    is_active: true
  })

  // Get unique customers
  const customers = Array.from(new Set(templates
    .map(t => t.customer_name)
    .filter(name => name)
  )).sort()

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.customer_name && template.customer_name.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCustomer = selectedCustomer === 'all' || 
      (selectedCustomer === 'general' && !template.customer_name) ||
      template.customer_name === selectedCustomer
    
    return matchesSearch && matchesCustomer
  })

  // Reset form
  const resetForm = () => {
    setTemplateFormData({
      name: '',
      customer_name: '',
      is_default: false,
      payment_terms: 'Net 30',
      notes: '',
      header_content: '',
      footer_content: '',
      template_data: {
        showQuantity: true,
        showUnitPrice: true,
        showLineTotal: true,
        showCTNumbers: true,
        logoPosition: 'top-left'
      },
      is_active: true
    })
  }

  // Open edit modal
  const openEditModal = (template: InvoiceTemplate) => {
    setSelectedTemplate(template)
    setTemplateFormData({
      name: template.name,
      customer_name: template.customer_name || '',
      is_default: template.is_default,
      payment_terms: template.payment_terms,
      notes: template.notes || '',
      header_content: template.header_content || '',
      footer_content: template.footer_content || '',
      template_data: template.template_data,
      is_active: template.is_active
    })
    setShowEditModal(true)
  }

  // Open view modal
  const openViewModal = (template: InvoiceTemplate) => {
    setSelectedTemplate(template)
    setShowViewModal(true)
  }

  // Handle create template
  const handleCreate = async () => {
    if (!templateFormData.name.trim()) {
      toast.error('Please enter a template name')
      return
    }

    const success = await createTemplate(templateFormData)
    if (success) {
      setShowCreateModal(false)
      resetForm()
    }
  }

  // Handle update template
  const handleUpdate = async () => {
    if (!selectedTemplate || !templateFormData.name.trim()) {
      toast.error('Please enter a template name')
      return
    }

    const success = await updateTemplate(selectedTemplate.id, templateFormData)
    if (success) {
      setShowEditModal(false)
      setSelectedTemplate(null)
      resetForm()
    }
  }

  // Handle delete template
  const handleDelete = async (template: InvoiceTemplate) => {
    if (window.confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
      await deleteTemplate(template.id)
    }
  }

  // Handle set as default
  const handleSetDefault = async (template: InvoiceTemplate) => {
    await setAsDefault(template.id)
  }

  // Toggle template configuration option
  const toggleConfigOption = (option: string) => {
    setTemplateFormData(prev => ({
      ...prev,
      template_data: {
        ...prev.template_data,
        [option]: !prev.template_data[option]
      }
    }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Invoice Templates</h2>
          <p className="text-sm text-gray-600">
            Manage invoice templates for different customers and use cases
          </p>
        </div>
        {canCreateTemplates && (
          <Button onClick={() => setShowCreateModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Template
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search templates by name or customer..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Select value={selectedCustomer} onValueChange={setSelectedCustomer}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Templates</SelectItem>
                  <SelectItem value="general">General Templates</SelectItem>
                  {customers.map(customer => (
                    <SelectItem key={customer} value={customer}>
                      {customer}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full text-center py-8">Loading templates...</div>
        ) : filteredTemplates.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-500">
              {searchTerm || selectedCustomer !== 'all' 
                ? 'Try adjusting your search or filter criteria' 
                : 'Get started by creating your first invoice template'}
            </p>
          </div>
        ) : (
          filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      {template.name}
                      {template.is_default && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </CardTitle>
                    {template.customer_name && (
                      <Badge variant="outline" className="mt-1">
                        <User className="h-3 w-3 mr-1" />
                        {template.customer_name}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Template Info */}
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center gap-1 mb-1">
                      <Settings className="h-3 w-3" />
                      Payment Terms: {template.payment_terms}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      Created: {new Date(template.created_at).toLocaleDateString()}
                    </div>
                  </div>

                  {template.notes && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {template.notes}
                    </p>
                  )}

                  {/* Configuration Preview */}
                  <div>
                    <div className="text-xs font-medium text-gray-700 mb-2">Configuration:</div>
                    <div className="flex flex-wrap gap-1">
                      {Object.entries(template.template_data)
                        .filter(([_, value]) => value === true)
                        .slice(0, 3)
                        .map(([key, _]) => (
                          <Badge key={key} variant="secondary" className="text-xs">
                            {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                          </Badge>
                        ))}
                      {Object.values(template.template_data).filter(v => v === true).length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{Object.values(template.template_data).filter(v => v === true).length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openViewModal(template)}
                      className="flex-1"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    {canCreateTemplates && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditModal(template)}
                        className="flex-1"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    )}
                  </div>

                  {/* Additional Actions */}
                  <div className="flex gap-2">
                    {canCreateTemplates && !template.is_default && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(template)}
                        className="flex-1 text-yellow-600 hover:text-yellow-700"
                      >
                        <Star className="h-3 w-3 mr-1" />
                        Set Default
                      </Button>
                    )}
                    {canDeleteTemplates && !template.is_default && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(template)}
                        className="flex-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create Template Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Invoice Template</DialogTitle>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Template Name *</Label>
                <Input
                  id="name"
                  value={templateFormData.name}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., HP Customer Invoice Template"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="customer">Customer (Optional)</Label>
                <Input
                  id="customer"
                  value={templateFormData.customer_name}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  placeholder="Leave empty for general template"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="payment_terms">Payment Terms</Label>
                <Select 
                  value={templateFormData.payment_terms} 
                  onValueChange={(value) => setTemplateFormData(prev => ({ ...prev, payment_terms: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Net 15">Net 15</SelectItem>
                    <SelectItem value="Net 30">Net 30</SelectItem>
                    <SelectItem value="Net 45">Net 45</SelectItem>
                    <SelectItem value="Net 60">Net 60</SelectItem>
                    <SelectItem value="Due on Receipt">Due on Receipt</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={templateFormData.notes}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Template description and usage notes"
                  rows={3}
                />
              </div>
            </div>

            {/* Configuration Options */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium">Display Options</h4>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { key: 'showQuantity', label: 'Show Quantity' },
                  { key: 'showUnitPrice', label: 'Show Unit Price' },
                  { key: 'showLineTotal', label: 'Show Line Total' },
                  { key: 'showCTNumbers', label: 'Show CT Numbers' },
                  { key: 'showPartMapping', label: 'Show Part Mapping' },
                  { key: 'groupByCategory', label: 'Group by Category' }
                ].map(({ key, label }) => (
                  <div key={key} className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => toggleConfigOption(key)}
                    >
                      {templateFormData.template_data[key] ? (
                        <CheckSquare className="h-4 w-4 text-blue-600" />
                      ) : (
                        <Square className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                    <Label className="text-sm">{label}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => setTemplateFormData(prev => ({ ...prev, is_default: !prev.is_default }))}
              >
                {templateFormData.is_default ? (
                  <CheckSquare className="h-4 w-4 text-blue-600" />
                ) : (
                  <Square className="h-4 w-4 text-gray-400" />
                )}
              </button>
              <Label>Set as default template</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate}>
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Template Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Invoice Template</DialogTitle>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            {/* Same form content as create modal */}
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit_name">Template Name *</Label>
                <Input
                  id="edit_name"
                  value={templateFormData.name}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., HP Customer Invoice Template"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_customer">Customer (Optional)</Label>
                <Input
                  id="edit_customer"
                  value={templateFormData.customer_name}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  placeholder="Leave empty for general template"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_payment_terms">Payment Terms</Label>
                <Select 
                  value={templateFormData.payment_terms} 
                  onValueChange={(value) => setTemplateFormData(prev => ({ ...prev, payment_terms: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Net 15">Net 15</SelectItem>
                    <SelectItem value="Net 30">Net 30</SelectItem>
                    <SelectItem value="Net 45">Net 45</SelectItem>
                    <SelectItem value="Net 60">Net 60</SelectItem>
                    <SelectItem value="Due on Receipt">Due on Receipt</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_notes">Notes</Label>
                <Textarea
                  id="edit_notes"
                  value={templateFormData.notes}
                  onChange={(e) => setTemplateFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Template description and usage notes"
                  rows={3}
                />
              </div>
            </div>

            {/* Configuration Options */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium">Display Options</h4>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { key: 'showQuantity', label: 'Show Quantity' },
                  { key: 'showUnitPrice', label: 'Show Unit Price' },
                  { key: 'showLineTotal', label: 'Show Line Total' },
                  { key: 'showCTNumbers', label: 'Show CT Numbers' },
                  { key: 'showPartMapping', label: 'Show Part Mapping' },
                  { key: 'groupByCategory', label: 'Group by Category' }
                ].map(({ key, label }) => (
                  <div key={key} className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => toggleConfigOption(key)}
                    >
                      {templateFormData.template_data[key] ? (
                        <CheckSquare className="h-4 w-4 text-blue-600" />
                      ) : (
                        <Square className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                    <Label className="text-sm">{label}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => setTemplateFormData(prev => ({ ...prev, is_default: !prev.is_default }))}
              >
                {templateFormData.is_default ? (
                  <CheckSquare className="h-4 w-4 text-blue-600" />
                ) : (
                  <Square className="h-4 w-4 text-gray-400" />
                )}
              </button>
              <Label>Set as default template</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdate}>
              Update Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Template Modal */}
      <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {selectedTemplate?.name}
              {selectedTemplate?.is_default && (
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
              )}
            </DialogTitle>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Customer</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedTemplate.customer_name || 'General Template'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Payment Terms</h4>
                  <p className="text-sm text-gray-600 mt-1">{selectedTemplate.payment_terms}</p>
                </div>
              </div>
              
              {selectedTemplate.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Notes</h4>
                  <p className="text-sm text-gray-600 mt-1">{selectedTemplate.notes}</p>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Configuration</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(selectedTemplate.template_data).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 text-sm">
                      {value ? (
                        <CheckSquare className="h-3 w-3 text-green-600" />
                      ) : (
                        <Square className="h-3 w-3 text-gray-400" />
                      )}
                      <span className={cn(
                        value ? 'text-gray-900' : 'text-gray-500'
                      )}>
                        {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Created: {new Date(selectedTemplate.created_at).toLocaleDateString()}
                </div>
                {selectedTemplate.is_default && (
                  <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                    <Star className="h-3 w-3 mr-1" />
                    Default Template
                  </Badge>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewModal(false)}>
              Close
            </Button>
            {selectedTemplate && canCreateTemplates && (
              <Button onClick={() => {
                setShowViewModal(false)
                openEditModal(selectedTemplate)
              }}>
                <Edit className="h-3 w-3 mr-1" />
                Edit Template
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}