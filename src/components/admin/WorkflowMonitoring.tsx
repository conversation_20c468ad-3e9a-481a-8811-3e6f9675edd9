import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useWorkflowCoordinator } from '@/hooks/useWorkflowCoordinator'
import { workflowStateManager } from '@/services/WorkflowStateManager'
import {
  Activity,
  RefreshCw,
  AlertTriangle,
  Clock,
  TrendingUp,
  Network,
  Wifi,
  WifiOff,
  Trash2
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function WorkflowMonitoring() {
  const coordinator = useWorkflowCoordinator()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [realtimeEvents, setRealtimeEvents] = useState<any[]>([])

  // Subscribe to real-time workflow events for monitoring
  useEffect(() => {
    const unsubscribe = workflowStateManager.subscribe('*', (eventData) => {
      setRealtimeEvents(prev => [eventData, ...prev.slice(0, 49)]) // Keep last 50 events
    })

    return unsubscribe
  }, [])

  const handleRefreshAll = async () => {
    setIsRefreshing(true)
    await coordinator.refreshAllWorkflows()
    setIsRefreshing(false)
  }

  const handleClearStates = () => {
    coordinator.clearAllWorkflowStates()
    setRealtimeEvents([])
  }


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflow State Monitoring</h2>
          <p className="text-sm text-gray-600">
            Real-time monitoring and coordination of workflow states
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleClearStates}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear States
          </Button>
          <Button
            onClick={handleRefreshAll}
            disabled={isRefreshing}
            className="gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Refresh All
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-600">Connection Status</div>
                <div className={cn("text-lg font-bold", coordinator.isOnline ? "text-green-600" : "text-red-600")}>
                  {coordinator.isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
              {coordinator.isOnline ? (
                <Wifi className="h-6 w-6 text-green-600" />
              ) : (
                <WifiOff className="h-6 w-6 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-600">Total Tasks</div>
                <div className="text-lg font-bold">
                  {coordinator.workflowHealth.totalTasks}
                </div>
              </div>
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-600">In Progress</div>
                <div className="text-lg font-bold text-orange-600">
                  {coordinator.workflowHealth.inProgressTasks}
                </div>
              </div>
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium text-gray-600">Success Rate</div>
                <div className={cn("text-lg font-bold", 
                  coordinator.workflowHealth.successRate > 95 ? "text-green-600" : 
                  coordinator.workflowHealth.successRate > 80 ? "text-yellow-600" : "text-red-600"
                )}>
                  {coordinator.workflowHealth.successRate.toFixed(1)}%
                </div>
              </div>
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflow State Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Kitting Workflow */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              <span>Kitting Workflow</span>
              <Badge variant={coordinator.kittingState.isOnline ? "default" : "secondary"}>
                {coordinator.kittingState.isOnline ? "Online" : "Offline"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-600">Current Tasks</div>
                <div className="font-semibold">{coordinator.kittingState.currentTasks.length}</div>
              </div>
              <div>
                <div className="text-gray-600">In Progress</div>
                <div className="font-semibold text-orange-600">
                  {coordinator.kittingState.inProgressTasks.length}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Completed Today</div>
                <div className="font-semibold text-green-600">
                  {coordinator.kittingState.metrics.completedToday}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Avg. Time</div>
                <div className="font-semibold">
                  {coordinator.kittingState.metrics.averageTime.toFixed(1)}m
                </div>
              </div>
            </div>
            
            <div className="text-xs text-gray-500">
              Last sync: {new Date(coordinator.kittingState.lastSync).toLocaleTimeString()}
            </div>

            {coordinator.kittingState.failedTasks.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-2">
                <div className="text-xs font-medium text-red-800">
                  {coordinator.kittingState.failedTasks.length} failed task(s)
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* QC Workflow */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              <span>QC Workflow</span>
              <Badge variant={coordinator.qcState.isOnline ? "default" : "secondary"}>
                {coordinator.qcState.isOnline ? "Online" : "Offline"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-600">Current Tasks</div>
                <div className="font-semibold">{coordinator.qcState.currentTasks.length}</div>
              </div>
              <div>
                <div className="text-gray-600">In Progress</div>
                <div className="font-semibold text-orange-600">
                  {coordinator.qcState.inProgressTasks.length}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Completed Today</div>
                <div className="font-semibold text-green-600">
                  {coordinator.qcState.metrics.completedToday}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Avg. Time</div>
                <div className="font-semibold">
                  {coordinator.qcState.metrics.averageTime.toFixed(1)}m
                </div>
              </div>
            </div>
            
            <div className="text-xs text-gray-500">
              Last sync: {new Date(coordinator.qcState.lastSync).toLocaleTimeString()}
            </div>

            {coordinator.qcState.failedTasks.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-2">
                <div className="text-xs font-medium text-red-800">
                  {coordinator.qcState.failedTasks.length} failed task(s)
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Invoicing Workflow */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center justify-between">
              <span>Invoicing Workflow</span>
              <Badge variant={coordinator.invoicingState.isOnline ? "default" : "secondary"}>
                {coordinator.invoicingState.isOnline ? "Online" : "Offline"}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-600">Current Tasks</div>
                <div className="font-semibold">{coordinator.invoicingState.currentTasks.length}</div>
              </div>
              <div>
                <div className="text-gray-600">In Progress</div>
                <div className="font-semibold text-orange-600">
                  {coordinator.invoicingState.inProgressTasks.length}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Completed Today</div>
                <div className="font-semibold text-green-600">
                  {coordinator.invoicingState.metrics.completedToday}
                </div>
              </div>
              <div>
                <div className="text-gray-600">Avg. Time</div>
                <div className="font-semibold">
                  {coordinator.invoicingState.metrics.averageTime.toFixed(1)}m
                </div>
              </div>
            </div>
            
            <div className="text-xs text-gray-500">
              Last sync: {new Date(coordinator.invoicingState.lastSync).toLocaleTimeString()}
            </div>

            {coordinator.invoicingState.failedTasks.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-2">
                <div className="text-xs font-medium text-red-800">
                  {coordinator.invoicingState.failedTasks.length} failed task(s)
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* System Health Alerts */}
      {coordinator.workflowHealth.bottleneck && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              System Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-yellow-800">
              {coordinator.workflowHealth.bottleneck === 'high_load' && (
                <>
                  <strong>High Load Detected:</strong> The system is currently processing {coordinator.workflowHealth.inProgressTasks} tasks simultaneously. 
                  Consider redistributing workload or adding more resources.
                </>
              )}
              {coordinator.workflowHealth.bottleneck === 'failures' && (
                <>
                  <strong>Task Failures Detected:</strong> {coordinator.workflowHealth.failedTasks} task(s) have failed. 
                  Please review and resolve the issues.
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Real-time Event Log */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Network className="h-5 w-5" />
            Real-time Event Log
            <Badge variant="secondary" className="ml-auto">
              {realtimeEvents.length} events
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {realtimeEvents.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No events yet. Workflow activities will appear here.</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {realtimeEvents.slice(0, 20).map((event, index) => (
                <div key={`${event.id}-${index}`} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      event.type === 'task_completed' ? 'default' :
                      event.type === 'task_failed' ? 'destructive' :
                      event.type === 'task_started' ? 'secondary' : 'outline'
                    }>
                      {event.type.replace('_', ' ')}
                    </Badge>
                    <span className="font-medium">{event.workflowType}</span>
                    {event.orderLineId && (
                      <span className="text-gray-600">Order: {event.orderLineId.slice(-8)}</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}