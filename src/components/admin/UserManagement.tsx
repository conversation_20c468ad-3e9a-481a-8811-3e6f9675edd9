import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'
import {
  Users,
  UserPlus,
  Edit,
  Search,
  Filter,
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  Lock,
  Unlock
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface User {
  id: string
  email: string
  role_id: string
  role_name: string
  role_description: string
  first_name: string | null
  last_name: string | null
  whatsapp_number: string | null
  is_active: boolean
  last_login: string | null
  created_at: string
  updated_at: string
  permissions: Record<string, boolean>
}

interface Role {
  id: string
  name: string
  description: string
  permissions: Record<string, boolean>
  created_at: string
}

interface UserFormData {
  email: string
  password?: string
  role_id: string
  first_name: string
  last_name: string
  whatsapp_number: string
  is_active: boolean
}

export function UserManagement() {
  const { } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<string>('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [userFormData, setUserFormData] = useState<UserFormData>({
    email: '',
    password: '',
    role_id: '',
    first_name: '',
    last_name: '',
    whatsapp_number: '',
    is_active: true
  })

  // Load users and roles data
  const loadData = useCallback(async () => {
    setIsLoading(true)
    try {
      // Load roles
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select('*')
        .order('name')

      if (rolesError) throw rolesError
      setRoles(rolesData || [])

      // Load users with role information
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          roles!inner (
            id,
            name,
            description,
            permissions
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError) throw usersError

      const formattedUsers: User[] = (usersData || []).map(user => ({
        id: user.id,
        email: user.email,
        role_id: user.role_id,
        role_name: user.roles.name,
        role_description: user.roles.description,
        first_name: user.first_name,
        last_name: user.last_name,
        whatsapp_number: user.whatsapp_number,
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at,
        updated_at: user.updated_at,
        permissions: user.roles.permissions
      }))

      setUsers(formattedUsers)
    } catch (err) {
      console.error('Failed to load user data:', err)
      toast.error('Failed to load user data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create new user
  const createUser = useCallback(async () => {
    if (!userFormData.email || !userFormData.password || !userFormData.role_id) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      // Create user via Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userFormData.email,
        password: userFormData.password,
        options: {
          data: {
            first_name: userFormData.first_name,
            last_name: userFormData.last_name
          }
        }
      })

      if (authError) throw authError

      if (authData.user) {
        // Insert user details into users table
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: userFormData.email,
            role_id: userFormData.role_id,
            first_name: userFormData.first_name,
            last_name: userFormData.last_name,
            whatsapp_number: userFormData.whatsapp_number,
            is_active: userFormData.is_active
          })

        if (userError) throw userError

        toast.success('User created successfully')
        setShowCreateModal(false)
        resetForm()
        loadData()
      }
    } catch (err: any) {
      console.error('Failed to create user:', err)
      toast.error(err.message || 'Failed to create user')
    }
  }, [userFormData, loadData])

  // Update user
  const updateUser = useCallback(async () => {
    if (!selectedUser || !userFormData.email || !userFormData.role_id) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const { error } = await supabase
        .from('users')
        .update({
          role_id: userFormData.role_id,
          first_name: userFormData.first_name,
          last_name: userFormData.last_name,
          whatsapp_number: userFormData.whatsapp_number,
          is_active: userFormData.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedUser.id)

      if (error) throw error

      toast.success('User updated successfully')
      setShowEditModal(false)
      setSelectedUser(null)
      resetForm()
      loadData()
    } catch (err: any) {
      console.error('Failed to update user:', err)
      toast.error(err.message || 'Failed to update user')
    }
  }, [selectedUser, userFormData, loadData])

  // Toggle user active status
  const toggleUserStatus = useCallback(async (userId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          is_active: !currentStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) throw error

      toast.success(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully`)
      loadData()
    } catch (err: any) {
      console.error('Failed to toggle user status:', err)
      toast.error(err.message || 'Failed to update user status')
    }
  }, [loadData])

  // Reset form
  const resetForm = () => {
    setUserFormData({
      email: '',
      password: '',
      role_id: '',
      first_name: '',
      last_name: '',
      whatsapp_number: '',
      is_active: true
    })
  }

  // Open edit modal
  const openEditModal = (user: User) => {
    setSelectedUser(user)
    setUserFormData({
      email: user.email,
      role_id: user.role_id,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      whatsapp_number: user.whatsapp_number || '',
      is_active: user.is_active
    })
    setShowEditModal(true)
  }

  // Filter users
  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = selectedRole === 'all' || user.role_id === selectedRole
    
    return matchesSearch && matchesRole
  })

  // Permission helper
  const getPermissionBadges = (permissions: Record<string, boolean>) => {
    const keyPermissions = [
      'ACCESS_ADMIN_PANEL',
      'MANAGE_USERS', 
      'VIEW_ORDER_PRICES',
      'CREATE_ORDERS',
      'MANAGE_KITTING_PACKING',
      'MANAGE_SCREENING_QC',
      'ACCESS_INVOICING'
    ]
    
    return keyPermissions
      .filter(perm => permissions[perm])
      .slice(0, 3) // Show only first 3
  }

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [loadData])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-sm text-gray-600">
            Manage users, roles, and permissions for the system
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="gap-2">
          <UserPlus className="h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search users by name, email, or role..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Users ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading users...</div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
              <p className="text-gray-500">
                {searchTerm || selectedRole !== 'all' 
                  ? 'Try adjusting your search or filter criteria' 
                  : 'Get started by creating your first user'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Key Permissions
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Login
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-700">
                                {(user.first_name?.[0] || user.email[0]).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {user.first_name && user.last_name 
                                ? `${user.first_name} ${user.last_name}`
                                : user.email.split('@')[0]
                              }
                            </div>
                            <div className="text-sm text-gray-500 flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {user.email}
                            </div>
                            {user.whatsapp_number && (
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {user.whatsapp_number}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div>
                          <Badge variant="outline" className="mb-1">
                            {user.role_name}
                          </Badge>
                          <div className="text-xs text-gray-500">
                            {user.role_description}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex flex-wrap gap-1">
                          {getPermissionBadges(user.permissions).map((perm) => (
                            <Badge key={perm} variant="secondary" className="text-xs">
                              {perm.replace(/_/g, ' ').toLowerCase()}
                            </Badge>
                          ))}
                          {Object.values(user.permissions).filter(Boolean).length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{Object.values(user.permissions).filter(Boolean).length - 3} more
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {user.is_active ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="bg-red-100 text-red-800">
                              <XCircle className="h-3 w-3 mr-1" />
                              Inactive
                            </Badge>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.last_login ? (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(user.last_login).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-gray-400">Never</span>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditModal(user)}
                            title="Edit User"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleUserStatus(user.id, user.is_active)}
                            title={user.is_active ? 'Deactivate User' : 'Activate User'}
                            className={cn(
                              user.is_active 
                                ? 'text-red-600 hover:text-red-700' 
                                : 'text-green-600 hover:text-green-700'
                            )}
                          >
                            {user.is_active ? <Lock className="h-3 w-3" /> : <Unlock className="h-3 w-3" />}
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create User Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create New User</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={userFormData.email}
                onChange={(e) => setUserFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={userFormData.password}
                onChange={(e) => setUserFormData(prev => ({ ...prev, password: e.target.value }))}
                placeholder="Minimum 6 characters"
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="grid gap-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input
                  id="first_name"
                  value={userFormData.first_name}
                  onChange={(e) => setUserFormData(prev => ({ ...prev, first_name: e.target.value }))}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  value={userFormData.last_name}
                  onChange={(e) => setUserFormData(prev => ({ ...prev, last_name: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="role">Role *</Label>
              <Select value={userFormData.role_id} onValueChange={(value) => setUserFormData(prev => ({ ...prev, role_id: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="whatsapp">WhatsApp Number</Label>
              <Input
                id="whatsapp"
                value={userFormData.whatsapp_number}
                onChange={(e) => setUserFormData(prev => ({ ...prev, whatsapp_number: e.target.value }))}
                placeholder="+1234567890"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={createUser}>
              Create User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit_email">Email</Label>
              <Input
                id="edit_email"
                type="email"
                value={userFormData.email}
                disabled
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500">Email cannot be changed</p>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="grid gap-2">
                <Label htmlFor="edit_first_name">First Name</Label>
                <Input
                  id="edit_first_name"
                  value={userFormData.first_name}
                  onChange={(e) => setUserFormData(prev => ({ ...prev, first_name: e.target.value }))}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_last_name">Last Name</Label>
                <Input
                  id="edit_last_name"
                  value={userFormData.last_name}
                  onChange={(e) => setUserFormData(prev => ({ ...prev, last_name: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit_role">Role *</Label>
              <Select value={userFormData.role_id} onValueChange={(value) => setUserFormData(prev => ({ ...prev, role_id: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map(role => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit_whatsapp">WhatsApp Number</Label>
              <Input
                id="edit_whatsapp"
                value={userFormData.whatsapp_number}
                onChange={(e) => setUserFormData(prev => ({ ...prev, whatsapp_number: e.target.value }))}
                placeholder="+1234567890"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit_active"
                checked={userFormData.is_active}
                onChange={(e) => setUserFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className="rounded"
              />
              <Label htmlFor="edit_active">Active User</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button onClick={updateUser}>
              Update User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}