import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Printer, 
  Server, 
  RefreshCw, 
  Settings as SettingsIcon, 
  Trash2, 
  Plus, 
  Edit3, 
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Monitor,
  Activity,
  FileText,
  Hash
} from 'lucide-react'
import { toast } from 'sonner'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { usePrinterConfig } from '@/hooks/usePrinterConfig'
import { cn } from '@/lib/utils'
import { TemplateManager } from './TemplateManager'

interface PrintJob {
  id: string
  printer_id: string
  status: 'pending' | 'printing' | 'completed' | 'failed'
  created_at: string
  metadata: Record<string, any>
  error?: string
}

export function MCPSettings() {
  const [activeTab, setActiveTab] = useState<'overview' | 'printers' | 'queue' | 'templates' | 'config'>('overview')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showAddPrinter, setShowAddPrinter] = useState(false)
  const [newPrinter, setNewPrinter] = useState({
    name: '',
    ip_address: '',
    port: '9100',
    model: 'Zebra ZT230',
    location: 'SB',
    is_active: true
  })

  // MCP Hooks
  const { 
    useZebraPrinters, 
    useGenericPrinters,
    useHealthIndicators,
    isConnected,
    connectionError,
    location 
  } = useMCPPrinting()

  // Printer Configuration
  const { 
    getActivePrinters, 
    getDefaultPrinter, 
    addPrinter
  } = usePrinterConfig()

  // Data queries
  const { data: zebraPrinters = [], refetch: refetchZebra } = useZebraPrinters()
  const { data: genericPrinters = [], refetch: refetchGeneric } = useGenericPrinters()
  const { refetch: refetchHealth } = useHealthIndicators()
  
  // Mock print queue data for now
  const printQueue: PrintJob[] = []
  const refetchQueue = async () => console.log('Queue refreshed')

  // Static printer configuration
  const configPrinters = getActivePrinters()
  const defaultPrinter = getDefaultPrinter()

  const handleRefreshAll = async () => {
    setIsRefreshing(true)
    try {
      await Promise.all([
        refetchZebra(),
        refetchGeneric(),
        refetchHealth(),
        refetchQueue()
      ])
      toast.success('MCP system refreshed successfully')
    } catch (error) {
      toast.error('Failed to refresh MCP system')
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleAddPrinter = () => {
    if (!newPrinter.name || !newPrinter.ip_address) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      addPrinter({
        name: newPrinter.name,
        ipAddress: newPrinter.ip_address,
        port: parseInt(newPrinter.port),
        model: newPrinter.model,
        location: newPrinter.location as 'SB' | 'NP',
        isActive: newPrinter.is_active
      })
      toast.success(`Printer "${newPrinter.name}" added successfully`)
      setShowAddPrinter(false)
      setNewPrinter({
        name: '',
        ip_address: '',
        port: '9100',
        model: 'Zebra ZT230',
        location: 'SB',
        is_active: true
      })
    } catch (error) {
      toast.error('Failed to add printer')
    }
  }

  const handleDeletePrinter = (printerId: string) => {
    try {
      // TODO: Implement actual printer deletion when available in usePrinterConfig
      console.log('Delete printer:', printerId)
      toast.success('Printer removed successfully')
    } catch (error) {
      toast.error('Failed to remove printer')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'online':
      case 'connected':
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'offline':
      case 'disconnected':
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      default:
        return <Monitor className="h-4 w-4 text-gray-400" />
    }
  }

  const formatJobMetadata = (metadata: Record<string, any>) => {
    if (metadata.order_uid) {
      return `Order: ${metadata.order_uid} | CT: ${metadata.ct_numbers?.length || 0} | Template: ${metadata.template_name}`
    }
    return `Type: ${metadata.print_type || 'Unknown'} | Quantity: ${metadata.quantity || 1}`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">MCP Printing System</h2>
          <p className="text-sm text-gray-600">
            Model Context Protocol printing server configuration and monitoring
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefreshAll}
            disabled={isRefreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Connection Status */}
      <MCPConnectionStatus showDetails={true} compact={false} />

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Activity },
            { id: 'printers', label: 'Printers', icon: Printer },
            { id: 'queue', label: 'Print Queue', icon: Monitor },
            { id: 'templates', label: 'Templates', icon: Edit3 },
            { id: 'config', label: 'Configuration', icon: SettingsIcon }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  "flex items-center space-x-2 py-2 px-1 border-b-2 transition-colors",
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                )}
              >
                <Icon className="h-4 w-4" />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* System Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Server className="h-4 w-4 mr-2" />
                MCP Server
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Status</span>
                  {isConnected ? (
                    <Badge variant="default" className="text-xs">Connected</Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">Offline</Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Location</span>
                  <span className="text-sm font-medium">{location || 'Unknown'}</span>
                </div>
                {connectionError && (
                  <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                    {connectionError}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Discovered Printers */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Printer className="h-4 w-4 mr-2" />
                Discovered Printers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Zebra Printers</span>
                  <Badge variant="outline" className="text-xs">
                    {zebraPrinters.length}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Generic Printers</span>
                  <Badge variant="outline" className="text-xs">
                    {genericPrinters.length}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Config Printers</span>
                  <Badge variant="outline" className="text-xs">
                    {configPrinters.length}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Print Queue Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Monitor className="h-4 w-4 mr-2" />
                Print Queue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total Jobs</span>
                  <Badge variant="outline" className="text-xs">
                    {printQueue.length}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Pending</span>
                  <Badge variant="secondary" className="text-xs">
                    {printQueue.filter((job: PrintJob) => job.status === 'pending').length}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Failed</span>
                  <Badge variant="destructive" className="text-xs">
                    {printQueue.filter((job: PrintJob) => job.status === 'failed').length}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'printers' && (
        <div className="space-y-6">
          {/* Add Printer Button */}
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium">Printer Management</h3>
            <Button 
              onClick={() => setShowAddPrinter(!showAddPrinter)}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Printer
            </Button>
          </div>

          {/* Add Printer Form */}
          {showAddPrinter && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Add New Printer</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="printer-name">Printer Name</Label>
                    <Input
                      id="printer-name"
                      value={newPrinter.name}
                      onChange={(e) => setNewPrinter({...newPrinter, name: e.target.value})}
                      placeholder="SB Zebra Printer 1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="printer-ip">IP Address</Label>
                    <Input
                      id="printer-ip"
                      value={newPrinter.ip_address}
                      onChange={(e) => setNewPrinter({...newPrinter, ip_address: e.target.value})}
                      placeholder="*************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="printer-port">Port</Label>
                    <Input
                      id="printer-port"
                      value={newPrinter.port}
                      onChange={(e) => setNewPrinter({...newPrinter, port: e.target.value})}
                      placeholder="9100"
                    />
                  </div>
                  <div>
                    <Label htmlFor="printer-model">Model</Label>
                    <select
                      id="printer-model"
                      value={newPrinter.model}
                      onChange={(e) => setNewPrinter({...newPrinter, model: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option>Zebra ZT230</option>
                      <option>Zebra ZT410</option>
                      <option>Zebra ZT420</option>
                      <option>HP LaserJet</option>
                      <option>Epson Generic</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="printer-location">Location</Label>
                    <select
                      id="printer-location"
                      value={newPrinter.location}
                      onChange={(e) => setNewPrinter({...newPrinter, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="SB">SB Location</option>
                      <option value="NP">NP Location</option>
                    </select>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button onClick={handleAddPrinter} size="sm">
                    Add Printer
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAddPrinter(false)}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Discovered Zebra Printers */}
          {zebraPrinters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Discovered Zebra Printers (MCP)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {zebraPrinters.map((printer: any) => (
                    <div key={printer.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(printer.health?.status)}
                        <div>
                          <p className="text-sm font-medium">{printer.name}</p>
                          <p className="text-xs text-gray-500">
                            {printer.ip_address} | {printer.model}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={printer.health?.status === 'online' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {printer.health?.status || 'Unknown'}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Static Configuration Printers */}
          {configPrinters.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Static Configuration Printers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {configPrinters.map((printer: any) => (
                    <div key={printer.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Printer className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium">{printer.name}</p>
                          <p className="text-xs text-gray-500">
                            {printer.ip_address || printer.ipAddress} | {printer.model}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {defaultPrinter?.id === printer.id && (
                          <Badge variant="default" className="text-xs">Default</Badge>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleDeletePrinter(printer.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'queue' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium">Print Queue Management</h3>
            <Button size="sm" variant="outline" onClick={() => refetchQueue()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Queue
            </Button>
          </div>

          {printQueue.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No print jobs in queue</p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-0">
                <div className="space-y-2">
                  {printQueue.map((job: PrintJob) => (
                    <div key={job.id} className="flex items-center justify-between p-4 border-b last:border-b-0">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(job.status)}
                        <div>
                          <p className="text-sm font-medium">Job #{job.id.slice(-8)}</p>
                          <p className="text-xs text-gray-500">
                            {formatJobMetadata(job.metadata)}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(job.created_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={
                            job.status === 'completed' ? 'default' : 
                            job.status === 'failed' ? 'destructive' : 'secondary'
                          }
                          className="text-xs"
                        >
                          {job.status}
                        </Badge>
                        {job.error && (
                          <Button size="sm" variant="outline" title={job.error}>
                            <AlertTriangle className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="space-y-6">
          {/* Label Templates */}
          <TemplateManager />

          {/* PDF Report Templates */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">PDF Report Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <h3 className="font-medium">Orders Summary</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Comprehensive overview of all orders with status and details
                  </p>
                  <Badge variant="default" className="text-xs">Available</Badge>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Hash className="h-4 w-4 text-green-500" />
                    <h3 className="font-medium">CT Numbers Report</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Detailed listing of all CT number assignments
                  </p>
                  <Badge variant="default" className="text-xs">Available</Badge>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <SettingsIcon className="h-4 w-4 text-purple-500" />
                    <h3 className="font-medium">Custom Reports</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Build custom reports with flexible data and formatting
                  </p>
                  <Badge variant="default" className="text-xs">Available</Badge>
                </div>
              </div>
              
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600 mb-3">
                  PDF reports are available through the Orders page Reports button
                </p>
                <div className="flex items-center justify-center space-x-2 text-xs text-green-600">
                  <CheckCircle className="h-3 w-3" />
                  <span>MCP Generic Printer Integration Active</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'config' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">MCP Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>MCP Server URL</Label>
                  <Input 
                    value={location || 'http://localhost:3001'} 
                    readOnly 
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Connection Timeout</Label>
                  <Input 
                    value="5000ms" 
                    readOnly 
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Retry Attempts</Label>
                  <Input 
                    value="3" 
                    readOnly 
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Queue Persistence</Label>
                  <Input 
                    value="SQLite + File Backup" 
                    readOnly 
                    className="bg-gray-50"
                  />
                </div>
              </div>
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">
                  Advanced MCP configuration will be implemented in the next phase
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}