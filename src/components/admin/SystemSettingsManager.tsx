import { useState } from 'react'
import { useSystemSettings, SystemSetting } from '@/hooks/useSystemSettings'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import {
  Settings,
  Edit,
  RotateCcw,
  Download,
  Upload,
  Save,
  Eye,
  EyeOff,
  Search,
  Calendar,
  User,
  AlertCircle,
  Palette,
  Hash,
  MessageSquare,
  List
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SettingEditor {
  key: string
  value: any
  originalValue: any
  isModified: boolean
}

export function SystemSettingsManager() {
  const {
    settings,
    isLoading,
    updateSetting,
    updateMultipleSettings,
    resetSetting,
    exportSettings,
    importSettings,
    canManageSettings
  } = useSystemSettings()

  const [searchTerm, setSearchTerm] = useState('')
  const [showEditModal, setShowEditModal] = useState(false)
  const [showImportModal, setShowImportModal] = useState(false)
  const [selectedSetting, setSelectedSetting] = useState<SystemSetting | null>(null)
  const [editValue, setEditValue] = useState<string>('')
  const [importData, setImportData] = useState('')
  const [showJsonValues, setShowJsonValues] = useState<Record<string, boolean>>({})
  const [modifiedSettings, setModifiedSettings] = useState<Record<string, SettingEditor>>({})

  // Filter settings
  const filteredSettings = settings.filter(setting =>
    setting.setting_key.toLowerCase().includes(searchTerm.toLowerCase()) ||
    setting.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get setting icon
  const getSettingIcon = (key: string) => {
    if (key.includes('color') || key.includes('eta')) return Palette
    if (key.includes('whatsapp') || key.includes('message')) return MessageSquare
    if (key.includes('prefix') || key.includes('uid')) return Hash
    if (key.includes('reason') || key.includes('list')) return List
    return Settings
  }

  // Format setting value for display
  const formatValue = (value: any): string => {
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2)
    }
    return String(value)
  }

  // Parse string value back to appropriate type
  const parseValue = (stringValue: string, originalValue: any): any => {
    if (typeof originalValue === 'object') {
      try {
        return JSON.parse(stringValue)
      } catch {
        throw new Error('Invalid JSON format')
      }
    }
    
    if (typeof originalValue === 'number') {
      const num = Number(stringValue)
      if (isNaN(num)) throw new Error('Invalid number format')
      return num
    }
    
    if (typeof originalValue === 'boolean') {
      return stringValue.toLowerCase() === 'true'
    }
    
    return stringValue
  }

  // Open edit modal
  const openEditModal = (setting: SystemSetting) => {
    setSelectedSetting(setting)
    setEditValue(formatValue(setting.setting_value))
    setShowEditModal(true)
  }

  // Handle save setting
  const handleSave = async () => {
    if (!selectedSetting) return

    try {
      const parsedValue = parseValue(editValue, selectedSetting.setting_value)
      const success = await updateSetting(selectedSetting.setting_key, parsedValue)
      
      if (success) {
        setShowEditModal(false)
        setSelectedSetting(null)
        setEditValue('')
      }
    } catch (err: any) {
      toast.error(err.message || 'Invalid value format')
    }
  }

  // Handle reset setting
  const handleReset = async (setting: SystemSetting) => {
    if (window.confirm(`Are you sure you want to reset "${setting.setting_key}" to its default value?`)) {
      await resetSetting(setting.setting_key)
    }
  }

  // Handle export
  const handleExport = () => {
    const exportData = exportSettings()
    const blob = new Blob([exportData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Settings exported successfully')
  }

  // Handle import
  const handleImport = async () => {
    if (!importData.trim()) {
      toast.error('Please enter JSON data to import')
      return
    }

    const success = await importSettings(importData)
    if (success) {
      setShowImportModal(false)
      setImportData('')
    }
  }

  // Toggle JSON view
  const toggleJsonView = (settingKey: string) => {
    setShowJsonValues(prev => ({
      ...prev,
      [settingKey]: !prev[settingKey]
    }))
  }

  // Start inline editing
  const startInlineEdit = (setting: SystemSetting) => {
    setModifiedSettings(prev => ({
      ...prev,
      [setting.setting_key]: {
        key: setting.setting_key,
        value: setting.setting_value,
        originalValue: setting.setting_value,
        isModified: false
      }
    }))
  }

  // Update inline edit value
  const updateInlineValue = (settingKey: string, newValue: any) => {
    setModifiedSettings(prev => {
      const current = prev[settingKey]
      if (!current) return prev

      return {
        ...prev,
        [settingKey]: {
          ...current,
          value: newValue,
          isModified: JSON.stringify(newValue) !== JSON.stringify(current.originalValue)
        }
      }
    })
  }

  // Save inline edits
  const saveInlineEdits = async () => {
    const updates = Object.entries(modifiedSettings)
      .filter(([_, editor]) => editor.isModified)
      .reduce((acc, [key, editor]) => {
        acc[key] = editor.value
        return acc
      }, {} as Record<string, any>)

    if (Object.keys(updates).length === 0) {
      toast.info('No changes to save')
      return
    }

    const success = await updateMultipleSettings(updates)
    if (success) {
      setModifiedSettings({})
    }
  }

  // Cancel inline edits
  const cancelInlineEdits = () => {
    setModifiedSettings({})
  }

  const hasModifications = Object.values(modifiedSettings).some(editor => editor.isModified)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">System Settings</h2>
          <p className="text-sm text-gray-600">
            Configure application-wide settings and preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasModifications && (
            <>
              <Button variant="outline" onClick={cancelInlineEdits}>
                Cancel Changes
              </Button>
              <Button onClick={saveInlineEdits} className="gap-2">
                <Save className="h-4 w-4" />
                Save Changes ({Object.values(modifiedSettings).filter(e => e.isModified).length})
              </Button>
            </>
          )}
          {canManageSettings && (
            <>
              <Button variant="outline" onClick={() => setShowImportModal(true)} className="gap-2">
                <Upload className="h-4 w-4" />
                Import
              </Button>
              <Button variant="outline" onClick={handleExport} className="gap-2">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search settings by key or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Settings List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">Loading settings...</div>
        ) : filteredSettings.length === 0 ? (
          <div className="text-center py-8">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No settings found</h3>
            <p className="text-gray-500">
              {searchTerm ? 'Try adjusting your search criteria' : 'No system settings available'}
            </p>
          </div>
        ) : (
          filteredSettings.map((setting) => {
            const Icon = getSettingIcon(setting.setting_key)
            const isJsonValue = typeof setting.setting_value === 'object'
            const showJson = showJsonValues[setting.setting_key]
            const editor = modifiedSettings[setting.setting_key]
            const displayValue = editor?.value ?? setting.setting_value

            return (
              <Card key={setting.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Icon className="h-5 w-5 text-blue-600" />
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{setting.setting_key}</h3>
                          <p className="text-sm text-gray-600">{setting.description}</p>
                        </div>
                        {editor?.isModified && (
                          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                            Modified
                          </Badge>
                        )}
                      </div>

                      {/* Value Display/Editor */}
                      <div className="mt-4">
                        <Label className="text-sm font-medium text-gray-700">Current Value</Label>
                        {isJsonValue ? (
                          <div className="mt-2">
                            <div className="flex items-center gap-2 mb-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => toggleJsonView(setting.setting_key)}
                                className="gap-2"
                              >
                                {showJson ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                                {showJson ? 'Hide JSON' : 'Show JSON'}
                              </Button>
                              {canManageSettings && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openEditModal(setting)}
                                  className="gap-2"
                                >
                                  <Edit className="h-3 w-3" />
                                  Edit
                                </Button>
                              )}
                            </div>
                            {showJson && (
                              <pre className="bg-gray-50 p-3 rounded-md text-sm overflow-auto max-h-64 border">
                                {formatValue(displayValue)}
                              </pre>
                            )}
                            {!showJson && (
                              <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                                Complex object with {Object.keys(displayValue).length} properties
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="mt-2">
                            {canManageSettings && !editor ? (
                              <div className="flex items-center gap-2">
                                <Input
                                  value={String(displayValue)}
                                  readOnly
                                  className="flex-1"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => startInlineEdit(setting)}
                                  className="gap-2"
                                >
                                  <Edit className="h-3 w-3" />
                                  Edit
                                </Button>
                              </div>
                            ) : editor ? (
                              <Input
                                value={String(displayValue)}
                                onChange={(e) => updateInlineValue(setting.setting_key, e.target.value)}
                                className={cn(
                                  "transition-colors",
                                  editor.isModified && "border-orange-300 bg-orange-50"
                                )}
                              />
                            ) : (
                              <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md border">
                                {String(displayValue)}
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Metadata */}
                      <div className="mt-4 flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Last updated: {new Date(setting.updated_at).toLocaleDateString()}
                        </div>
                        {setting.updated_by_name && (
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            by {setting.updated_by_name}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    {canManageSettings && (
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReset(setting)}
                          className="gap-2 text-orange-600 hover:text-orange-700"
                        >
                          <RotateCcw className="h-3 w-3" />
                          Reset
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>

      {/* Edit Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit System Setting</DialogTitle>
          </DialogHeader>
          {selectedSetting && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">{selectedSetting.setting_key}</Label>
                <p className="text-sm text-gray-600 mt-1">{selectedSetting.description}</p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_value">Value</Label>
                <Textarea
                  id="edit_value"
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  placeholder="Enter the setting value..."
                  rows={10}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500">
                  For JSON objects, ensure proper formatting. For simple values, enter as plain text.
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Setting
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Modal */}
      <Dialog open={showImportModal} onOpenChange={setShowImportModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import System Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Warning</span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                Importing settings will overwrite existing values. Make sure to export current settings as backup first.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="import_data">JSON Settings Data</Label>
              <Textarea
                id="import_data"
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder='{"setting_key": "value", ...}'
                rows={15}
                className="font-mono text-sm"
              />
              <p className="text-xs text-gray-500">
                Paste the JSON data containing the settings to import. Only existing setting keys will be updated.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImportModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleImport} variant="destructive">
              Import Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}