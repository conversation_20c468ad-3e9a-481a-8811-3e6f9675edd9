import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { UserManagement } from './UserManagement'
import { RoleManagement } from './RoleManagement'
import { Users, Shield } from 'lucide-react'
import { cn } from '@/lib/utils'

export function UserRoleManagement() {
  const [activeTab, setActiveTab] = useState<'users' | 'roles'>('users')

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Users & Roles Management</h2>
        <p className="text-sm text-gray-600">
          Manage user accounts, roles, and permissions for the system
        </p>
      </div>

      {/* Tab Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('users')}
              className={cn(
                "flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors",
                activeTab === 'users'
                  ? "bg-white text-blue-700 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              <Users className="h-4 w-4" />
              User Management
            </button>
            <button
              onClick={() => setActiveTab('roles')}
              className={cn(
                "flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors",
                activeTab === 'roles'
                  ? "bg-white text-blue-700 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              <Shield className="h-4 w-4" />
              Role Management
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Tab Content */}
      <div>
        {activeTab === 'users' && <UserManagement />}
        {activeTab === 'roles' && <RoleManagement />}
      </div>
    </div>
  )
}