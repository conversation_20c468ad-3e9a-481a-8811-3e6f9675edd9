import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'
import {
  Shield,
  Plus,
  Edit,
  Users,
  Key,
  CheckSquare,
  Square,
  Eye
} from 'lucide-react'

interface Role {
  id: string
  name: string
  description: string
  permissions: Record<string, boolean>
  created_at: string
  user_count?: number
}

interface RoleFormData {
  name: string
  description: string
  permissions: Record<string, boolean>
}

// Define all available permissions with descriptions
const PERMISSION_DEFINITIONS = {
  // Order Management
  'VIEW_ALL_ORDERS': 'View all orders in the system',
  'VIEW_OWN_ORDERS': 'View only assigned orders',
  'CREATE_ORDERS': 'Create new orders',
  'EDIT_ORDERS': 'Edit existing orders',
  'DELETE_ORDERS': 'Delete orders',
  'VIEW_ORDER_PRICES': 'View order pricing information',
  'VIEW_CUSTOMER_NAMES': 'View customer names and details',
  
  // CT Number Management
  'ASSIGN_CT_NUMBERS': 'Assign CT numbers to orders',
  'OVERRIDE_DUPLICATE_CT': 'Override duplicate CT number checks',
  'APPROVE_DUPLICATE_CT': 'Approve duplicate CT numbers',
  
  // Workflow Management
  'MANAGE_KITTING_PACKING': 'Manage kitting and packing operations',
  'MANAGE_SCREENING_QC': 'Manage screening and QC operations',
  'MANAGE_NPQC': 'Manage NP location QC operations',
  'CREATE_TRANSFERS': 'Create inter-location transfers',
  
  // Administrative
  'MANAGE_USERS': 'Create and manage user accounts',
  'MANAGE_ROLES': 'Create and manage user roles',
  'MANAGE_SYSTEM_SETTINGS': 'Modify system configuration',
  'ACCESS_ADMIN_PANEL': 'Access administrative functions',
  
  // Financial
  'ACCESS_INVOICING': 'Access invoicing module',
  'CREATE_INVOICES': 'Create and send invoices',
  
  // Documents & Files
  'UPLOAD_DOCUMENTS': 'Upload FAI and other documents',
  
  // Procurement
  'ACCESS_PROCUREMENT': 'Access procurement functions',
  'MANAGE_VENDORS': 'Manage vendor information',
  
  // Communication
  'SEND_WHATSAPP': 'Send WhatsApp messages'
}

const PERMISSION_GROUPS = {
  'Order Management': [
    'VIEW_ALL_ORDERS',
    'VIEW_OWN_ORDERS', 
    'CREATE_ORDERS',
    'EDIT_ORDERS',
    'DELETE_ORDERS',
    'VIEW_ORDER_PRICES',
    'VIEW_CUSTOMER_NAMES'
  ],
  'CT Numbers': [
    'ASSIGN_CT_NUMBERS',
    'OVERRIDE_DUPLICATE_CT',
    'APPROVE_DUPLICATE_CT'
  ],
  'Workflow Operations': [
    'MANAGE_KITTING_PACKING',
    'MANAGE_SCREENING_QC',
    'MANAGE_NPQC',
    'CREATE_TRANSFERS'
  ],
  'Administrative': [
    'MANAGE_USERS',
    'MANAGE_ROLES',
    'MANAGE_SYSTEM_SETTINGS',
    'ACCESS_ADMIN_PANEL'
  ],
  'Financial': [
    'ACCESS_INVOICING',
    'CREATE_INVOICES'
  ],
  'Documents': [
    'UPLOAD_DOCUMENTS'
  ],
  'Procurement': [
    'ACCESS_PROCUREMENT',
    'MANAGE_VENDORS'
  ],
  'Communication': [
    'SEND_WHATSAPP'
  ]
}

export function RoleManagement() {
  const { } = useAuth()
  const [roles, setRoles] = useState<Role[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [roleFormData, setRoleFormData] = useState<RoleFormData>({
    name: '',
    description: '',
    permissions: {}
  })

  // Load roles data
  const loadRoles = useCallback(async () => {
    setIsLoading(true)
    try {
      // Load roles with user count
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select(`
          *,
          users!inner(count)
        `)
        .order('created_at')

      if (rolesError) throw rolesError

      const rolesWithCount: Role[] = (rolesData || []).map(role => ({
        ...role,
        user_count: role.users?.length || 0
      }))

      setRoles(rolesWithCount)
    } catch (err) {
      console.error('Failed to load roles:', err)
      toast.error('Failed to load roles')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Create new role
  const createRole = useCallback(async () => {
    if (!roleFormData.name || !roleFormData.description) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const { error } = await supabase
        .from('roles')
        .insert({
          name: roleFormData.name,
          description: roleFormData.description,
          permissions: roleFormData.permissions
        })

      if (error) throw error

      toast.success('Role created successfully')
      setShowCreateModal(false)
      resetForm()
      loadRoles()
    } catch (err: any) {
      console.error('Failed to create role:', err)
      toast.error(err.message || 'Failed to create role')
    }
  }, [roleFormData, loadRoles])

  // Update role
  const updateRole = useCallback(async () => {
    if (!selectedRole || !roleFormData.name || !roleFormData.description) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const { error } = await supabase
        .from('roles')
        .update({
          name: roleFormData.name,
          description: roleFormData.description,
          permissions: roleFormData.permissions
        })
        .eq('id', selectedRole.id)

      if (error) throw error

      toast.success('Role updated successfully')
      setShowEditModal(false)
      setSelectedRole(null)
      resetForm()
      loadRoles()
    } catch (err: any) {
      console.error('Failed to update role:', err)
      toast.error(err.message || 'Failed to update role')
    }
  }, [selectedRole, roleFormData, loadRoles])

  // Reset form
  const resetForm = () => {
    setRoleFormData({
      name: '',
      description: '',
      permissions: {}
    })
  }

  // Open edit modal
  const openEditModal = (role: Role) => {
    setSelectedRole(role)
    setRoleFormData({
      name: role.name,
      description: role.description,
      permissions: { ...role.permissions }
    })
    setShowEditModal(true)
  }

  // Open view modal
  const openViewModal = (role: Role) => {
    setSelectedRole(role)
    setShowViewModal(true)
  }

  // Toggle permission
  const togglePermission = (permission: string) => {
    setRoleFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: !prev.permissions[permission]
      }
    }))
  }

  // Load data on component mount
  useEffect(() => {
    loadRoles()
  }, [loadRoles])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Role Management</h2>
          <p className="text-sm text-gray-600">
            Define roles and permissions for different user types
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Create Role
        </Button>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full text-center py-8">Loading roles...</div>
        ) : roles.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No roles found</h3>
            <p className="text-gray-500">Get started by creating your first role</p>
          </div>
        ) : (
          roles.map((role) => {
            const permissionCount = Object.values(role.permissions).filter(Boolean).length
            return (
              <Card key={role.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Shield className="h-5 w-5 text-blue-600" />
                        {role.name}
                      </CardTitle>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {role.description}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Statistics */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-1 text-gray-600">
                        <Users className="h-4 w-4" />
                        {role.user_count || 0} users
                      </div>
                      <div className="flex items-center gap-1 text-gray-600">
                        <Key className="h-4 w-4" />
                        {permissionCount} permissions
                      </div>
                    </div>

                    {/* Key Permissions Preview */}
                    <div>
                      <div className="text-xs font-medium text-gray-700 mb-2">Key Permissions:</div>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(role.permissions)
                          .filter(([_, value]) => value)
                          .slice(0, 3)
                          .map(([permission, _]) => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission.replace(/_/g, ' ').toLowerCase()}
                            </Badge>
                          ))}
                        {permissionCount > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{permissionCount - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openViewModal(role)}
                        className="flex-1"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditModal(role)}
                        className="flex-1"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>

      {/* Create Role Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Role Name *</Label>
                <Input
                  id="name"
                  value={roleFormData.name}
                  onChange={(e) => setRoleFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Quality Control Manager"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={roleFormData.description}
                  onChange={(e) => setRoleFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this role does and its responsibilities"
                  rows={3}
                />
              </div>
            </div>

            {/* Permissions */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium">Permissions</h4>
              {Object.entries(PERMISSION_GROUPS).map(([groupName, permissions]) => (
                <Card key={groupName}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">{groupName}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3">
                      {permissions.map((permission) => (
                        <div key={permission} className="flex items-start space-x-3">
                          <button
                            type="button"
                            onClick={() => togglePermission(permission)}
                            className="mt-1"
                          >
                            {roleFormData.permissions[permission] ? (
                              <CheckSquare className="h-4 w-4 text-blue-600" />
                            ) : (
                              <Square className="h-4 w-4 text-gray-400" />
                            )}
                          </button>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900">
                              {permission.replace(/_/g, ' ').toLowerCase()}
                            </div>
                            <div className="text-xs text-gray-500">
                              {PERMISSION_DEFINITIONS[permission as keyof typeof PERMISSION_DEFINITIONS]}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={createRole}>
              Create Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Role Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit_name">Role Name *</Label>
                <Input
                  id="edit_name"
                  value={roleFormData.name}
                  onChange={(e) => setRoleFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Quality Control Manager"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit_description">Description *</Label>
                <Textarea
                  id="edit_description"
                  value={roleFormData.description}
                  onChange={(e) => setRoleFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this role does and its responsibilities"
                  rows={3}
                />
              </div>
            </div>

            {/* Permissions */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium">Permissions</h4>
              {Object.entries(PERMISSION_GROUPS).map(([groupName, permissions]) => (
                <Card key={groupName}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">{groupName}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3">
                      {permissions.map((permission) => (
                        <div key={permission} className="flex items-start space-x-3">
                          <button
                            type="button"
                            onClick={() => togglePermission(permission)}
                            className="mt-1"
                          >
                            {roleFormData.permissions[permission] ? (
                              <CheckSquare className="h-4 w-4 text-blue-600" />
                            ) : (
                              <Square className="h-4 w-4 text-gray-400" />
                            )}
                          </button>
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900">
                              {permission.replace(/_/g, ' ').toLowerCase()}
                            </div>
                            <div className="text-xs text-gray-500">
                              {PERMISSION_DEFINITIONS[permission as keyof typeof PERMISSION_DEFINITIONS]}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button onClick={updateRole}>
              Update Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Role Modal */}
      <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {selectedRole?.name}
            </DialogTitle>
          </DialogHeader>
          {selectedRole && (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-900">Description</h4>
                <p className="text-sm text-gray-600 mt-1">{selectedRole.description}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Permissions</h4>
                <div className="space-y-3">
                  {Object.entries(PERMISSION_GROUPS).map(([groupName, permissions]) => {
                    const groupPermissions = permissions.filter(p => selectedRole.permissions[p])
                    if (groupPermissions.length === 0) return null

                    return (
                      <div key={groupName} className="border rounded-lg p-3">
                        <h5 className="text-sm font-medium text-gray-800 mb-2">{groupName}</h5>
                        <div className="grid gap-1">
                          {groupPermissions.map((permission) => (
                            <div key={permission} className="flex items-center gap-2 text-sm">
                              <CheckSquare className="h-3 w-3 text-green-600" />
                              <span>{permission.replace(/_/g, ' ').toLowerCase()}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {selectedRole.user_count || 0} users assigned
                </div>
                <div className="flex items-center gap-1">
                  <Key className="h-4 w-4" />
                  {Object.values(selectedRole.permissions).filter(Boolean).length} permissions
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewModal(false)}>
              Close
            </Button>
            {selectedRole && (
              <Button onClick={() => {
                setShowViewModal(false)
                openEditModal(selectedRole)
              }}>
                <Edit className="h-3 w-3 mr-1" />
                Edit Role
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}