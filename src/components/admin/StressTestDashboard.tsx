/**
 * Stress Test Dashboard for Admin Interface
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Play, 
  Square, 
  Activity, 
  Users, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Zap,
  Database,
  Wifi
} from 'lucide-react'
import { 
  useStressTesting, 
  StressTestConfigurations, 
  StressTestResults 
} from '@/utils/stressTesting'
import { performanceMonitor } from '@/utils/monitoring'

export function StressTestDashboard() {
  const { runTest, stopTest, isRunning, results, error } = useStressTesting()
  const [selectedConfig, setSelectedConfig] = useState<string>('light')
  const [systemMetrics, setSystemMetrics] = useState<any>(null)

  useEffect(() => {
    const interval = setInterval(() => {
      if (isRunning) {
        setSystemMetrics(performanceMonitor.getSummary())
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [isRunning])

  const handleStartTest = async () => {
    const config = StressTestConfigurations[selectedConfig]
    if (config) {
      await runTest(config)
    }
  }

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m ${seconds % 60}s`
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(Math.round(num))
  }

  const getConfigBadgeColor = (configName: string) => {
    switch (configName) {
      case 'light': return 'default'
      case 'moderate': return 'warning'
      case 'heavy': return 'destructive'
      case 'realtimeFocused': return 'secondary'
      default: return 'default'
    }
  }

  const getSuccessRate = (results: StressTestResults) => {
    if (results.totalOperations === 0) return 0
    return (results.successfulOperations / results.totalOperations) * 100
  }

  return (
    <div className="space-y-6">
      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Stress Test Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {Object.entries(StressTestConfigurations).map(([name, config]) => (
              <Card 
                key={name}
                className={`cursor-pointer transition-colors ${
                  selectedConfig === name ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedConfig(name)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant={getConfigBadgeColor(name) as any}>
                      {name.toUpperCase()}
                    </Badge>
                    <Users className="w-4 h-4 text-muted-foreground" />
                  </div>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Users:</span>
                      <span className="font-medium">{config.concurrentUsers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span className="font-medium">{formatDuration(config.testDurationMs)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Ops/sec:</span>
                      <span className="font-medium">{config.operationsPerSecond}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Channels:</span>
                      <span className="font-medium">{config.realtimeChannels.length}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex space-x-4">
            <Button 
              onClick={handleStartTest}
              disabled={isRunning}
              className="flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Start Stress Test</span>
            </Button>
            
            {isRunning && (
              <Button 
                variant="destructive"
                onClick={stopTest}
                className="flex items-center space-x-2"
              >
                <Square className="w-4 h-4" />
                <span>Stop Test</span>
              </Button>
            )}
          </div>

          {error && (
            <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertTriangle className="w-4 h-4" />
                <span className="font-medium">Test Failed:</span>
              </div>
              <p className="mt-1 text-sm text-destructive/80">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Real-time Metrics (while test is running) */}
      {isRunning && systemMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5" />
              <span>Live System Metrics</span>
              <Badge variant="default" className="animate-pulse">RUNNING</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3">
                <Activity className="w-8 h-8 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Active Metrics</p>
                  <p className="text-2xl font-bold">{systemMetrics.metricsCount}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Users className="w-8 h-8 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">User Interactions</p>
                  <p className="text-2xl font-bold">{systemMetrics.interactionsCount}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Wifi className="w-8 h-8 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Real-time Active</p>
                  <p className="text-2xl font-bold text-green-500">
                    <CheckCircle className="w-6 h-6 inline mr-2" />
                    Connected
                  </p>
                </div>
              </div>
            </div>

            {systemMetrics.topSlowOperations?.length > 0 && (
              <div className="mt-6">
                <h4 className="font-medium mb-3">Slow Operations (Live)</h4>
                <div className="space-y-2">
                  {systemMetrics.topSlowOperations.slice(0, 5).map((op: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-accent rounded">
                      <span className="text-sm font-medium">{op.name}</span>
                      <Badge variant="secondary">
                        {op.value.toFixed(0)}ms
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Test Results</span>
              <Badge variant={getSuccessRate(results) > 95 ? 'default' : 'destructive'}>
                {getSuccessRate(results).toFixed(1)}% Success Rate
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Summary Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-accent rounded-lg">
                <Database className="w-6 h-6 mx-auto mb-2 text-blue-500" />
                <p className="text-2xl font-bold">{formatNumber(results.totalOperations)}</p>
                <p className="text-sm text-muted-foreground">Total Operations</p>
              </div>
              
              <div className="text-center p-4 bg-accent rounded-lg">
                <CheckCircle className="w-6 h-6 mx-auto mb-2 text-green-500" />
                <p className="text-2xl font-bold">{formatNumber(results.successfulOperations)}</p>
                <p className="text-sm text-muted-foreground">Successful</p>
              </div>
              
              <div className="text-center p-4 bg-accent rounded-lg">
                <Clock className="w-6 h-6 mx-auto mb-2 text-orange-500" />
                <p className="text-2xl font-bold">{results.averageResponseTime.toFixed(0)}ms</p>
                <p className="text-sm text-muted-foreground">Avg Response</p>
              </div>
              
              <div className="text-center p-4 bg-accent rounded-lg">
                <Activity className="w-6 h-6 mx-auto mb-2 text-purple-500" />
                <p className="text-2xl font-bold">{results.operationsPerSecond.toFixed(1)}</p>
                <p className="text-sm text-muted-foreground">Ops/Second</p>
              </div>
            </div>

            {/* Detailed Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Performance Metrics */}
              <div>
                <h4 className="font-medium mb-3">Performance Metrics</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Test Duration:</span>
                    <span className="font-medium">
                      {formatDuration(results.endTime! - results.startTime)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Min Response Time:</span>
                    <span className="font-medium">{results.minResponseTime?.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Max Response Time:</span>
                    <span className="font-medium">{results.maxResponseTime?.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Failed Operations:</span>
                    <Badge variant={results.failedOperations > 0 ? 'destructive' : 'default'}>
                      {formatNumber(results.failedOperations)}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Real-time Metrics */}
              <div>
                <h4 className="font-medium mb-3">Real-time Metrics</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Subscriptions:</span>
                    <span className="font-medium">{results.realtimeMetrics.subscriptionCount}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Messages Sent:</span>
                    <span className="font-medium">{formatNumber(results.realtimeMetrics.messagesSent)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Messages Received:</span>
                    <span className="font-medium">{formatNumber(results.realtimeMetrics.messagesReceived)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Connection Errors:</span>
                    <Badge variant={results.realtimeMetrics.connectionErrors > 0 ? 'destructive' : 'default'}>
                      {results.realtimeMetrics.connectionErrors}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Error Details */}
            {results.errors.length > 0 && (
              <div className="mt-6">
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4 text-destructive" />
                  <span>Errors ({results.errors.length})</span>
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {results.errors.slice(0, 10).map((error, index) => (
                    <div key={index} className="text-sm p-2 bg-destructive/10 rounded border-l-2 border-destructive">
                      <div className="flex justify-between items-start">
                        <span className="font-medium">{error.operation}</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(error.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-destructive/80 mt-1">{error.error}</p>
                    </div>
                  ))}
                  {results.errors.length > 10 && (
                    <p className="text-sm text-muted-foreground text-center">
                      ... and {results.errors.length - 10} more errors
                    </p>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}