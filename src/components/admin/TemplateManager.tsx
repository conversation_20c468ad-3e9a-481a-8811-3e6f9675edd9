import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  RefreshCw, 
  Plus, 
  Edit3, 
  Trash2, 
  Upload, 
  Download,
  Eye,
  CheckCircle,
  AlertTriangle,
  Package,
  Truck,
  AlertCircle as AlertIcon,
  Clock,
  Search,
  FileText,
  Hash
} from 'lucide-react'
import { toast } from 'sonner'
import { 
  TEMPLATE_CATEGORIES,
  initializeTemplateSystem
} from '@/utils/templateLoader'
import { supabase } from '@/lib/supabase'

interface Template {
  id: string
  name: string
  description: string
  category: string
  template_type: 'quick_print' | 'ct_label'
  label_size: string
  zpl_template: string
  is_active: boolean
  created_by: string
  created_at: string
  updated_at?: string
}

export function TemplateManager() {
  const navigate = useNavigate()
  const [templates, setTemplates] = useState<Template[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<'all' | 'quick_print' | 'ct_label'>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Load templates from database
  const loadTemplates = async () => {
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from('label_templates')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }

      const formattedTemplates: Template[] = (data || []).map(template => ({
        id: template.id,
        name: template.name,
        description: template.description || '',
        category: template.category,
        template_type: template.template_type || 'quick_print',
        label_size: typeof template.label_size === 'object' 
          ? `${template.label_size.width}x${template.label_size.height}${template.label_size.unit}` 
          : template.label_size || '4x6inch',
        zpl_template: template.zpl_template || '',
        is_active: template.is_active ?? true,
        created_by: template.created_by || 'system',
        created_at: template.created_at
      }))
      
      setTemplates(formattedTemplates)
      toast.success(`Loaded ${formattedTemplates.length} templates from database`)
    } catch (error) {
      console.error('Failed to load templates:', error)
      toast.error('Failed to load templates from database')
    } finally {
      setIsLoading(false)
    }
  }

  // Navigate to Label Designer to edit template
  const handleEditTemplate = (template: Template) => {
    navigate('/label-designer', {
      state: {
        editTemplate: {
          id: template.id,
          name: template.name,
          category: template.category,
          template_type: template.template_type,
          label_size: template.label_size,
          zpl_template: template.zpl_template,
          canvas_data: template.zpl_template // Convert ZPL to canvas data if needed
        }
      }
    })
  }

  // Initialize predefined templates
  const handleInitializeTemplates = async () => {
    setIsInitializing(true)
    try {
      await initializeTemplateSystem()
      toast.success('Template system initialized successfully')
      await loadTemplates() // Reload templates after initialization
    } catch (error) {
      console.error('Failed to initialize templates:', error)
      toast.error('Failed to initialize template system')
    } finally {
      setIsInitializing(false)
    }
  }

  // Filter templates based on search and filters
  useEffect(() => {
    let filtered = templates

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory)
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(template => template.template_type === selectedType)
    }

    setFilteredTemplates(filtered)
  }, [templates, searchQuery, selectedCategory, selectedType])

  // Load templates on component mount
  useEffect(() => {
    loadTemplates()
  }, [])

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'shipping': return <Truck className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'priority': return <Clock className="h-4 w-4" />
      case 'qc': return <CheckCircle className="h-4 w-4" />
      case 'inspection': return <Search className="h-4 w-4" />
      case 'testing': return <Package className="h-4 w-4" />
      case 'returns': return <RefreshCw className="h-4 w-4" />
      case 'ct_labels': return <Hash className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'shipping': return 'bg-blue-100 text-blue-700'
      case 'warning': return 'bg-red-100 text-red-700'
      case 'priority': return 'bg-orange-100 text-orange-700'
      case 'qc': return 'bg-green-100 text-green-700'
      case 'inspection': return 'bg-purple-100 text-purple-700'
      case 'testing': return 'bg-indigo-100 text-indigo-700'
      case 'returns': return 'bg-yellow-100 text-yellow-700'
      case 'ct_labels': return 'bg-emerald-100 text-emerald-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Template Management</h2>
          <p className="text-sm text-gray-600">
            Manage label templates for quick printing and CT number assignment
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleInitializeTemplates}
            disabled={isInitializing}
          >
            {isInitializing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Upload className="h-4 w-4 mr-2" />
            )}
            Load Predefined
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadTemplates}
            disabled={isLoading}
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Templates</p>
                <p className="text-2xl font-bold">{templates.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">Quick Print</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => t.template_type === 'quick_print').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Hash className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium">CT Labels</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => t.template_type === 'ct_label').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-emerald-500" />
              <div>
                <p className="text-sm font-medium">Active</p>
                <p className="text-2xl font-bold">
                  {templates.filter(t => t.is_active).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Template Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium mb-2">Search Templates</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name or description..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Categories</option>
                {Object.entries(TEMPLATE_CATEGORIES).map(([key, label]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>

            {/* Type Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Template Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value as 'all' | 'quick_print' | 'ct_label')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Types</option>
                <option value="quick_print">Quick Print</option>
                <option value="ct_label">CT Labels</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(template.category)}
                  <h3 className="font-medium text-sm">{template.name}</h3>
                </div>
                <div className="flex items-center space-x-1">
                  {template.is_active ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertIcon className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              <p className="text-xs text-gray-600 line-clamp-2">
                {template.description}
              </p>
              
              <div className="flex items-center justify-between">
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${getCategoryColor(template.category)}`}
                >
                  {TEMPLATE_CATEGORIES[template.category as keyof typeof TEMPLATE_CATEGORIES] || template.category}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {template.label_size}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <Badge 
                  variant={template.template_type === 'quick_print' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {template.template_type === 'quick_print' ? 'Quick Print' : 'CT Label'}
                </Badge>
                <span className="text-xs text-gray-500">
                  by {template.created_by}
                </span>
              </div>

              <div className="flex items-center space-x-2 pt-2 border-t">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="h-3 w-3 mr-1" />
                  Preview
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleEditTemplate(template)}
                  title="Edit in Label Designer"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline">
                  <Download className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && !isLoading && (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-500 mb-4">
              {searchQuery || selectedCategory !== 'all' || selectedType !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Get started by loading predefined templates or creating a new one'}
            </p>
            {!searchQuery && selectedCategory === 'all' && selectedType === 'all' && (
              <Button onClick={handleInitializeTemplates} disabled={isInitializing}>
                {isInitializing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                Load Predefined Templates
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}