import { useState } from 'react'
import { useQuantityHolds, QuantityHold, QuantityHoldFormData } from '@/hooks/useQuantityHolds'
import { useOrders } from '@/hooks/useOrders'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import {
  AlertTriangle,
  Plus,
  Play,
  X,
  ArrowUp,
  Search,
  Filter,
  Clock,
  Package,
  CheckCircle,
  Hash
} from 'lucide-react'
import { cn } from '@/lib/utils'

export function QuantityHoldsManager() {
  const {
    holds,
    isLoading,
    createHold,
    releaseHold,
    escalateHold,
    rejectHold,
    getHoldStats,
    canManageKittingHolds,
    canManageQCHolds
  } = useQuantityHolds()

  const { data: orders } = useOrders()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStage, setSelectedStage] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('active')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showActionModal, setShowActionModal] = useState(false)
  const [selectedHold, setSelectedHold] = useState<QuantityHold | null>(null)
  const [actionType, setActionType] = useState<'release' | 'escalate' | 'reject'>('release')
  const [actionNotes, setActionNotes] = useState('')
  const [holdFormData, setHoldFormData] = useState<QuantityHoldFormData>({
    order_line_id: '',
    quantity_held: 1,
    hold_stage: 'kitting_packing',
    hold_reason: '',
    hold_notes: ''
  })

  // Filter holds
  const filteredHolds = holds.filter(hold => {
    const matchesSearch = searchTerm === '' || 
      hold.order_uid?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hold.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hold.customer_part_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hold.hold_reason.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStage = selectedStage === 'all' || hold.hold_stage === selectedStage
    
    const matchesStatus = selectedStatus === 'all' || 
      (selectedStatus === 'active' && hold.is_active) ||
      (selectedStatus === 'resolved' && !hold.is_active)
    
    return matchesSearch && matchesStage && matchesStatus
  })

  // Get available orders for creating holds
  const availableOrders = orders?.filter(order => {
    const quantities = order.quantities
    if (!quantities) return false
    
    // Orders that have quantities in kitting or QC stages
    return quantities.in_kitting_packing > 0 || 
           quantities.kitted_awaiting_qc > 0 ||
           quantities.in_screening_qc > 0
  }) || []

  // Reset form
  const resetForm = () => {
    setHoldFormData({
      order_line_id: '',
      quantity_held: 1,
      hold_stage: 'kitting_packing',
      hold_reason: '',
      hold_notes: ''
    })
  }

  // Handle create hold
  const handleCreate = async () => {
    if (!holdFormData.order_line_id || !holdFormData.hold_reason.trim()) {
      toast.error('Please fill in all required fields')
      return
    }

    const success = await createHold(holdFormData)
    if (success) {
      setShowCreateModal(false)
      resetForm()
    }
  }

  // Handle hold action
  const handleAction = async () => {
    if (!selectedHold) return

    let success = false
    switch (actionType) {
      case 'release':
        success = await releaseHold(selectedHold.id, actionNotes)
        break
      case 'escalate':
        if (!actionNotes.trim()) {
          toast.error('Please provide escalation notes')
          return
        }
        success = await escalateHold(selectedHold.id, actionNotes)
        break
      case 'reject':
        if (!actionNotes.trim()) {
          toast.error('Please provide rejection reason')
          return
        }
        success = await rejectHold(selectedHold.id, actionNotes)
        break
    }

    if (success) {
      setShowActionModal(false)
      setSelectedHold(null)
      setActionNotes('')
    }
  }

  // Open action modal
  const openActionModal = (hold: QuantityHold, action: 'release' | 'escalate' | 'reject') => {
    setSelectedHold(hold)
    setActionType(action)
    setActionNotes('')
    setShowActionModal(true)
  }

  // Get hold duration
  const getHoldDuration = (heldAt: string): string => {
    const now = new Date()
    const held = new Date(heldAt)
    const diffMs = now.getTime() - held.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `${diffDays}d ${diffHours % 24}h`
    }
    return `${diffHours}h`
  }

  // Get hold priority
  const getHoldPriority = (heldAt: string): 'urgent' | 'high' | 'normal' => {
    const hours = (new Date().getTime() - new Date(heldAt).getTime()) / (1000 * 60 * 60)
    if (hours > 48) return 'urgent'
    if (hours > 24) return 'high'
    return 'normal'
  }

  const stats = getHoldStats()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Quantity Holds Management</h2>
          <p className="text-sm text-gray-600">
            Monitor and manage quantity holds in kitting and QC processes
          </p>
        </div>
        {(canManageKittingHolds || canManageQCHolds) && (
          <Button onClick={() => setShowCreateModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Hold
          </Button>
        )}
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Active Holds</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalActiveHolds}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Quantity on Hold</p>
                <p className="text-2xl font-bold text-gray-900">{stats.quantityOnHold}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Avg Duration</p>
                <p className="text-2xl font-bold text-gray-900">{Math.round(stats.avgHoldDuration)}h</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <ArrowUp className="h-8 w-8 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Urgent Holds</p>
                <p className="text-2xl font-bold text-gray-900">{stats.urgentHolds}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by order UID, customer, part number, or reason..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Select value={selectedStage} onValueChange={setSelectedStage}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by stage" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Stages</SelectItem>
                  <SelectItem value="kitting_packing">Kitting/Packing</SelectItem>
                  <SelectItem value="screening_qc">Screening/QC</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Holds Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Quantity Holds ({filteredHolds.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading holds...</div>
          ) : filteredHolds.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No holds found</h3>
              <p className="text-gray-500">
                {searchTerm || selectedStage !== 'all' || selectedStatus !== 'active'
                  ? 'Try adjusting your search or filter criteria' 
                  : 'No quantity holds have been created yet'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stage
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reason
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredHolds.map((hold) => {
                    const priority = getHoldPriority(hold.held_at)
                    return (
                      <tr key={hold.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 flex items-center gap-1">
                              <Hash className="h-3 w-3" />
                              {hold.order_uid}
                            </div>
                            <div className="text-sm text-gray-500">{hold.customer_name}</div>
                            <div className="text-xs text-gray-400">{hold.customer_part_number}</div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <Badge variant={hold.hold_stage === 'kitting_packing' ? 'default' : 'secondary'}>
                            {hold.hold_stage.replace('_', ' ')}
                          </Badge>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{hold.quantity_held}</div>
                        </td>
                        <td className="px-4 py-4">
                          <div className="text-sm text-gray-900">{hold.hold_reason}</div>
                          {hold.hold_notes && (
                            <div className="text-xs text-gray-500 mt-1">{hold.hold_notes}</div>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className={cn(
                            "text-sm font-medium",
                            priority === 'urgent' && "text-red-600",
                            priority === 'high' && "text-orange-600",
                            priority === 'normal' && "text-gray-600"
                          )}>
                            {getHoldDuration(hold.held_at)}
                          </div>
                          <div className="text-xs text-gray-500">
                            {hold.held_by_name}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          {hold.is_active ? (
                            <Badge variant="destructive">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          ) : (
                            <div>
                              <Badge variant="default" className="bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {hold.resolution_action}
                              </Badge>
                              {hold.released_by_name && (
                                <div className="text-xs text-gray-500 mt-1">
                                  by {hold.released_by_name}
                                </div>
                              )}
                            </div>
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          {hold.is_active && (canManageKittingHolds || canManageQCHolds) && (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openActionModal(hold, 'release')}
                                className="text-green-600 hover:text-green-700"
                              >
                                <Play className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openActionModal(hold, 'escalate')}
                                className="text-orange-600 hover:text-orange-700"
                              >
                                <ArrowUp className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openActionModal(hold, 'reject')}
                                className="text-red-600 hover:text-red-700"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Hold Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Quantity Hold</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="order">Order *</Label>
              <Select 
                value={holdFormData.order_line_id} 
                onValueChange={(value) => setHoldFormData(prev => ({ ...prev, order_line_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an order" />
                </SelectTrigger>
                <SelectContent>
                  {availableOrders.map(order => (
                    <SelectItem key={order.id} value={order.id}>
                      {order.uid} - {order.customer_name} - {order.customer_part_number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="stage">Hold Stage *</Label>
              <Select 
                value={holdFormData.hold_stage} 
                onValueChange={(value: 'kitting_packing' | 'screening_qc') => 
                  setHoldFormData(prev => ({ ...prev, hold_stage: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {canManageKittingHolds && (
                    <SelectItem value="kitting_packing">Kitting/Packing</SelectItem>
                  )}
                  {canManageQCHolds && (
                    <SelectItem value="screening_qc">Screening/QC</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="quantity">Quantity to Hold *</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={holdFormData.quantity_held}
                onChange={(e) => setHoldFormData(prev => ({ 
                  ...prev, 
                  quantity_held: parseInt(e.target.value) || 1 
                }))}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="reason">Hold Reason *</Label>
              <Input
                id="reason"
                value={holdFormData.hold_reason}
                onChange={(e) => setHoldFormData(prev => ({ ...prev, hold_reason: e.target.value }))}
                placeholder="e.g., Quality concern, Missing documentation"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={holdFormData.hold_notes}
                onChange={(e) => setHoldFormData(prev => ({ ...prev, hold_notes: e.target.value }))}
                placeholder="Additional details about the hold..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate}>
              Create Hold
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Action Modal */}
      <Dialog open={showActionModal} onOpenChange={setShowActionModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {actionType === 'release' && 'Release Hold'}
              {actionType === 'escalate' && 'Escalate Hold'}
              {actionType === 'reject' && 'Reject Hold'}
            </DialogTitle>
          </DialogHeader>
          {selectedHold && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded-md">
                <div className="text-sm font-medium">{selectedHold.order_uid}</div>
                <div className="text-sm text-gray-600">{selectedHold.customer_name}</div>
                <div className="text-sm text-gray-500">
                  {selectedHold.quantity_held} units in {selectedHold.hold_stage.replace('_', ' ')}
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="action_notes">
                  {actionType === 'release' && 'Resolution Notes (Optional)'}
                  {actionType === 'escalate' && 'Escalation Reason *'}
                  {actionType === 'reject' && 'Rejection Reason *'}
                </Label>
                <Textarea
                  id="action_notes"
                  value={actionNotes}
                  onChange={(e) => setActionNotes(e.target.value)}
                  placeholder={
                    actionType === 'release' ? 'Optional resolution notes...' :
                    actionType === 'escalate' ? 'Explain why this needs escalation...' :
                    'Explain why this is being rejected...'
                  }
                  rows={3}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowActionModal(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAction}
              variant={actionType === 'reject' ? 'destructive' : 'default'}
            >
              {actionType === 'release' && 'Release Hold'}
              {actionType === 'escalate' && 'Escalate'}
              {actionType === 'reject' && 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}