/**
 * Lazy route component for code splitting
 */

import { Suspense, ComponentType, lazy } from 'react'
import { Loading } from '@/components/ui/loading'

interface LazyRouteProps {
  component: () => Promise<{ default: ComponentType<any> }>
  fallback?: React.ComponentType
}

export function LazyRoute({ component, fallback: Fallback = Loading }: LazyRouteProps) {
  const LazyComponent = lazy(component)

  return (
    <Suspense fallback={<Fallback />}>
      <LazyComponent />
    </Suspense>
  )
}

// Pre-defined lazy-loaded routes
export const LazyDashboard = () => import('@/pages/Dashboard')
export const LazyOrders = () => import('@/pages/Orders')
export const LazySettings = () => import('@/pages/Settings')
export const LazyLabelDesignerPage = () => import('@/pages/LabelDesignerPage')

// Workflow dashboard lazy routes
export const LazyQCDashboard = () => import('@/pages/QCDashboard')
export const LazyInvoicingDashboard = () => import('@/pages/InvoicingDashboard')

// Admin component lazy routes
export const LazyUserManagement = () => import('@/components/admin/UserManagement')
export const LazyRoleManagement = () => import('@/components/admin/RoleManagement')
export const LazyWorkflowMonitoring = () => import('@/components/admin/WorkflowMonitoring')

// Heavy component lazy routes
export const LazyLabelDesigner = () => import('@/components/labelDesigner/LabelDesigner')
export const LazyImageViewer = () => import('@/components/orders/ImageViewerModal')
export const LazyPDFReports = () => import('@/components/orders/PDFReportsModal')