import { useState } from 'react'
import { 
  CheckCircle2, 
  PauseCircle, 
  PlayCircle,
  Clock,
  User,
  Calendar,
  MessageSquare,
  ChevronRight,
  Loader2,
  AlertCircle,
  History as HistoryIcon
} from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'
import { useQuantityTracking } from '@/hooks/useQuantityTracking'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'

interface CTNumberCardProps {
  ctNumber: string
  currentState: string
  history?: CTHistoryItem
  orderLineId?: string
  onActionComplete?: () => void
}

interface CTHistoryItem {
  ctNumber: string
  currentState: string
  transitions: {
    fromState: string
    toState: string
    timestamp: string
    user: string
    reason?: string
  }[]
}

const HOLD_REASONS = [
  'Missing component',
  'Damaged part',
  'Wrong part delivered',
  'Packaging damaged',
  'Awaiting supervisor',
  'Documentation issue',
  'Other (specify)'
]

export function CTNumberCard({ 
  ctNumber, 
  currentState, 
  history, 
  orderLineId,
  onActionComplete 
}: CTNumberCardProps) {
  const { user } = useAuth()
  const { transitionQuantity } = useQuantityTracking()
  const [isLoading, setIsLoading] = useState(false)
  const [showHoldDialog, setShowHoldDialog] = useState(false)
  const [holdReason, setHoldReason] = useState('')
  const [holdNotes, setHoldNotes] = useState('')
  const [showHistory, setShowHistory] = useState(false)

  // Get state display properties
  const getStateIcon = (state: string) => {
    if (state.includes('completed') || state.includes('passed')) return CheckCircle2
    if (state.includes('hold')) return PauseCircle
    if (state.includes('reject')) return AlertCircle
    if (state.includes('in_')) return PlayCircle
    return Clock
  }

  const getStateColor = (state: string) => {
    if (state.includes('completed') || state.includes('passed')) return 'text-green-600'
    if (state.includes('hold')) return 'text-yellow-600'
    if (state.includes('reject')) return 'text-red-600'
    if (state.includes('in_')) return 'text-blue-600'
    return 'text-gray-600'
  }

  const formatStateName = (state: string) => {
    return state.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const StateIcon = getStateIcon(currentState)
  const stateColor = getStateColor(currentState)

  // Handle completing kitting for this CT
  const handleComplete = async () => {
    if (!orderLineId) {
      toast.error('Order information missing')
      return
    }

    setIsLoading(true)
    try {
      const result = await transitionQuantity({
        orderLineId,
        fromState: 'in_kitting_packing',
        toState: 'kitted_packed_awaiting_screening_qc',
        quantity: 1,
        ctNumbers: [ctNumber],
        reason: `CT ${ctNumber} kitting completed by ${user?.email?.split('@')[0]}`
      })

      if (result.success) {
        toast.success(`CT ${ctNumber} moved to QC`)
        
        // Broadcast the update
        const broadcastChannel = supabase.channel('kitting-broadcast')
        await broadcastChannel.send({
          type: 'broadcast',
          event: 'kitting-update',
          payload: {
            ctNumber,
            action: 'completed',
            userId: user?.id
          }
        })
        
        onActionComplete?.()
      } else {
        toast.error(result.message || 'Failed to complete kitting')
      }
    } catch (error) {
      console.error('Complete action failed:', error)
      toast.error('Failed to complete kitting')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle putting CT on hold
  const handleHold = async () => {
    if (!orderLineId || !holdReason) {
      toast.error('Please select a reason')
      return
    }

    setIsLoading(true)
    try {
      const fullReason = holdReason === 'Other (specify)' 
        ? holdNotes 
        : `${holdReason}${holdNotes ? ` - ${holdNotes}` : ''}`

      const result = await transitionQuantity({
        orderLineId,
        fromState: 'in_kitting_packing',
        toState: 'on_hold_at_kitting_packing',
        quantity: 1,
        ctNumbers: [ctNumber],
        reason: fullReason
      })

      if (result.success) {
        toast.success(`CT ${ctNumber} put on hold`)
        setShowHoldDialog(false)
        setHoldReason('')
        setHoldNotes('')
        
        // Broadcast the update
        const broadcastChannel = supabase.channel('kitting-broadcast')
        await broadcastChannel.send({
          type: 'broadcast',
          event: 'kitting-update',
          payload: {
            ctNumber,
            action: 'on_hold',
            reason: fullReason,
            userId: user?.id
          }
        })
        
        onActionComplete?.()
      } else {
        toast.error(result.message || 'Failed to put on hold')
      }
    } catch (error) {
      console.error('Hold action failed:', error)
      toast.error('Failed to put on hold')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle resuming from hold
  const handleResume = async () => {
    if (!orderLineId) {
      toast.error('Order information missing')
      return
    }

    setIsLoading(true)
    try {
      const result = await transitionQuantity({
        orderLineId,
        fromState: 'on_hold_at_kitting_packing',
        toState: 'in_kitting_packing',
        quantity: 1,
        ctNumbers: [ctNumber],
        reason: `CT ${ctNumber} resumed by ${user?.email?.split('@')[0]}`
      })

      if (result.success) {
        toast.success(`CT ${ctNumber} resumed`)
        
        // Broadcast the update
        const broadcastChannel = supabase.channel('kitting-broadcast')
        await broadcastChannel.send({
          type: 'broadcast',
          event: 'kitting-update',
          payload: {
            ctNumber,
            action: 'resumed',
            userId: user?.id
          }
        })
        
        onActionComplete?.()
      } else {
        toast.error(result.message || 'Failed to resume')
      }
    } catch (error) {
      console.error('Resume action failed:', error)
      toast.error('Failed to resume')
    } finally {
      setIsLoading(false)
    }
  }

  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    if (diffMins > 0) return `${diffMins}m ago`
    return 'Just now'
  }

  const lastTransition = history?.transitions[history.transitions.length - 1]

  return (
    <>
      <Card className="bg-white hover:shadow-md transition-all duration-200">
        <div className="p-4">
          {/* Header Row */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="font-mono text-sm">
                {ctNumber}
              </Badge>
              <div className={cn("flex items-center gap-1.5", stateColor)}>
                <StateIcon className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {formatStateName(currentState)}
                </span>
              </div>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-1">
              {currentState === 'in_kitting_packing' && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleComplete}
                    disabled={isLoading}
                    className="h-8 px-2 text-xs"
                  >
                    {isLoading ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <>
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Complete
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowHoldDialog(true)}
                    disabled={isLoading}
                    className="h-8 px-2 text-xs"
                  >
                    <PauseCircle className="h-3 w-3 mr-1" />
                    Hold
                  </Button>
                </>
              )}
              
              {currentState === 'on_hold_at_kitting_packing' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleResume}
                  disabled={isLoading}
                  className="h-8 px-2 text-xs"
                >
                  {isLoading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <>
                      <PlayCircle className="h-3 w-3 mr-1" />
                      Resume
                    </>
                  )}
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowHistory(!showHistory)}
                className="h-8 px-2 text-xs"
              >
                <HistoryIcon className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Last Activity Info */}
          {lastTransition && (
            <div className="flex items-center gap-4 text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>{lastTransition.user}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{formatTime(lastTransition.timestamp)}</span>
              </div>
              {lastTransition.reason && (
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  <span className="truncate max-w-[200px]">{lastTransition.reason}</span>
                </div>
              )}
            </div>
          )}

          {/* Expandable History */}
          {showHistory && history && history.transitions.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <div className="space-y-2">
                {history.transitions.slice().reverse().map((transition, idx) => (
                  <div key={idx} className="flex items-start gap-2 text-xs">
                    <ChevronRight className="h-3 w-3 text-gray-400 mt-0.5" />
                    <div className="flex-1">
                      <div>
                        <span className="font-medium">{transition.user}</span>
                        <span className="text-gray-500"> moved from </span>
                        <span className="font-medium text-gray-700">
                          {formatStateName(transition.fromState)}
                        </span>
                        <span className="text-gray-500"> to </span>
                        <span className="font-medium text-gray-700">
                          {formatStateName(transition.toState)}
                        </span>
                      </div>
                      <div className="text-gray-400 mt-0.5">
                        {new Date(transition.timestamp).toLocaleString()}
                      </div>
                      {transition.reason && (
                        <div className="text-gray-600 italic mt-1">
                          "{transition.reason}"
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Hold Dialog */}
      <Dialog open={showHoldDialog} onOpenChange={setShowHoldDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Put CT on Hold</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Reason for Hold
              </label>
              <Select value={holdReason} onValueChange={setHoldReason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  {HOLD_REASONS.map(reason => (
                    <SelectItem key={reason} value={reason}>
                      {reason}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">
                Additional Notes (Optional)
              </label>
              <Textarea
                value={holdNotes}
                onChange={(e) => setHoldNotes(e.target.value)}
                placeholder="Add any additional details..."
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowHoldDialog(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleHold}
              disabled={!holdReason || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                'Put on Hold'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}