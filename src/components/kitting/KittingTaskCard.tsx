import { useState } from 'react'
import { 
  Package, 
  Clock, 
  User, 
  ChevronDown, 
  ChevronUp, 
  CheckCircle2, 
  PauseCircle, 
  AlertCircle,
  PlayCircle,
  Timer,
  Target,
  Zap,
  BarChart3,
  Calendar,
  Hash,
  Layers,
  TrendingUp
} from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { KittingTask } from '@/hooks/useKittingQueue'
import { CTNumberCard } from './CTNumberCard'

interface KittingTaskCardProps {
  task: KittingTask
  onAssign: () => void
  onStart: () => void
  isCurrentUser: boolean
  ctHistory?: CTHistoryItem[]
}

interface CTHistoryItem {
  ctNumber: string
  currentState: string
  transitions: {
    fromState: string
    toState: string
    timestamp: string
    user: string
    reason?: string
  }[]
}

export function KittingTaskCard({ 
  task, 
  onAssign, 
  onStart, 
  isCurrentUser,
  ctHistory = [] 
}: KittingTaskCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-300'
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-300'
      case 'normal': return 'bg-blue-100 text-blue-700 border-blue-300'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const getComplexityIcon = (complexity: string) => {
    switch (complexity) {
      case 'simple': return { icon: Zap, color: 'text-green-600', label: 'Simple' }
      case 'medium': return { icon: Target, color: 'text-yellow-600', label: 'Medium' }
      case 'complex': return { icon: BarChart3, color: 'text-red-600', label: 'Complex' }
      default: return { icon: Package, color: 'text-gray-600', label: 'Unknown' }
    }
  }

  const formatTimeRemaining = (dueTime: Date) => {
    const now = new Date()
    const diff = dueTime.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (diff < 0) return 'Overdue'
    if (hours > 24) return `${Math.floor(hours / 24)}d ${hours % 24}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  // Removed unused functions - these are now in CTNumberCard

  const ComplexityInfo = getComplexityIcon(task.complexity)
  const timeRemaining = formatTimeRemaining(task.dueTime)
  const isOverdue = timeRemaining === 'Overdue'

  // Calculate progress based on CT numbers in different states
  const totalCTs = task.ctNumbers.length
  const processedCTs = ctHistory.filter(ct => 
    ct.currentState !== 'awaiting_kitting_packing'
  ).length
  const progressPercentage = totalCTs > 0 ? (processedCTs / totalCTs) * 100 : 0

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-lg",
      task.priority === 'urgent' && "border-red-300 bg-red-50/50",
      task.priority === 'high' && "border-orange-300 bg-orange-50/50"
    )}>
      <div className="p-4">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 space-y-2">
            {/* Order Info Row */}
            <div className="flex items-center gap-3 flex-wrap">
              <Badge className={cn("font-medium", getPriorityColor(task.priority))}>
                {task.priority.toUpperCase()}
              </Badge>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 font-mono">
                {task.order.uid}
              </Badge>
              <span className="font-medium text-gray-900">{task.order.customer_part_number}</span>
              <div className="flex items-center gap-1">
                <ComplexityInfo.icon className={cn("h-4 w-4", ComplexityInfo.color)} />
                <span className={cn("text-xs", ComplexityInfo.color)}>{ComplexityInfo.label}</span>
              </div>
            </div>

            {/* Description */}
            <p className="text-sm text-gray-700">{task.order.bpi_description}</p>

            {/* Metrics Row */}
            <div className="flex items-center gap-4 text-xs text-gray-600">
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3" />
                <span className="font-medium">{task.awaitingQuantity}</span> units
              </div>
              <div className="flex items-center gap-1">
                <Hash className="h-3 w-3" />
                <span className="font-medium">{totalCTs}</span> CT numbers
              </div>
              <div className="flex items-center gap-1">
                <Timer className="h-3 w-3" />
                <span>Est. {task.estimatedTime}</span>
              </div>
              <div className={cn("flex items-center gap-1", isOverdue && "text-red-600 font-medium")}>
                <Clock className="h-3 w-3" />
                <span>Due {timeRemaining}</span>
              </div>
              {task.assignedTo && (
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>{task.assignedTo}</span>
                </div>
              )}
            </div>

            {/* Progress Bar */}
            {totalCTs > 0 && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-gray-600">
                  <span>{processedCTs} of {totalCTs} CTs processed</span>
                  <span>{Math.round(progressPercentage)}%</span>
                </div>
                <Progress value={progressPercentage} className="h-2" />
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 ml-4">
            {!task.assignedTo ? (
              <Button variant="outline" size="sm" onClick={onAssign}>
                <User className="h-4 w-4 mr-1" />
                Assign to Me
              </Button>
            ) : isCurrentUser ? (
              <Button size="sm" onClick={onStart} className="bg-green-600 hover:bg-green-700">
                <PlayCircle className="h-4 w-4 mr-1" />
                Start Task
              </Button>
            ) : (
              <Badge variant="outline" className="text-xs">
                Assigned to {task.assignedTo}
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="ml-2"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Additional Info Row */}
        <div className="flex items-center gap-6 text-xs text-gray-500 border-t pt-2 mt-2">
          <div className="flex items-center gap-1">
            <Layers className="h-3 w-3" />
            <span>{task.order.category_name || 'Uncategorized'}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Created {new Date(task.order.created_at).toLocaleDateString()}</span>
          </div>
          {task.order.current_eta && (
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3" />
              <span>ETA: {new Date(task.order.current_eta).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>

      {/* Expandable CT Details Section */}
      {isExpanded && (
        <div className="border-t bg-gray-50 p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-sm text-gray-900">CT Number Details</h4>
            <div className="text-xs text-gray-500">
              {task.ctNumbers.length} CT Numbers
            </div>
          </div>
          
          {task.ctNumbers.length === 0 ? (
            <div className="bg-white rounded-lg p-4 border border-gray-200 text-center">
              <Package className="h-8 w-8 text-gray-300 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No CT numbers assigned yet</p>
            </div>
          ) : (
            <div className="space-y-2">
              {task.ctNumbers.map((ctNumber) => {
                const history = ctHistory.find(h => h.ctNumber === ctNumber)
                const currentState = history?.currentState || 'awaiting_kitting_packing'
                
                return (
                  <CTNumberCard
                    key={ctNumber}
                    ctNumber={ctNumber}
                    currentState={currentState}
                    history={history}
                    orderLineId={task.order.id}
                    onActionComplete={() => {
                      // This will be called when an action is completed
                      // The real-time updates will handle the refresh
                    }}
                  />
                )
              })}
            </div>
          )}
        </div>
      )}
    </Card>
  )
}