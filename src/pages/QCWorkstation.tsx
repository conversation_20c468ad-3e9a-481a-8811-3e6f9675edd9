import React, { useState, useEffect } from 'react'
import { BarChart3, Camera, Eye, AlertTriangle, CheckCircle2, XCircle, TrendingUp, Image } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import ScanningWorkInterface from '@/components/scanning/ScanningWorkInterface'
import { useQCQueue } from '@/hooks/useQCQueue'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface QCSession {
  startTime: Date
  itemsInspected: number
  itemsPassed: number
  itemsRejected: number
  itemsOnHold: number
  lastActivity: Date
}

export default function QCWorkstation() {
  const [qcSession, setQCSession] = useState<QCSession>({
    startTime: new Date(),
    itemsInspected: 0,
    itemsPassed: 0,
    itemsRejected: 0,
    itemsOnHold: 0,
    lastActivity: new Date()
  })
  const [showDetailedStats, setShowDetailedStats] = useState(false)
  const [qualityScore, setQualityScore] = useState(100)
  
  const { user } = useAuth()
  const { 
    qcQueue, 
    isLoading, 
    totalItems, 
    inProgress, 
    passedToday,
    rejectedToday,
    holdItemsCount 
  } = useQCQueue()

  // Update session stats when QC work is completed
  const handleQCComplete = (ctNumber: string, action: any) => {
    const now = new Date()
    setQCSession(prev => {
      const updated = {
        ...prev,
        itemsInspected: prev.itemsInspected + 1,
        lastActivity: now
      }
      
      switch (action.type) {
        case 'complete':
          updated.itemsPassed = prev.itemsPassed + 1
          break
        case 'reject':
          updated.itemsRejected = prev.itemsRejected + 1
          break
        case 'hold':
          updated.itemsOnHold = prev.itemsOnHold + 1
          break
      }
      
      return updated
    })
    
    // Update quality score
    const newScore = qcSession.itemsInspected > 0 
      ? Math.round(((qcSession.itemsPassed + 1) / (qcSession.itemsInspected + 1)) * 100)
      : 100
    setQualityScore(newScore)
    
    // Show appropriate feedback
    switch (action.type) {
      case 'complete':
        toast.success(`QC PASSED: ${ctNumber}`)
        break
      case 'reject':
        toast.error(`QC REJECTED: ${ctNumber}`)
        break
      case 'hold':
        toast.warning(`QC HOLD: ${ctNumber}`)
        break
    }
  }

  // Calculate QC metrics
  const getPassRate = (): number => {
    return qcSession.itemsInspected > 0 
      ? Math.round((qcSession.itemsPassed / qcSession.itemsInspected) * 100)
      : 100
  }

  const getInspectionRate = (): number => {
    const diffMs = Date.now() - qcSession.startTime.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    return diffHours > 0 ? Math.round(qcSession.itemsInspected / diffHours) : 0
  }

  const getSessionDuration = (): string => {
    const diffMs = Date.now() - qcSession.startTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const hours = Math.floor(diffMins / 60)
    const mins = diffMins % 60
    
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* QC Station Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <h1 className="text-xl font-semibold text-gray-900">QC Workstation</h1>
            <Badge variant="outline">{user?.email?.split('@')[0] || 'Inspector'}</Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge 
              variant={qualityScore >= 95 ? 'default' : qualityScore >= 90 ? 'secondary' : 'destructive'}
              className="text-xs"
            >
              {qualityScore}% Pass Rate
            </Badge>
            
            <Button
              variant={showDetailedStats ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowDetailedStats(!showDetailedStats)}
            >
              <BarChart3 className="w-4 h-4 mr-1" />
              Stats
            </Button>
          </div>
        </div>
      </div>

      {/* Detailed QC Statistics */}
      {showDetailedStats && (
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Eye className="w-4 h-4 text-blue-500" />
                <div>
                  <div className="text-xs text-gray-500">Inspected</div>
                  <div className="font-medium">{qcSession.itemsInspected}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                <div>
                  <div className="text-xs text-gray-500">Passed</div>
                  <div className="font-medium">{qcSession.itemsPassed}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-red-500" />
                <div>
                  <div className="text-xs text-gray-500">Rejected</div>
                  <div className="font-medium">{qcSession.itemsRejected}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-purple-500" />
                <div>
                  <div className="text-xs text-gray-500">Rate/Hour</div>
                  <div className="font-medium">{getInspectionRate()}</div>
                </div>
              </div>
            </Card>
          </div>
          
          {/* Quality Metrics */}
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Pass Rate (Session)</span>
                <span>{getPassRate()}%</span>
              </div>
              <Progress value={getPassRate()} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Quality Score</span>
                <span>{qualityScore}%</span>
              </div>
              <Progress 
                value={qualityScore} 
                className="h-2"
                indicatorClassName={
                  qualityScore >= 95 ? 'bg-green-500' : 
                  qualityScore >= 90 ? 'bg-yellow-500' : 'bg-red-500'
                }
              />
            </div>
          </div>
        </div>
      )}

      {/* QC Queue Overview */}
      <div className="p-4">
        <Card className="p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="font-medium text-gray-900">QC Queue</h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                <span>{totalItems} awaiting QC</span>
                <span>•</span>
                <span>{inProgress} in progress</span>
                <span>•</span>
                <span>{holdItemsCount} on hold</span>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {totalItems - qcSession.itemsInspected}
              </div>
              <div className="text-xs text-gray-500">remaining</div>
            </div>
          </div>
        </Card>

        {/* Quality Alert Cards */}
        {qcSession.itemsRejected > 0 && (
          <Card className="p-4 mb-4 border-red-200 bg-red-50">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <div>
                <div className="font-medium text-red-900">Quality Alert</div>
                <div className="text-sm text-red-700">
                  {qcSession.itemsRejected} items rejected this session. Review procedures.
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Main QC Scanning Interface */}
      <ScanningWorkInterface
        workType="qc"
        onWorkComplete={handleQCComplete}
        className="pb-6"
      />

      {/* QC Tools & Actions */}
      <div className="p-4">
        <Card className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">QC Tools</h3>
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" size="sm" className="justify-start">
              <Camera className="w-4 h-4 mr-2" />
              Photo Capture
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <Image className="w-4 h-4 mr-2" />
              Master Images
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <BarChart3 className="w-4 h-4 mr-2" />
              QC Reports
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Resolve Holds
            </Button>
          </div>
        </Card>
      </div>

      {/* Daily Summary */}
      <div className="p-4">
        <Card className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">Today's Summary</h3>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{passedToday}</div>
              <div className="text-xs text-gray-500">Passed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{rejectedToday}</div>
              <div className="text-xs text-gray-500">Rejected</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{getSessionDuration()}</div>
              <div className="text-xs text-gray-500">Session Time</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Floating Quality Alert Button */}
      <div className="fixed bottom-6 right-6">
        <Button
          size="lg"
          variant={qcSession.itemsRejected > 0 ? 'destructive' : 'default'}
          className="rounded-full w-14 h-14 shadow-lg"
          onClick={() => toast.info('Supervisor notified of quality concern')}
        >
          <AlertTriangle className="w-6 h-6" />
        </Button>
      </div>
    </div>
  )
}