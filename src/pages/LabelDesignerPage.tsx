import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, TestTube } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LabelDesigner } from '@/components/labelDesigner/LabelDesigner'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuth'
import type { LabelTemplate } from '@/types/labelDesigner'

export function LabelDesignerPage() {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useAuth()
  const [, setIsSaving] = useState(false)
  const [templateName, setTemplateName] = useState('')
  const [templateCategory, setTemplateCategory] = useState<LabelTemplate['category']>('generic')
  const [selectedPrinter, setSelectedPrinter] = useState<string>('')
  const [isEditMode, setIsEditMode] = useState(false)
  const [editTemplateId, setEditTemplateId] = useState<string | null>(null)

  // Get data from location state
  const orderData = location.state?.orderData
  const editTemplate = location.state?.editTemplate

  // MCP Printing integration
  const { 
    useZebraPrinters, 
    printZebraLabel, 
    isConnected: isMCPConnected
  } = useMCPPrinting()
  
  const { data: zebraPrinters = [] } = useZebraPrinters()

  // Initialize edit mode if template is provided
  useEffect(() => {
    if (editTemplate) {
      setIsEditMode(true)
      setEditTemplateId(editTemplate.id)
      setTemplateName(editTemplate.name)
      setTemplateCategory(editTemplate.category)
      toast.info(`Editing template: ${editTemplate.name}`)
    }
  }, [editTemplate])

  // Auto-select first printer
  useEffect(() => {
    if (zebraPrinters.length > 0 && !selectedPrinter) {
      setSelectedPrinter(zebraPrinters[0].id)
    }
  }, [zebraPrinters, selectedPrinter])

  const handleSaveTemplate = async (templateData: any) => {
    if (!templateName.trim()) {
      toast.error('Please enter a template name')
      return
    }

    setIsSaving(true)
    try {
      const template: Partial<LabelTemplate> = {
        name: templateName,
        category: templateCategory,
        canvas_data: templateData.canvas_data,
        label_size: templateData.label_size,
      }

      if (isEditMode && editTemplateId) {
        // Update existing template
        template.updated_at = new Date().toISOString()
        
        const { error } = await supabase
          .from('label_templates')
          .update(template)
          .eq('id', editTemplateId)

        if (error) throw error
        toast.success('Template updated successfully')
      } else {
        // Create new template
        template.created_by = user?.id || ''
        template.created_at = new Date().toISOString()
        
        const { error } = await supabase
          .from('label_templates')
          .insert(template)

        if (error) throw error
        toast.success('Template saved successfully')
      }

      navigate('/settings?tab=templates')
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error(`Failed to ${isEditMode ? 'update' : 'save'} template`)
    } finally {
      setIsSaving(false)
    }
  }

  const handlePrint = async (zplCode: string) => {
    try {
      if (isMCPConnected && selectedPrinter && zebraPrinters.length > 0) {
        // Use MCP printing system
        const printResult = await printZebraLabel(selectedPrinter, zplCode, {
          template_name: templateName || 'Custom Label',
          created_by: user?.email || 'unknown',
          print_method: 'label_designer'
        })

        if (printResult.success) {
          toast.success(`Label sent to printer: ${zebraPrinters.find((p: any) => p.id === selectedPrinter)?.name}`)
        } else {
          throw new Error(printResult.error || 'Print failed')
        }
      } else {
        // Fallback: download ZPL file
        const blob = new Blob([zplCode], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `label_${templateName || 'custom'}_${Date.now()}.zpl`
        a.click()
        URL.revokeObjectURL(url)
        
        toast.success('ZPL file downloaded (MCP not available)')
      }
    } catch (error) {
      console.error('Error handling print:', error)
      toast.error('Failed to print label')
      
      // Fallback on error
      try {
        const blob = new Blob([zplCode], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `label_${templateName || 'custom'}_${Date.now()}.zpl`
        a.click()
        URL.revokeObjectURL(url)
        toast.info('ZPL file downloaded as fallback')
      } catch (fallbackError) {
        console.error('Fallback download failed:', fallbackError)
      }
    }
  }

  const handleTestPrint = async () => {
    if (!selectedPrinter) {
      toast.error('Please select a printer first')
      return
    }

    try {
      const testZPL = `^XA
^FO50,50^A0N,30,30^FDTest Print from Label Designer^FS
^FO50,100^A0N,20,20^FDTemplate: ${templateName || 'Unnamed'}^FS
^FO50,130^A0N,20,20^FDPrinted: ${new Date().toLocaleString()}^FS
^FO50,160^A0N,20,20^FDUser: ${user?.email || 'Unknown'}^FS
^XZ`

      const result = await printZebraLabel(selectedPrinter, testZPL, {
        template_name: 'Test Print',
        created_by: user?.email || 'unknown',
        print_method: 'test_print'
      })

      if (result.success) {
        toast.success('Test print sent successfully')
      } else {
        toast.error(`Test print failed: ${result.error}`)
      }
    } catch (error) {
      console.error('Test print error:', error)
      toast.error('Test print failed')
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-lg font-semibold">
            Label Designer {isEditMode && <span className="text-blue-600">(Editing: {editTemplate?.name})</span>}
          </h1>
        </div>
        
        <div className="flex items-center gap-2">
          {/* MCP Status */}
          <MCPConnectionStatus compact={true} showDetails={false} />
          
          {/* Printer Selection */}
          {zebraPrinters.length > 0 && (
            <select
              className="px-3 py-1 text-sm border rounded-md"
              value={selectedPrinter}
              onChange={(e) => setSelectedPrinter(e.target.value)}
              title="Select printer for testing"
            >
              <option value="">Select Printer...</option>
              {zebraPrinters.map((printer: any) => (
                <option key={printer.id} value={printer.id}>
                  {printer.name} ({printer.ip_address})
                </option>
              ))}
            </select>
          )}
          
          {/* Test Print Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestPrint}
            disabled={!selectedPrinter || !isMCPConnected}
            title="Send test print to selected printer"
          >
            <TestTube className="h-4 w-4 mr-1" />
            Test
          </Button>
          
          <input
            type="text"
            placeholder="Template name..."
            className="px-3 py-1 text-sm border rounded-md"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
          />
          <select
            className="px-3 py-1 text-sm border rounded-md"
            value={templateCategory}
            onChange={(e) => setTemplateCategory(e.target.value as any)}
          >
            <option value="generic">Generic</option>
            <option value="ct_label">CT Label</option>
            <option value="shipping">Shipping</option>
            <option value="internal">Internal</option>
            <option value="custom">Custom</option>
          </select>
        </div>
      </div>

      {/* Designer */}
      <div className="flex-1 p-4 overflow-hidden">
        <LabelDesigner
          initialTemplate={editTemplate}
          onSave={handleSaveTemplate}
          onPrint={handlePrint}
        />
      </div>

      {/* Order Data Preview (if available) */}
      {orderData && (
        <Card className="m-4">
          <CardHeader className="py-3">
            <CardTitle className="text-sm">Order Data Available</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <div className="grid grid-cols-4 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">UID:</span> {orderData.uid}
              </div>
              <div>
                <span className="text-muted-foreground">Part:</span> {orderData.customer_part_number}
              </div>
              <div>
                <span className="text-muted-foreground">Qty:</span> {orderData.total_quantity}
              </div>
              <div>
                <span className="text-muted-foreground">Customer:</span> {orderData.customer_name}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}