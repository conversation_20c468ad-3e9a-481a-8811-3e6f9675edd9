import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useInvoicing } from '@/hooks/useInvoicing'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Loading } from '@/components/ui/loading'
import { 
  FileText, 
  DollarSign, 
  Clock, 
  CheckCircle2,
  User,
  Search,
  RefreshCw,
  Plus,
  Download,
  Calendar,
  Building2,
  Package,
  AlertCircle,
  Eye,
  Send,
  Printer,
  Mail,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Real invoicing data from useInvoicing hook

export function InvoicingDashboard() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [customerFilter, setCustomerFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  
  // Use real invoicing hook
  const {
    readyToInvoice,
    pendingInvoices,
    isLoading,
    error,
    stats,
    createInvoice,
    createQuickInvoice,
    sendInvoice,
    updateInvoiceStatus,
    formatCurrency,
    getTotalSelectedValue,
    refresh
  } = useInvoicing()

  // Show error if any
  useEffect(() => {
    if (error) {
      console.error('Invoicing dashboard error:', error)
    }
  }, [error])

  // Filter ready to invoice items
  const filteredItems = readyToInvoice.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.order.uid?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.order.customer_part_number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.order.bpi_description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.customerInvoiceRef.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCustomer = customerFilter === 'all' || 
      item.order.customer?.name?.toLowerCase().includes(customerFilter.toLowerCase())
    
    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter

    return matchesSearch && matchesCustomer && matchesPriority
  })

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredItems.map(item => item.id))
    }
  }

  const handleCreateInvoice = async (itemIds: string[] = selectedItems) => {
    const invoiceId = await createInvoice(itemIds)
    if (invoiceId) {
      console.log('📄 Created invoice:', invoiceId)
      setSelectedItems([]) // Clear selection after successful creation
    }
  }

  const handleQuickInvoice = async (itemId: string) => {
    const invoiceId = await createQuickInvoice(itemId)
    if (invoiceId) {
      console.log('⚡ Created quick invoice:', invoiceId)
    }
  }

  const handleViewInvoice = (invoiceId: string) => {
    console.log('👁️ Viewing invoice:', invoiceId)
    // TODO: Open invoice viewer modal
  }

  const handleSendInvoice = async (invoiceId: string) => {
    const success = await sendInvoice(invoiceId, 'email')
    if (success) {
      console.log('📧 Sent invoice:', invoiceId)
    }
  }

  const handlePrintInvoice = async (invoiceId: string) => {
    const success = await sendInvoice(invoiceId, 'print')
    if (success) {
      console.log('🖨️ Printed invoice:', invoiceId)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-300'
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-300'
      case 'normal': return 'bg-blue-100 text-blue-700 border-blue-300'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-700'
      case 'pending_approval': return 'bg-yellow-100 text-yellow-700'
      case 'ready_to_send': return 'bg-green-100 text-green-700'
      case 'sent': return 'bg-blue-100 text-blue-700'
      case 'paid': return 'bg-emerald-100 text-emerald-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} ago`
    }
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  }

  const formatTimeUntil = (date: Date) => {
    const now = new Date()
    const diff = date.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`
    }
    return `${hours} hour${hours > 1 ? 's' : ''}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading size="lg" message="Loading invoicing dashboard..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoicing Dashboard</h1>
          <p className="text-gray-600">Manage customer invoicing and billing operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <User className="h-3 w-3 mr-1" />
            {user?.email?.split('@')[0] || 'Unknown'}
          </Badge>
          <Button variant="outline" size="sm" onClick={refresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Invoicing Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Ready to Invoice</p>
              <p className="text-2xl font-bold">{stats.readyToInvoice}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Invoices</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingInvoices}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Value</p>
              <p className="text-xl font-bold text-green-600">
                {formatCurrency(stats.totalValue)}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Urgent Items</p>
              <p className="text-2xl font-bold text-red-600">
                {stats.urgentItems}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>
      </div>

      {/* Selection Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckCircle2 className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">
                {selectedItems.length} item{selectedItems.length > 1 ? 's' : ''} selected
              </span>
              <Badge variant="outline" className="bg-green-100 text-green-700">
                Total: {formatCurrency(getTotalSelectedValue(selectedItems))}
              </Badge>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedItems([])}
              >
                Clear Selection
              </Button>
              <Button
                size="sm"
                onClick={() => handleCreateInvoice()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Invoice ({selectedItems.length})
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Pending Invoices Section */}
      {pendingInvoices.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-yellow-600" />
            Pending Invoices ({pendingInvoices.length})
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {pendingInvoices.map((invoice) => (
              <div key={invoice.id} className="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Badge className="bg-yellow-600 text-white">
                        {invoice.invoiceNumber}
                      </Badge>
                      <span className="font-medium">{invoice.customerName}</span>
                      <Badge variant="outline" className={cn("text-xs", getStatusColor(invoice.status))}>
                        {invoice.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-700">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-3 w-3" />
                        <span>{formatCurrency(invoice.totalAmount)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Package className="h-3 w-3" />
                        <span>{invoice.orderCount} orders</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Due in {formatTimeUntil(invoice.dueDate)}</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Created {formatTimeAgo(invoice.createdDate)} • Assigned to {invoice.assignedTo}
                    </p>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewInvoice(invoice.id)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    {invoice.status === 'ready_to_send' && (
                      <Button
                        size="sm"
                        onClick={() => handleSendInvoice(invoice.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Send className="h-4 w-4 mr-1" />
                        Send
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePrintInvoice(invoice.id)}
                    >
                      <Printer className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by UID, part number, description, or invoice ref..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={customerFilter}
              onChange={(e) => setCustomerFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Customers</option>
              <option value="hp">HP Inc.</option>
              <option value="lenovo">Lenovo Corp.</option>
            </select>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="normal">Normal</option>
              <option value="low">Low</option>
            </select>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
            >
              {selectedItems.length === filteredItems.length ? 'Deselect All' : 'Select All'}
            </Button>
          </div>
        </div>
      </div>

      {/* Ready to Invoice Items */}
      <div className="space-y-3">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <FileText className="h-5 w-5 mr-2 text-blue-600" />
          Ready to Invoice ({filteredItems.length})
        </h2>
        
        {filteredItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>No items ready for invoicing found matching your filters.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredItems.map((item) => (
              <div
                key={item.id}
                className={cn(
                  "bg-white border rounded-lg p-4 hover:shadow-md transition-all cursor-pointer",
                  item.priority === 'urgent' && "border-red-300 bg-red-50",
                  selectedItems.includes(item.id) && "border-blue-500 bg-blue-50"
                )}
                onClick={() => handleSelectItem(item.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <Badge className={cn("font-medium", getPriorityColor(item.priority))}>
                          {item.priority.toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {item.order.uid}
                        </Badge>
                        <span className="font-medium">{item.order.customer_part_number}</span>
                        <Badge variant="outline" className="bg-green-100 text-green-700">
                          {formatCurrency(item.totalValue)}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-700 mt-1">{item.order.bpi_description}</p>
                      
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Building2 className="h-3 w-3" />
                          <span>{item.order.customer?.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Package className="h-3 w-3" />
                          <span>{item.readyQuantity} units @ {formatCurrency(item.unitPrice)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>QC passed {formatTimeAgo(item.qcPassedDate)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Target className="h-3 w-3" />
                          <span>Ship in {formatTimeUntil(item.estimatedShipDate)}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">
                          PO: {item.order.po_number}
                        </Badge>
                        <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700">
                          Ref: {item.customerInvoiceRef}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleQuickInvoice(item.id)
                      }}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Quick Invoice
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-50 border rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-3">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button variant="outline" size="sm">
            <Mail className="h-4 w-4 mr-2" />
            Email Reports
          </Button>
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Review
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Invoice Templates
          </Button>
        </div>
      </div>
    </div>
  )
}