import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useKittingQueue } from '@/hooks/useKittingQueue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Loading } from '@/components/ui/loading'
import { KittingTaskCard } from '@/components/kitting/KittingTaskCard'
import { Card } from '@/components/ui/card'
import { 
  Package, 
  Clock, 
  AlertCircle, 
  CheckCircle2,
  User,
  PlayCircle,
  PauseCircle,
  Search,
  RefreshCw,
  Timer,
  Activity,
  TrendingUp
} from 'lucide-react'

// Real kitting queue data from useKittingQueue hook

export function KittingDashboard() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [assignmentFilter, setAssignmentFilter] = useState('all')
  
  // Use real kitting queue hook
  const {
    kittingQueue,
    currentTasks,
    isLoading,
    error,
    stats,
    assignTaskToMe,
    startKittingTask,
    completeKittingTask,
    pauseKittingTask,
    refresh
  } = useKittingQueue()

  // Show error if any
  useEffect(() => {
    if (error) {
      console.error('Kitting dashboard error:', error)
    }
  }, [error])

  // Debug logging for data updates
  useEffect(() => {
    console.log('📊 Kitting Dashboard Data Updated:', {
      timestamp: new Date().toISOString(),
      queueLength: kittingQueue.length,
      stats,
      currentTasks: currentTasks.length,
      isLoading
    })
  }, [kittingQueue, stats, currentTasks, isLoading])

  // Filter kitting queue
  const filteredQueue = kittingQueue.filter(task => {
    const matchesSearch = searchQuery === '' || 
      task.order.uid?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.order.customer_part_number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.order.bpi_description?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter
    
    const matchesAssignment = assignmentFilter === 'all' ||
      (assignmentFilter === 'unassigned' && !task.assignedTo) ||
      (assignmentFilter === 'assigned' && task.assignedTo) ||
      (assignmentFilter === 'mine' && task.assignedTo === user?.email?.split('@')[0])

    return matchesSearch && matchesPriority && matchesAssignment
  })

  const handleStartTask = async (taskId: string) => {
    const success = await startKittingTask(taskId)
    if (success) {
      console.log('✅ Started kitting task:', taskId)
    }
  }

  const handlePauseTask = async (taskId: string) => {
    const success = await pauseKittingTask(taskId)
    if (success) {
      console.log('⏸️ Paused kitting task:', taskId)
    }
  }

  const handleCompleteTask = async (taskId: string) => {
    const success = await completeKittingTask(taskId)
    if (success) {
      console.log('✅ Completed kitting task:', taskId)
    }
  }

  const handleAssignToMe = async (taskId: string) => {
    const success = await assignTaskToMe(taskId)
    if (success) {
      console.log('👤 Assigned task to me:', taskId)
    }
  }

  // Removed auto-refresh as it's now handled by useKittingQueue hook with polling

  const formatTimeRemaining = (dueTime: Date) => {
    const now = new Date()
    const diff = dueTime.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const getTaskDuration = (startedAt: Date) => {
    const now = new Date()
    const diff = now.getTime() - startedAt.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes} min`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading size="lg" message="Loading kitting dashboard..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Kitting Dashboard</h1>
          <p className="text-gray-600">Manage kitting and packing operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => {
              console.log('🔄 Manual refresh triggered')
              refresh()
            }}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <User className="h-3 w-3 mr-1" />
            {user?.email?.split('@')[0] || 'Unknown'}
          </Badge>
        </div>
      </div>

      {/* Current Tasks Section */}
      {currentTasks.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <PlayCircle className="h-5 w-5 mr-2 text-blue-600" />
              My Active Tasks
            </h2>
            <Badge className="bg-blue-100 text-blue-700">{currentTasks.length} Active</Badge>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {currentTasks.map((task) => (
              <Card key={task.id} className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-300">
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Badge className="bg-blue-600 text-white font-mono">
                          {task.orderUid}
                        </Badge>
                        <span className="font-medium text-gray-900">{task.partNumber}</span>
                        <Badge variant="outline" className="bg-white">
                          {task.quantity} units
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-700">{task.description}</p>
                      <div className="flex items-center gap-4 mt-3 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Timer className="h-3 w-3" />
                          <span>Started {getTaskDuration(task.startedAt)} ago</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>Est. {formatTimeRemaining(task.estimatedCompletion)} remaining</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Activity className="h-3 w-3" />
                          <span>{task.progress}% complete</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 ml-4">
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-700">{task.progress}%</div>
                        <div className="text-xs text-gray-500">Progress</div>
                      </div>
                      
                      <div className="flex flex-col gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePauseTask(task.id)}
                          className="bg-white"
                        >
                          <PauseCircle className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleCompleteTask(task.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle2 className="h-4 w-4 mr-1" />
                          Complete
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-3">
                    <div className="w-full bg-white/50 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Queue Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Queue</p>
              <p className="text-3xl font-bold text-gray-900">{stats.totalQueue}</p>
              <p className="text-xs text-gray-500 mt-1">Items awaiting kitting</p>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>
        
        <Card className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Unassigned</p>
              <p className="text-3xl font-bold text-orange-600">{stats.unassigned}</p>
              <p className="text-xs text-gray-500 mt-1">Need assignment</p>
            </div>
            <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
        
        <Card className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Urgent Tasks</p>
              <p className="text-3xl font-bold text-red-600">{stats.urgentTasks}</p>
              <p className="text-xs text-gray-500 mt-1">Priority attention</p>
            </div>
            <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Clock className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </Card>
        
        <Card className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">My Tasks</p>
              <p className="text-3xl font-bold text-green-600">{stats.myTasks}</p>
              <p className="text-xs text-gray-500 mt-1">Assigned to you</p>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <User className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by UID, part number, or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center gap-2">
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-4 py-2 border border-gray-200 rounded-lg text-sm bg-white hover:border-gray-300 transition-colors"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">🔴 Urgent</option>
              <option value="high">🟠 High</option>
              <option value="normal">🔵 Normal</option>
              <option value="low">⚪ Low</option>
            </select>
            <select
              value={assignmentFilter}
              onChange={(e) => setAssignmentFilter(e.target.value)}
              className="px-4 py-2 border border-gray-200 rounded-lg text-sm bg-white hover:border-gray-300 transition-colors"
            >
              <option value="all">All Tasks</option>
              <option value="unassigned">👤 Unassigned</option>
              <option value="assigned">✅ Assigned</option>
              <option value="mine">⭐ My Tasks</option>
            </select>
            <div className="border-l pl-2 ml-2">
              <Button variant="ghost" size="sm" onClick={() => {
                setSearchQuery('')
                setPriorityFilter('all')
                setAssignmentFilter('all')
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {/* Kitting Queue */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Package className="h-5 w-5 mr-2 text-blue-600" />
            Kitting Queue
          </h2>
          <div className="flex items-center gap-2">
            <Badge variant="outline">{filteredQueue.length} tasks</Badge>
            {stats.totalQueue > 0 && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                <TrendingUp className="h-3 w-3 mr-1" />
                {Math.round((stats.inProgress / stats.totalQueue) * 100)}% in progress
              </Badge>
            )}
          </div>
        </div>
        
        {filteredQueue.length === 0 ? (
          <Card className="p-12">
            <div className="text-center text-gray-500">
              <Package className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="font-medium">No kitting tasks found</p>
              <p className="text-sm mt-1">Try adjusting your filters or refresh the queue</p>
            </div>
          </Card>
        ) : (
          <div className="space-y-3">
            {filteredQueue.map((task) => (
              <KittingTaskCard
                key={task.id}
                task={task}
                onAssign={() => handleAssignToMe(task.id)}
                onStart={() => handleStartTask(task.id)}
                isCurrentUser={task.assignedTo === user?.email?.split('@')[0]}
                ctHistory={task.ctHistory}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}