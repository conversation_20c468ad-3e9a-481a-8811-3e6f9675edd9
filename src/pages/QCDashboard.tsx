import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useQCQueue } from '@/hooks/useQCQueue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Loading } from '@/components/ui/loading'
import { 
  BarChart3, 
  Clock, 
  AlertCircle, 
  CheckCircle2,
  XCircle,
  User,
  Image,
  PlayCircle,
  PauseCircle,
  Search,
  RefreshCw,
  Eye,
  FileText,
  Target,
  Zap,
  Timer,
  ThumbsUp,
  ThumbsDown,
  Pause
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Real QC queue data from useQCQueue hook

export function QCDashboard() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [assignmentFilter, setAssignmentFilter] = useState('all')
  const [qcTypeFilter, setQCTypeFilter] = useState('all')
  
  // Use real QC queue hook
  const {
    qcQueue,
    currentTasks,
    holdItems,
    isLoading,
    error,
    stats,
    assignQCTaskToMe,
    startQCTask,
    passQCTask,
    failQCTask,
    holdQCTask,
    resolveQCHold,
    pauseQCTask,

    refresh
  } = useQCQueue()

  // Show error if any
  useEffect(() => {
    if (error) {
      console.error('QC dashboard error:', error)
    }
  }, [error])

  // Filter QC queue
  const filteredQueue = qcQueue.filter(task => {
    const matchesSearch = searchQuery === '' || 
      task.order.uid?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.order.customer_part_number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.order.bpi_description?.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter
    const matchesQCType = qcTypeFilter === 'all' || task.qcType === qcTypeFilter
    
    const matchesAssignment = assignmentFilter === 'all' ||
      (assignmentFilter === 'unassigned' && !task.assignedTo) ||
      (assignmentFilter === 'assigned' && task.assignedTo) ||
      (assignmentFilter === 'mine' && task.assignedTo === user?.email?.split('@')[0])

    return matchesSearch && matchesPriority && matchesAssignment && matchesQCType
  })

  const handleStartQC = async (taskId: string) => {
    const success = await startQCTask(taskId)
    if (success) {
      console.log('🔍 Started QC task:', taskId)
    }
  }

  const handlePauseQC = async (taskId: string) => {
    const success = await pauseQCTask(taskId)
    if (success) {
      console.log('⏸️ Paused QC task:', taskId)
    }
  }

  const handlePassQC = async (taskId: string) => {
    const success = await passQCTask(taskId, 'QC passed - all checks successful')
    if (success) {
      console.log('✅ Passed QC task:', taskId)
    }
  }

  const handleFailQC = async (taskId: string) => {
    const success = await failQCTask(taskId, 'QC failed - quality issues detected', 'Manual review required')
    if (success) {
      console.log('❌ Failed QC task:', taskId)
    }
  }

  const handleHoldQC = async (taskId: string) => {
    const success = await holdQCTask(taskId, 'QC hold - pending review', 'Requires supervisor attention')
    if (success) {
      console.log('🔒 Placed QC task on hold:', taskId)
    }
  }

  const handleResolveHold = async (holdId: string) => {
    const success = await resolveQCHold(holdId, 'release', 'Issue resolved - returning to queue')
    if (success) {
      console.log('🔓 Resolved hold:', holdId)
    }
  }

  const handleAssignToMe = async (taskId: string) => {
    const success = await assignQCTaskToMe(taskId)
    if (success) {
      console.log('👤 Assigned QC task to me:', taskId)
    }
  }

  const handleViewFAI = (partNumber: string) => {
    console.log('📸 Viewing FAI images for:', partNumber)
    // TODO: Open FAI image viewer modal
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-300'
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-300'
      case 'normal': return 'bg-blue-100 text-blue-700 border-blue-300'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-300'
      default: return 'bg-gray-100 text-gray-700 border-gray-300'
    }
  }

  const getComplexityIcon = (complexity: string) => {
    switch (complexity) {
      case 'simple': return <Zap className="h-4 w-4 text-green-600" />
      case 'medium': return <Target className="h-4 w-4 text-yellow-600" />
      case 'complex': return <BarChart3 className="h-4 w-4 text-red-600" />
      default: return <BarChart3 className="h-4 w-4" />
    }
  }

  const getQCTypeColor = (qcType: string) => {
    switch (qcType) {
      case 'visual_only': return 'bg-purple-100 text-purple-700'
      case 'functional_only': return 'bg-blue-100 text-blue-700'
      case 'visual_functional': return 'bg-green-100 text-green-700'
      case 'full_inspection': return 'bg-orange-100 text-orange-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const formatTimeRemaining = (dueTime: Date) => {
    const now = new Date()
    const diff = dueTime.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const getTaskDuration = (startedAt: Date) => {
    const now = new Date()
    const diff = now.getTime() - startedAt.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes} min`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading size="lg" message="Loading QC dashboard..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quality Control Dashboard</h1>
          <p className="text-gray-600">Manage screening and quality control operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <User className="h-3 w-3 mr-1" />
            {user?.email?.split('@')[0] || 'Unknown'}
          </Badge>
          <Button variant="outline" size="sm" onClick={refresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Current QC Tasks */}
      {currentTasks.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <PlayCircle className="h-5 w-5 mr-2 text-blue-600" />
            My Current QC Tasks ({currentTasks.length})
          </h2>
          <div className="grid grid-cols-1 gap-3">
            {currentTasks.map((task) => (
              <div key={task.id} className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Badge className="bg-blue-600 text-white">
                        {task.orderUid}
                      </Badge>
                      <span className="font-medium">{task.partNumber}</span>
                      <Badge variant="outline" className="bg-orange-100 text-orange-700">
                        {task.quantity} units
                      </Badge>
                      <Badge variant="outline" className="bg-purple-100 text-purple-700 text-xs">
                        {task.currentStage.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-700 mt-1">{task.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Timer className="h-3 w-3" />
                        <span>In progress {getTaskDuration(task.startedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>Est. {formatTimeRemaining(task.estimatedCompletion)} remaining</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-3 w-3" />
                        <span>CTs: {task.ctNumbers.slice(0, 2).join(', ')}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    {/* Progress Bar */}
                    <div className="w-24">
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>{task.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePauseQC(task.id)}
                      >
                        <PauseCircle className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleHoldQC(task.id)}
                        className="border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                      >
                        <Pause className="h-4 w-4 mr-1" />
                        Hold
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleFailQC(task.id)}
                        className="border-red-300 text-red-700 hover:bg-red-50"
                      >
                        <ThumbsDown className="h-4 w-4 mr-1" />
                        Fail
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handlePassQC(task.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        Pass
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Hold Items Alert */}
      {holdItems.length > 0 && (
        <div className="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <h3 className="font-semibold text-yellow-900">Items on Hold ({holdItems.length})</h3>
            </div>
            <Button variant="outline" size="sm" className="border-yellow-300 text-yellow-700">
              <Eye className="h-4 w-4 mr-2" />
              View All Holds
            </Button>
          </div>
          <div className="mt-3 space-y-2">
            {holdItems.map((hold) => (
              <div key={hold.id} className="flex items-center justify-between bg-white p-3 rounded border">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-700">
                      {hold.orderUid}
                    </Badge>
                    <span className="text-sm font-medium">{hold.partNumber}</span>
                    <span className="text-xs text-gray-600">({hold.quantity} units)</span>
                  </div>
                  <p className="text-xs text-gray-700 mt-1">Hold reason: {hold.holdReason}</p>
                  <p className="text-xs text-gray-500">On hold for {getTaskDuration(hold.holdDate)} by {hold.holdBy}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleResolveHold(hold.id)}
                  className="border-green-300 text-green-700 hover:bg-green-50"
                >
                  <CheckCircle2 className="h-4 w-4 mr-1" />
                  Resolve
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* QC Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">QC Queue</p>
              <p className="text-2xl font-bold">{stats.qcQueue}</p>
            </div>
            <BarChart3 className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Unassigned</p>
              <p className="text-2xl font-bold text-orange-600">
                {stats.unassigned}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-orange-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">On Hold</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.onHold}</p>
            </div>
            <Pause className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Re-work</p>
              <p className="text-2xl font-bold text-red-600">
                {stats.rework}
              </p>
            </div>
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">My Tasks</p>
              <p className="text-2xl font-bold text-green-600">
                {stats.myTasks}
              </p>
            </div>
            <User className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by UID, part number, or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="normal">Normal</option>
              <option value="low">Low</option>
            </select>
            <select
              value={qcTypeFilter}
              onChange={(e) => setQCTypeFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All QC Types</option>
              <option value="visual_only">Visual Only</option>
              <option value="functional_only">Functional Only</option>
              <option value="visual_functional">Visual + Functional</option>
              <option value="full_inspection">Full Inspection</option>
            </select>
            <select
              value={assignmentFilter}
              onChange={(e) => setAssignmentFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Tasks</option>
              <option value="unassigned">Unassigned</option>
              <option value="assigned">Assigned</option>
              <option value="mine">My Tasks</option>
            </select>
          </div>
        </div>
      </div>

      {/* QC Queue */}
      <div className="space-y-3">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
          QC Queue ({filteredQueue.length})
        </h2>
        
        {filteredQueue.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>No QC tasks found matching your filters.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {filteredQueue.map((task) => (
              <div
                key={task.id}
                className={cn(
                  "bg-white border rounded-lg p-4 hover:shadow-md transition-shadow",
                  task.priority === 'urgent' && "border-red-300 bg-red-50",
                  task.hasFailedPreviously && "border-l-4 border-l-orange-400"
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <Badge className={cn("font-medium", getPriorityColor(task.priority))}>
                        {task.priority.toUpperCase()}
                      </Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {task.order.uid}
                      </Badge>
                      <span className="font-medium">{task.order.customer_part_number}</span>
                      {getComplexityIcon(task.complexity)}
                      <Badge variant="outline" className={cn("text-xs", getQCTypeColor(task.qcType))}>
                        {task.qcType.replace('_', ' ').toUpperCase()}
                      </Badge>
                      {task.hasFailedPreviously && (
                        <Badge variant="outline" className="bg-orange-100 text-orange-700 text-xs">
                          RE-WORK
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-700 mt-1">{task.order.bpi_description}</p>
                    
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-600">
                      <div className="flex items-center space-x-1">
                        <BarChart3 className="h-3 w-3" />
                        <span>{task.quantity} units</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>Est. {task.estimatedTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Timer className="h-3 w-3" />
                        <span>Due in {formatTimeRemaining(task.dueTime)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-3 w-3" />
                        <span>CTs: {task.ctNumbers.slice(0, 2).join(', ')}</span>
                      </div>
                      {task.assignedTo && (
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>Assigned to {task.assignedTo}</span>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-xs text-gray-600 mt-1">
                      Requirements: {task.customerRequirements}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewFAI(task.order.customer_part_number || '')}
                    >
                      <Image className="h-4 w-4 mr-1" />
                      FAI
                    </Button>
                    
                    {!task.assignedTo ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAssignToMe(task.id)}
                      >
                        <User className="h-4 w-4 mr-1" />
                        Assign to Me
                      </Button>
                    ) : task.assignedTo === user?.email?.split('@')[0] ? (
                      <Button
                        size="sm"
                        onClick={() => handleStartQC(task.id)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <PlayCircle className="h-4 w-4 mr-1" />
                        Start QC
                      </Button>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        Assigned to {task.assignedTo}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}