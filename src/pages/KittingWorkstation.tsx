import { useState } from 'react'
import { Package, Activity, Clock, AlertTriangle, CheckCircle2, Users, RefreshCw } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import ScanningWorkInterface from '@/components/scanning/ScanningWorkInterface'
import { useKittingQueue } from '@/hooks/useKittingQueue'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface WorkSession {
  startTime: Date
  itemsProcessed: number
  itemsOnHold: number
  lastActivity: Date
}

export default function KittingWorkstation() {
  const [workSession, setWorkSession] = useState<WorkSession>({
    startTime: new Date(),
    itemsProcessed: 0,
    itemsOnHold: 0,
    lastActivity: new Date()
  })
  const [showStats, setShowStats] = useState(false)
  const isActive = true
  
  const { user } = useAuth()
  const { 
    kittingQueue, 
    isLoading, 
    totalItems, 
    assignedToUser, 
    onHoldItems,
    refresh 
  } = useKittingQueue()

  // Update session stats when work is completed
  const handleWorkComplete = (ctNumber: string, action: any) => {
    const now = new Date()
    setWorkSession(prev => ({
      ...prev,
      itemsProcessed: action.type === 'complete' ? prev.itemsProcessed + 1 : prev.itemsProcessed,
      itemsOnHold: action.type === 'hold' ? prev.itemsOnHold + 1 : prev.itemsOnHold,
      lastActivity: now
    }))
    
    // Show session progress
    if (action.type === 'complete') {
      toast.success(`Item ${workSession.itemsProcessed + 1} completed!`)
    }
  }

  // Calculate session duration
  const getSessionDuration = (): string => {
    const diffMs = Date.now() - workSession.startTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const hours = Math.floor(diffMins / 60)
    const mins = diffMins % 60
    
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  // Calculate items per hour
  const getItemsPerHour = (): number => {
    const diffMs = Date.now() - workSession.startTime.getTime()
    const diffHours = diffMs / (1000 * 60 * 60)
    return diffHours > 0 ? Math.round(workSession.itemsProcessed / diffHours) : 0
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Station Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <h1 className="text-xl font-semibold text-gray-900">Kitting Workstation</h1>
            <Badge variant="outline">{user?.email?.split('@')[0] || 'Staff'}</Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refresh()}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant={showStats ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowStats(!showStats)}
            >
              <Activity className="w-4 h-4 mr-1" />
              Stats
            </Button>
          </div>
        </div>
      </div>

      {/* Session Stats (Collapsible) */}
      {showStats && (
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <div>
                  <div className="text-xs text-gray-500">Session Time</div>
                  <div className="font-medium">{getSessionDuration()}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <CheckCircle2 className="w-4 h-4 text-green-500" />
                <div>
                  <div className="text-xs text-gray-500">Completed</div>
                  <div className="font-medium">{workSession.itemsProcessed}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-orange-500" />
                <div>
                  <div className="text-xs text-gray-500">On Hold</div>
                  <div className="font-medium">{workSession.itemsOnHold}</div>
                </div>
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center space-x-2">
                <Package className="w-4 h-4 text-purple-500" />
                <div>
                  <div className="text-xs text-gray-500">Rate/Hour</div>
                  <div className="font-medium">{getItemsPerHour()}</div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* Queue Overview */}
      <div className="p-4">
        <Card className="p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="font-medium text-gray-900">Kitting Queue</h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                <span>{totalItems} total items</span>
                <span>•</span>
                <span>{assignedToUser} assigned to you</span>
                <span>•</span>
                <span>{onHoldItems} on hold</span>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {Math.max(0, (totalItems || 0) - ((workSession.itemsProcessed || 0) + (workSession.itemsOnHold || 0)))}
              </div>
              <div className="text-xs text-gray-500">remaining</div>
            </div>
          </div>
          
          {/* Queue Progress Bar */}
          <div className="mt-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{Math.round((((workSession.itemsProcessed || 0) + (workSession.itemsOnHold || 0)) / Math.max((totalItems || 0), 1)) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${Math.min((((workSession.itemsProcessed || 0) + (workSession.itemsOnHold || 0)) / Math.max((totalItems || 0), 1)) * 100, 100)}%`
                }}
              />
            </div>
          </div>
        </Card>
      </div>

      {/* Main Scanning Interface */}
      <ScanningWorkInterface
        workType="kitting"
        onWorkComplete={handleWorkComplete}
        className="pb-6"
      />

      {/* Recent Activity (Bottom Section) */}
      <div className="p-4">
        <Card className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" size="sm" className="justify-start">
              <Users className="w-4 h-4 mr-2" />
              View Team Status
            </Button>
            <Button variant="outline" size="sm" className="justify-start">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Resolve Holds
            </Button>
          </div>
        </Card>
      </div>

      {/* Floating Action Button for Emergency */}
      <div className="fixed bottom-6 right-6">
        <Button
          size="lg"
          className="rounded-full w-14 h-14 shadow-lg"
          onClick={() => toast.info('Emergency support contacted')}
        >
          <AlertTriangle className="w-6 h-6" />
        </Button>
      </div>
    </div>
  )
}