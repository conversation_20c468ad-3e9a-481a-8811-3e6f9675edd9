import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  ListChecks,
  RefreshCw,
  Monitor,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Search,

  Trash2,
  FileText,
  Printer
} from 'lucide-react'
import { toast } from 'sonner'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface PrintJob {
  id: string
  printer_id: string
  printer_name?: string
  status: 'pending' | 'printing' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  metadata: {
    order_uid?: string
    ct_numbers?: string[]
    template_name?: string
    created_by?: string
    part_number?: string
    description?: string
    quantity?: number
    purpose?: string
  }
  error?: string
  retry_count?: number
}

export function PrintQueuePage() {
  const [printQueue, setPrintQueue] = useState<PrintJob[]>([])
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [timeFilter, setTimeFilter] = useState<string>('24h')

  const { isConnected } = useMCPPrinting()

  // Mock data for demonstration - replace with actual API calls
  useEffect(() => {
    // In production, this would fetch from your print queue API
    const mockJobs: PrintJob[] = [
      {
        id: 'job-001',
        printer_id: 'printer-1',
        printer_name: 'SB Zebra Printer 1',
        status: 'completed',
        created_at: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
        completed_at: new Date(Date.now() - 1000 * 60 * 3).toISOString(),
        metadata: {
          order_uid: 'ORD-2024-0123',
          ct_numbers: ['CT12345678901234', 'CT12345678901235'],
          template_name: 'default-ct-label',
          created_by: '<EMAIL>',
          part_number: 'HP-PN-123',
          quantity: 2
        }
      },
      {
        id: 'job-002',
        printer_id: 'printer-2',
        printer_name: 'SB Zebra Printer 2',
        status: 'printing',
        created_at: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
        metadata: {
          order_uid: 'ORD-2024-0124',
          template_name: 'hp-external',
          created_by: '<EMAIL>',
          purpose: 'quick_print',
          quantity: 5
        }
      },
      {
        id: 'job-003',
        printer_id: 'printer-1',
        printer_name: 'SB Zebra Printer 1',
        status: 'failed',
        created_at: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
        metadata: {
          order_uid: 'ORD-2024-0122',
          ct_numbers: ['CT12345678901230'],
          template_name: 'default-ct-label',
          created_by: '<EMAIL>'
        },
        error: 'Printer offline',
        retry_count: 2
      }
    ]
    setPrintQueue(mockJobs)
  }, [])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // In production, refetch from API
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Print queue refreshed')
    } catch (error) {
      toast.error('Failed to refresh print queue')
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleRetryJob = async (jobId: string) => {
    try {
      toast.success(`Retrying job ${jobId.slice(-8)}`)
      // In production, call retry API
    } catch (error) {
      toast.error('Failed to retry job')
    }
  }

  const handleCancelJob = async (jobId: string) => {
    try {
      toast.success(`Cancelled job ${jobId.slice(-8)}`)
      // In production, call cancel API
    } catch (error) {
      toast.error('Failed to cancel job')
    }
  }

  const handleClearCompleted = async () => {
    try {
      const completedCount = printQueue.filter(job => job.status === 'completed').length
      setPrintQueue(prev => prev.filter(job => job.status !== 'completed'))
      toast.success(`Cleared ${completedCount} completed jobs`)
    } catch (error) {
      toast.error('Failed to clear completed jobs')
    }
  }

  const getStatusIcon = (status: PrintJob['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'printing':
        return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const formatJobMetadata = (metadata: PrintJob['metadata']) => {
    const parts = []
    if (metadata.order_uid) parts.push(metadata.order_uid)
    if (metadata.part_number) parts.push(metadata.part_number)
    if (metadata.ct_numbers) parts.push(`${metadata.ct_numbers.length} CTs`)
    if (metadata.purpose === 'quick_print') parts.push('Quick Print')
    return parts.join(' • ')
  }

  const getTimeElapsed = (timestamp: string) => {
    const now = Date.now()
    const then = new Date(timestamp).getTime()
    const diff = now - then

    if (diff < 60000) return 'Just now'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
    return `${Math.floor(diff / 86400000)}d ago`
  }

  // Filter jobs
  const filteredJobs = printQueue.filter(job => {
    // Status filter
    if (statusFilter !== 'all' && job.status !== statusFilter) return false

    // Search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      return (
        job.id.toLowerCase().includes(search) ||
        job.metadata.order_uid?.toLowerCase().includes(search) ||
        job.metadata.part_number?.toLowerCase().includes(search) ||
        job.metadata.ct_numbers?.some(ct => ct.toLowerCase().includes(search))
      )
    }

    // Time filter
    const jobTime = new Date(job.created_at).getTime()
    const now = Date.now()
    switch (timeFilter) {
      case '1h':
        return now - jobTime < 3600000
      case '24h':
        return now - jobTime < 86400000
      case '7d':
        return now - jobTime < 604800000
      default:
        return true
    }
  })

  // Calculate statistics
  const stats = {
    total: filteredJobs.length,
    pending: filteredJobs.filter(j => j.status === 'pending').length,
    printing: filteredJobs.filter(j => j.status === 'printing').length,
    completed: filteredJobs.filter(j => j.status === 'completed').length,
    failed: filteredJobs.filter(j => j.status === 'failed').length
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <ListChecks className="h-6 w-6 text-gray-700" />
            <h1 className="text-2xl font-bold text-gray-900">Print Queue</h1>
          </div>
          <MCPConnectionStatus />
        </div>
        <p className="text-gray-600">Monitor and manage print jobs across all printers</p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Jobs</span>
              <FileText className="h-4 w-4 text-gray-400" />
            </div>
            <p className="text-2xl font-bold">{stats.total}</p>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Pending</span>
              <Clock className="h-4 w-4 text-gray-400" />
            </div>
            <p className="text-2xl font-bold text-gray-600">{stats.pending}</p>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Printing</span>
              <Printer className="h-4 w-4 text-blue-400" />
            </div>
            <p className="text-2xl font-bold text-blue-600">{stats.printing}</p>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Completed</span>
              <CheckCircle className="h-4 w-4 text-green-400" />
            </div>
            <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Failed</span>
              <XCircle className="h-4 w-4 text-red-400" />
            </div>
            <p className="text-2xl font-bold text-red-600">{stats.failed}</p>
          </CardHeader>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by order, part, or CT number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="printing">Printing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last hour</SelectItem>
                  <SelectItem value="24h">Last 24 hours</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearCompleted}
                disabled={stats.completed === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Completed
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Print Jobs List */}
      <Card>
        <CardContent className="p-0">
          {filteredJobs.length === 0 ? (
            <div className="text-center py-12">
              <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">No print jobs found</p>
              <p className="text-gray-400 text-sm mt-1">
                {searchTerm || statusFilter !== 'all' ? 'Try adjusting your filters' : 'Jobs will appear here when printing'}
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {filteredJobs.map((job) => (
                <div key={job.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getStatusIcon(job.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">Job #{job.id.slice(-8)}</span>
                          <Badge variant="outline" className="text-xs">
                            {job.printer_name || 'Unknown Printer'}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">
                          {formatJobMetadata(job.metadata)}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Created: {getTimeElapsed(job.created_at)}</span>
                          {job.completed_at && (
                            <span>Completed: {getTimeElapsed(job.completed_at)}</span>
                          )}
                          {job.metadata.created_by && (
                            <span>By: {job.metadata.created_by}</span>
                          )}
                        </div>
                        {job.error && (
                          <div className="mt-2 flex items-center gap-2 text-xs text-red-600 bg-red-50 p-2 rounded">
                            <AlertTriangle className="h-3 w-3" />
                            <span>{job.error}</span>
                            {job.retry_count && job.retry_count > 0 && (
                              <span className="text-red-400">• {job.retry_count} retries</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          job.status === 'completed' ? 'default' :
                          job.status === 'failed' ? 'destructive' :
                          job.status === 'printing' ? 'secondary' :
                          'outline'
                        }
                        className="text-xs"
                      >
                        {job.status}
                      </Badge>
                      {job.status === 'failed' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRetryJob(job.id)}
                        >
                          Retry
                        </Button>
                      )}
                      {(job.status === 'pending' || job.status === 'printing') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCancelJob(job.id)}
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {!isConnected && (
        <Card className="mt-6 border-amber-200 bg-amber-50">
          <CardHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              <CardTitle className="text-base text-amber-900">MCP Server Offline</CardTitle>
            </div>
            <CardDescription className="text-amber-700">
              The MCP printing server is not connected. Print jobs cannot be processed until the connection is restored.
            </CardDescription>
          </CardHeader>
        </Card>
      )}
    </div>
  )
}