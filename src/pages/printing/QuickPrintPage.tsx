import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Printer, Loader2, Eye, AlertTriangle, Package } from 'lucide-react'
import { toast } from 'sonner'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { QUICK_PRINT_TEMPLATES } from '@/types/labelDesigner'
import { cn } from '@/lib/utils'
import { useMCPPrinting } from '@/hooks/useMCPPrinting'
import { MCPConnectionStatus } from '@/components/ui/mcp-connection-status'
import { usePrinterConfig } from '@/hooks/usePrinterConfig'
import { generateQuickPrintZPL, downloadZPLFile, generateLabelaryPreviewUrl, LABEL_TEMPLATES } from '@/utils/labelGeneration'

export function QuickPrintPage() {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [quantity, setQuantity] = useState('1')
  const [selectedPrinter, setSelectedPrinter] = useState('')
  const [isPrinting, setIsPrinting] = useState(false)
  const [isGeneratingZPL, setIsGeneratingZPL] = useState(false)

  // MCP Printing hooks
  const { 
    useZebraPrinters, 
    printZebraLabel, 
    isConnected: isMCPConnected 
  } = useMCPPrinting()
  
  const { getActivePrinters, getDefaultPrinter } = usePrinterConfig()

  // Available printers
  const { data: mcpZebraPrinters = [] } = useZebraPrinters()
  const configPrinters = getActivePrinters()
  const defaultPrinter = getDefaultPrinter()
  
  const availablePrinters = mcpZebraPrinters.length > 0 ? mcpZebraPrinters : configPrinters
  const defaultPrinterId = mcpZebraPrinters.length > 0 
    ? mcpZebraPrinters[0]?.id 
    : defaultPrinter?.id

  useEffect(() => {
    if (defaultPrinterId && !selectedPrinter) {
      setSelectedPrinter(defaultPrinterId)
    }
  }, [defaultPrinterId, selectedPrinter])

  const handlePrint = async () => {
    const printQty = parseInt(quantity) || 1
    
    if (!selectedTemplate || !selectedPrinter || printQty < 1) {
      toast.error('Please select a template, printer, and valid quantity')
      return
    }

    setIsPrinting(true)

    try {
      const templateData = LABEL_TEMPLATES[selectedTemplate]
      if (!templateData) {
        throw new Error('Template not found')
      }

      const zpl = generateQuickPrintZPL(templateData, printQty)
      
      if (isMCPConnected && selectedPrinter) {
        const result = await printZebraLabel(selectedPrinter, zpl, {
          template: selectedTemplate,
          quantity: printQty,
          purpose: 'quick_print',
          created_by: 'Quick Print Page'
        })

        if (result.success) {
          toast.success(`Successfully printed ${printQty} label${printQty > 1 ? 's' : ''}`)
        } else {
          downloadZPLFile(zpl, `QuickPrint_${selectedTemplate}_${Date.now()}.zpl`)
          toast.info('Print failed - ZPL file downloaded as backup')
        }
      } else {
        downloadZPLFile(zpl, `QuickPrint_${selectedTemplate}_${Date.now()}.zpl`)
        toast.info('MCP not connected - ZPL file downloaded')
      }
    } catch (error) {
      console.error('Print error:', error)
      toast.error('Failed to print labels')
    } finally {
      setIsPrinting(false)
    }
  }

  const handlePreview = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template to preview')
      return
    }

    setIsGeneratingZPL(true)

    try {
      const templateData = LABEL_TEMPLATES[selectedTemplate]
      if (!templateData) {
        throw new Error('Template not found')
      }

      const zpl = generateQuickPrintZPL(templateData, 1)
      const previewUrl = generateLabelaryPreviewUrl(zpl, templateData)
      window.open(previewUrl, '_blank')
    } catch (error) {
      console.error('Preview error:', error)
      toast.error('Failed to generate preview')
    } finally {
      setIsGeneratingZPL(false)
    }
  }

  const selectedTemplateData = selectedTemplate ? LABEL_TEMPLATES[selectedTemplate] : null

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Printer className="h-6 w-6 text-gray-700" />
          <h1 className="text-2xl font-bold text-gray-900">Quick Print</h1>
        </div>
        <p className="text-gray-600">Print labels quickly without creating orders</p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Quick Label Printing</CardTitle>
              <CardDescription>
                Select a template and quantity to print labels immediately
              </CardDescription>
            </div>
            <MCPConnectionStatus />
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Select Template</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {QUICK_PRINT_TEMPLATES.map((templateId) => {
                const template = LABEL_TEMPLATES[templateId]
                if (!template) return null
                
                return (
                  <Card
                    key={templateId}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      selectedTemplate === templateId && "ring-2 ring-blue-500"
                    )}
                    onClick={() => setSelectedTemplate(templateId)}
                  >
                    <CardHeader className="p-4">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-gray-500" />
                        <div>
                          <h3 className="font-medium text-sm">{template.name}</h3>
                          <p className="text-xs text-gray-500">
                            {template.width}" × {template.height}"
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Printer Selection */}
          <div className="space-y-2">
            <Label htmlFor="printer">Printer</Label>
            <Select value={selectedPrinter} onValueChange={setSelectedPrinter}>
              <SelectTrigger id="printer">
                <SelectValue placeholder="Select a printer" />
              </SelectTrigger>
              <SelectContent>
                {availablePrinters.map((printer: any) => (
                  <SelectItem key={printer.id} value={printer.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{printer.name}</span>
                      {mcpZebraPrinters.length > 0 && (
                        <span className="ml-2">
                          {printer.health?.status === 'online' ? '✅' : '❌'}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!isMCPConnected && (
              <div className="flex items-center gap-2 p-2 bg-amber-50 text-amber-700 rounded text-sm">
                <AlertTriangle className="h-4 w-4" />
                <span>MCP not connected - Will download ZPL file instead</span>
              </div>
            )}
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max="100"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              placeholder="Number of labels to print"
            />
          </div>

          {/* Selected Template Info */}
          {selectedTemplateData && (
            <Card className="bg-gray-50">
              <CardHeader className="p-4">
                <h3 className="font-medium text-sm">Template Details</h3>
                <div className="text-xs text-gray-600 space-y-1 mt-2">
                  <p>Size: {selectedTemplateData.width}" × {selectedTemplateData.height}"</p>
                  <p>DPI: {selectedTemplateData.dpi}</p>
                  <p>Type: Quick Print Label</p>
                </div>
              </CardHeader>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              onClick={handlePreview}
              variant="outline"
              disabled={!selectedTemplate || isGeneratingZPL}
              className="flex-1"
            >
              {isGeneratingZPL ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </>
              )}
            </Button>
            <Button
              onClick={handlePrint}
              disabled={!selectedTemplate || !selectedPrinter || isPrinting || parseInt(quantity) < 1}
              className="flex-1"
            >
              {isPrinting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Printing...
                </>
              ) : (
                <>
                  <Printer className="mr-2 h-4 w-4" />
                  Print {quantity} Label{parseInt(quantity) !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}