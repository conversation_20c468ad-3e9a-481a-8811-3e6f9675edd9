import { TemplateManager } from '@/components/admin/TemplateManager'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { FileSpreadsheet } from 'lucide-react'

export function TemplateManagerPage() {
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <FileSpreadsheet className="h-6 w-6 text-gray-700" />
          <h1 className="text-2xl font-bold text-gray-900">Template Manager</h1>
        </div>
        <p className="text-gray-600">Create, edit, and manage label templates for printing</p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Label Templates</CardTitle>
          <CardDescription>
            Manage your label templates here. Templates can be used for CT labels, shipping labels, and other printing needs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TemplateManager />
        </CardContent>
      </Card>
    </div>
  )
}