import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { useEffect, Suspense, lazy } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { QueryProvider } from '@/providers/QueryProvider'
import { LoginForm } from '@/components/auth/LoginForm'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Dashboard } from '@/pages/Dashboard'
import { Orders } from '@/pages/Orders'
import { Settings } from '@/pages/Settings'
import { Loading } from '@/components/ui/loading'
import { Toaster } from '@/components/ui/sonner'
import { ErrorBoundary, AsyncErrorBoundary } from '@/utils/errorBoundary'
import { cleanupOrdersSubscriptions } from '@/hooks/useOrders'
import { performanceMonitor } from '@/utils/monitoring'
import { initializeStatePersistence } from '@/utils/statePersistence'
import './App.css'

// Initialize performance monitoring and state persistence
performanceMonitor.recordMetric('app-initialization', performance.now())
initializeStatePersistence()

// Lazy load heavy components for better performance
const LazyLabelDesigner = lazy(() => import('@/pages/LabelDesignerPage').then(module => ({ default: module.LabelDesignerPage })))
const LazyKittingDashboard = lazy(() => import('@/pages/KittingDashboard').then(module => ({ default: module.KittingDashboard })))
const LazyQCDashboard = lazy(() => import('@/pages/QCDashboard').then(module => ({ default: module.QCDashboard })))
const LazyInvoicingDashboard = lazy(() => import('@/pages/InvoicingDashboard').then(module => ({ default: module.InvoicingDashboard })))
const LazyKittingWorkstation = lazy(() => import('@/pages/KittingWorkstation'))
const LazyQCWorkstation = lazy(() => import('@/pages/QCWorkstation'))
const LazyTemplateManager = lazy(() => import('@/pages/printing/TemplateManagerPage').then(module => ({ default: module.TemplateManagerPage })))
const LazyQuickPrint = lazy(() => import('@/pages/printing/QuickPrintPage').then(module => ({ default: module.QuickPrintPage })))
const LazyPrintQueue = lazy(() => import('@/pages/printing/PrintQueuePage').then(module => ({ default: module.PrintQueuePage })))

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <Loading message="Checking authentication..." />
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

function AppContent() {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <Loading message="Initializing Mini-ERP..." />
  }

  return (
    <Routes>
      <Route path="/login" element={
        isAuthenticated ? <Navigate to="/" replace /> : <LoginForm />
      } />
      
      <Route path="/" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        </ProtectedRoute>
      } />
      
      <Route path="/orders" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Orders />
          </DashboardLayout>
        </ProtectedRoute>
      } />
      
      <Route path="/settings" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        </ProtectedRoute>
      } />
      
      <Route path="/preferences" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/label-designer" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Label Designer..." />}>
              <LazyLabelDesigner />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/printing/templates" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Template Manager..." />}>
              <LazyTemplateManager />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/printing/quick" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Quick Print..." />}>
              <LazyQuickPrint />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/printing/queue" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Print Queue..." />}>
              <LazyPrintQueue />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/settings/mcp" element={
        <Navigate to="/settings?tab=mcp" replace />
      } />

      <Route path="/kitting" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Kitting Dashboard..." />}>
              <LazyKittingDashboard />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/kitting/station" element={
        <ProtectedRoute>
          <Suspense fallback={<Loading message="Loading Kitting Workstation..." />}>
            <LazyKittingWorkstation />
          </Suspense>
        </ProtectedRoute>
      } />

      <Route path="/qc" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading QC Dashboard..." />}>
              <LazyQCDashboard />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/qc/station" element={
        <ProtectedRoute>
          <Suspense fallback={<Loading message="Loading QC Workstation..." />}>
            <LazyQCWorkstation />
          </Suspense>
        </ProtectedRoute>
      } />

      <Route path="/invoicing" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Suspense fallback={<Loading message="Loading Invoicing Dashboard..." />}>
              <LazyInvoicingDashboard />
            </Suspense>
          </DashboardLayout>
        </ProtectedRoute>
      } />

      
      {/* Add more routes as we build more pages */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

function App() {
  // Performance monitoring and cleanup
  useEffect(() => {
    performanceMonitor.recordMetric('app-mount', performance.now())
    
    return () => {
      cleanupOrdersSubscriptions()
      performanceMonitor.recordMetric('app-unmount', performance.now())
    }
  }, [])

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        performanceMonitor.recordMetric('error-boundary-triggered', 1, {
          error: error.message,
          componentStack: errorInfo.componentStack
        })
      }}
    >
      <AsyncErrorBoundary>
        <BrowserRouter>
          <QueryProvider>
            <AppContent />
            <Toaster />
          </QueryProvider>
        </BrowserRouter>
      </AsyncErrorBoundary>
    </ErrorBoundary>
  )
}

export default App