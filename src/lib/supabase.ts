import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Enhanced configuration for better session management and WebSocket resilience
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: {
      getItem: (key: string) => {
        try {
          return localStorage.getItem(key)
        } catch {
          return null
        }
      },
      setItem: (key: string, value: string) => {
        try {
          localStorage.setItem(key, value)
        } catch {
          // Silently fail if localStorage is not available
        }
      },
      removeItem: (key: string) => {
        try {
          localStorage.removeItem(key)
        } catch {
          // Silently fail if localStorage is not available
        }
      }
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    },
    heartbeatIntervalMs: 30000,
    reconnectAfterMs: function (tries: number) {
      return Math.min(1000 * Math.pow(2, tries), 30000)
    },
    encode: function (payload: any, callback: (data: string) => void) {
      callback(JSON.stringify(payload))
    },
    decode: function (payload: string, callback: (data: any) => void) {
      callback(JSON.parse(payload))
    },
    timeout: 20000,
    transport: typeof WebSocket !== 'undefined' ? WebSocket : undefined,
    logger: function (kind: string, msg: string, data?: any) {
      if (kind === 'error') {
        console.warn(`Supabase Realtime ${kind}:`, msg, data)
      }
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'mini-erp-client'
    }
  }
})