/**
 * Performance monitoring and analytics utilities
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

interface UserInteraction {
  type: 'click' | 'scroll' | 'input' | 'navigation'
  element?: string
  timestamp: number
  duration?: number
  metadata?: Record<string, any>
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private interactions: UserInteraction[] = []
  private observers: PerformanceObserver[] = []
  private isEnabled: boolean = process.env.NODE_ENV === 'production'

  constructor() {
    if (this.isEnabled && typeof window !== 'undefined') {
      this.initializeObservers()
      this.trackCoreWebVitals()
    }
  }

  private initializeObservers() {
    // Long Task Observer
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('long-task', entry.duration, {
              startTime: entry.startTime,
              name: entry.name
            })
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      } catch (error) {
        console.warn('Long Task Observer not supported')
      }

      // Navigation Observer
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const navEntry = entry as PerformanceNavigationTiming
            this.recordMetric('navigation-complete', navEntry.loadEventEnd - navEntry.navigationStart, {
              domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
              domInteractive: navEntry.domInteractive - navEntry.navigationStart,
              type: navEntry.type
            })
          }
        })
        navigationObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navigationObserver)
      } catch (error) {
        console.warn('Navigation Observer not supported')
      }

      // Resource Observer
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const resource = entry as PerformanceResourceTiming
            if (resource.duration > 1000) { // Only track slow resources
              this.recordMetric('slow-resource', resource.duration, {
                name: resource.name,
                size: resource.transferSize,
                type: resource.initiatorType
              })
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (error) {
        console.warn('Resource Observer not supported')
      }
    }
  }

  private trackCoreWebVitals() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.recordMetric('lcp', lastEntry.startTime, {
            element: (lastEntry as any).element?.tagName
          })
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (error) {
        console.warn('LCP Observer not supported')
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('fid', (entry as any).processingStart - entry.startTime, {
              eventType: (entry as any).name
            })
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (error) {
        console.warn('FID Observer not supported')
      }

      // Cumulative Layout Shift (CLS)
      let clsValue = 0
      try {
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.recordMetric('cls', clsValue)
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (error) {
        console.warn('CLS Observer not supported')
      }
    }
  }

  public recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    }

    this.metrics.push(metric)

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }

    // Log significant metrics in development
    if (process.env.NODE_ENV === 'development' && value > 100) {
      console.log(`Performance Metric: ${name} = ${value.toFixed(2)}ms`, metadata)
    }
  }

  public recordInteraction(type: UserInteraction['type'], element?: string, metadata?: Record<string, any>) {
    if (!this.isEnabled) return

    const interaction: UserInteraction = {
      type,
      element,
      timestamp: Date.now(),
      metadata
    }

    this.interactions.push(interaction)

    // Keep only last 500 interactions
    if (this.interactions.length > 500) {
      this.interactions = this.interactions.slice(-500)
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  public getInteractions(): UserInteraction[] {
    return [...this.interactions]
  }

  public getSummary() {
    const now = Date.now()
    const fiveMinutesAgo = now - (5 * 60 * 1000)
    
    const recentMetrics = this.metrics.filter(m => m.timestamp > fiveMinutesAgo)
    const recentInteractions = this.interactions.filter(i => i.timestamp > fiveMinutesAgo)

    return {
      timeRange: '5 minutes',
      metricsCount: recentMetrics.length,
      interactionsCount: recentInteractions.length,
      averageMetrics: this.calculateAverages(recentMetrics),
      topSlowOperations: this.getTopSlowOperations(recentMetrics),
      interactionBreakdown: this.getInteractionBreakdown(recentInteractions)
    }
  }

  private calculateAverages(metrics: PerformanceMetric[]) {
    const grouped = metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = []
      }
      acc[metric.name].push(metric.value)
      return acc
    }, {} as Record<string, number[]>)

    return Object.entries(grouped).map(([name, values]) => ({
      name,
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      count: values.length,
      max: Math.max(...values),
      min: Math.min(...values)
    }))
  }

  private getTopSlowOperations(metrics: PerformanceMetric[]) {
    return metrics
      .filter(m => m.value > 100) // Only operations > 100ms
      .sort((a, b) => b.value - a.value)
      .slice(0, 10)
      .map(m => ({
        name: m.name,
        value: m.value,
        timestamp: new Date(m.timestamp).toISOString()
      }))
  }

  private getInteractionBreakdown(interactions: UserInteraction[]) {
    const breakdown = interactions.reduce((acc, interaction) => {
      acc[interaction.type] = (acc[interaction.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(breakdown).map(([type, count]) => ({ type, count }))
  }

  public exportData() {
    return {
      metrics: this.getMetrics(),
      interactions: this.getInteractions(),
      summary: this.getSummary(),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  public clearData() {
    this.metrics = []
    this.interactions = []
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.clearData()
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor()

// React hook for performance monitoring
export function usePerformanceTracking(componentName: string) {
  const renderStartTime = React.useRef<number>()
  const renderCount = React.useRef<number>(0)

  React.useEffect(() => {
    renderStartTime.current = performance.now()
    renderCount.current++
  })

  React.useEffect(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current
      performanceMonitor.recordMetric(`component-render-${componentName}`, renderTime, {
        renderCount: renderCount.current
      })
    }
  })

  const trackUserAction = React.useCallback((action: string, metadata?: Record<string, any>) => {
    performanceMonitor.recordInteraction('click', `${componentName}-${action}`, metadata)
  }, [componentName])

  return { trackUserAction }
}

// Hook for tracking API calls
export function useAPIPerformance() {
  const trackAPICall = React.useCallback((
    endpoint: string, 
    method: string, 
    duration: number, 
    success: boolean,
    metadata?: Record<string, any>
  ) => {
    performanceMonitor.recordMetric(`api-${method.toLowerCase()}-${endpoint}`, duration, {
      success,
      endpoint,
      method,
      ...metadata
    })
  }, [])

  return { trackAPICall }
}

// Global error tracking
window.addEventListener('error', (event) => {
  performanceMonitor.recordMetric('error', 1, {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
})

window.addEventListener('unhandledrejection', (event) => {
  performanceMonitor.recordMetric('unhandled-promise-rejection', 1, {
    reason: event.reason?.toString()
  })
})

// Export for React
import React from 'react'