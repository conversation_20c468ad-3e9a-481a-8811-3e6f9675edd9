/**
 * Mobile optimization utilities for Mini-ERP system
 */

export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop'
  orientation: 'portrait' | 'landscape'
  screenSize: {
    width: number
    height: number
  }
  capabilities: {
    touch: boolean
    camera: boolean
    geolocation: boolean
    offline: boolean
  }
}

export interface MobileOptimizationConfig {
  enableTouch: boolean
  enableSwipeGestures: boolean
  enablePullToRefresh: boolean
  optimizeImages: boolean
  enableOfflineMode: boolean
  enableBarcodeScanning: boolean
}

class MobileOptimizer {
  private config: MobileOptimizationConfig
  private deviceInfo: DeviceInfo

  constructor(config: Partial<MobileOptimizationConfig> = {}) {
    this.config = {
      enableTouch: true,
      enableSwipeGestures: true,
      enablePullToRefresh: true,
      optimizeImages: true,
      enableOfflineMode: true,
      enableBarcodeScanning: true,
      ...config
    }

    this.deviceInfo = this.detectDevice()
    this.initializeOptimizations()
  }

  private detectDevice(): DeviceInfo {
    const width = window.innerWidth
    const height = window.innerHeight

    let type: DeviceInfo['type'] = 'desktop'
    if (width <= 768) {
      type = 'mobile'
    } else if (width <= 1024) {
      type = 'tablet'
    }

    return {
      type,
      orientation: width > height ? 'landscape' : 'portrait',
      screenSize: { width, height },
      capabilities: {
        touch: 'ontouchstart' in window,
        camera: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        geolocation: !!navigator.geolocation,
        offline: 'serviceWorker' in navigator
      }
    }
  }

  private initializeOptimizations(): void {
    // Initialize touch optimizations
    if (this.config.enableTouch && this.deviceInfo.capabilities.touch) {
      this.initializeTouchOptimizations()
    }

    // Initialize gesture support
    if (this.config.enableSwipeGestures) {
      this.initializeGestureSupport()
    }

    // Initialize pull to refresh
    if (this.config.enablePullToRefresh) {
      this.initializePullToRefresh()
    }

    // Initialize image optimization
    if (this.config.optimizeImages) {
      this.initializeImageOptimization()
    }

    // Initialize viewport optimizations
    this.initializeViewportOptimizations()
  }

  private initializeTouchOptimizations(): void {
    // Add touch-friendly CSS classes
    document.body.classList.add('touch-enabled')

    // Improve button tap targets
    const style = document.createElement('style')
    style.textContent = `
      .touch-enabled button,
      .touch-enabled .clickable {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 12px !important;
      }
      
      .touch-enabled .touch-large {
        font-size: 1.1em !important;
        line-height: 1.4 !important;
      }
      
      .touch-enabled .touch-spacing {
        margin: 8px 4px !important;
      }
      
      .touch-enabled input,
      .touch-enabled select,
      .touch-enabled textarea {
        min-height: 44px !important;
        font-size: 16px !important; /* Prevents zoom on iOS */
      }
    `
    document.head.appendChild(style)
  }

  private initializeGestureSupport(): void {
    let startX = 0
    let startY = 0
    let startTime = 0

    document.addEventListener('touchstart', (e) => {
      const touch = e.touches[0]
      startX = touch.clientX
      startY = touch.clientY
      startTime = Date.now()
    }, { passive: true })

    document.addEventListener('touchend', (e) => {
      if (e.touches.length > 0) return

      const touch = e.changedTouches[0]
      const endX = touch.clientX
      const endY = touch.clientY
      const endTime = Date.now()

      const deltaX = endX - startX
      const deltaY = endY - startY
      const deltaTime = endTime - startTime

      // Detect swipe gestures
      if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 100 && deltaTime < 300) {
        const direction = deltaX > 0 ? 'right' : 'left'
        this.handleSwipeGesture(direction, e.target as Element)
      }
    }, { passive: true })
  }

  private handleSwipeGesture(direction: 'left' | 'right', target: Element): void {
    // Dispatch custom swipe events
    const swipeEvent = new CustomEvent('mobile-swipe', {
      detail: { direction, target }
    })
    document.dispatchEvent(swipeEvent)

    // Handle specific swipe actions
    const card = target.closest('.order-card, .card')
    if (card) {
      if (direction === 'right') {
        // Quick actions menu
        this.showQuickActions(card)
      } else if (direction === 'left') {
        // Hide quick actions or navigate
        this.hideQuickActions(card)
      }
    }
  }

  private showQuickActions(element: Element): void {
    element.classList.add('show-quick-actions')
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      this.hideQuickActions(element)
    }, 3000)
  }

  private hideQuickActions(element: Element): void {
    element.classList.remove('show-quick-actions')
  }

  private initializePullToRefresh(): void {
    let startY = 0
    let pullDistance = 0
    let isPulling = false
    const threshold = 100

    // Create pull to refresh indicator
    const indicator = document.createElement('div')
    indicator.className = 'pull-to-refresh-indicator'
    indicator.innerHTML = '↓ Pull to refresh'
    indicator.style.cssText = `
      position: fixed;
      top: -60px;
      left: 50%;
      transform: translateX(-50%);
      background: #007bff;
      color: white;
      padding: 15px 30px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 500;
      z-index: 1000;
      transition: top 0.3s ease;
    `
    document.body.appendChild(indicator)

    document.addEventListener('touchstart', (e) => {
      if (window.scrollY === 0) {
        startY = e.touches[0].clientY
        isPulling = true
      }
    }, { passive: true })

    document.addEventListener('touchmove', (e) => {
      if (!isPulling || window.scrollY > 0) return

      const currentY = e.touches[0].clientY
      pullDistance = Math.max(0, currentY - startY)

      if (pullDistance > 20) {
        e.preventDefault()
        const progress = Math.min(pullDistance / threshold, 1)
        indicator.style.top = `${-60 + (progress * 80)}px`
        
        if (pullDistance > threshold) {
          indicator.innerHTML = '↑ Release to refresh'
          indicator.style.background = '#28a745'
        } else {
          indicator.innerHTML = '↓ Pull to refresh'
          indicator.style.background = '#007bff'
        }
      }
    })

    document.addEventListener('touchend', () => {
      if (isPulling && pullDistance > threshold) {
        this.triggerRefresh()
      }
      
      isPulling = false
      pullDistance = 0
      indicator.style.top = '-60px'
      indicator.innerHTML = '↓ Pull to refresh'
      indicator.style.background = '#007bff'
    }, { passive: true })
  }

  private triggerRefresh(): void {
    const refreshEvent = new CustomEvent('mobile-refresh')
    document.dispatchEvent(refreshEvent)
  }

  private initializeImageOptimization(): void {
    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]')
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = img.dataset.src || ''
            img.removeAttribute('data-src')
            imageObserver.unobserve(img)
          }
        })
      })

      images.forEach(img => imageObserver.observe(img))
    }

    // WebP support detection
    this.detectWebPSupport().then(supportsWebP => {
      if (supportsWebP) {
        document.body.classList.add('webp-supported')
      }
    })
  }

  private async detectWebPSupport(): Promise<boolean> {
    return new Promise((resolve) => {
      const webp = new Image()
      webp.onload = webp.onerror = () => {
        resolve(webp.height === 2)
      }
      webp.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
    })
  }

  private initializeViewportOptimizations(): void {
    // Prevent zoom on input focus (iOS)
    const viewport = document.querySelector('meta[name=viewport]') as HTMLMetaElement
    if (viewport) {
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
    }

    // Handle orientation changes
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.deviceInfo = this.detectDevice()
        this.handleOrientationChange()
      }, 500)
    })

    // Handle resize for responsive adjustments
    window.addEventListener('resize', this.debounce(() => {
      this.deviceInfo = this.detectDevice()
      this.handleResize()
    }, 250))
  }

  private handleOrientationChange(): void {
    document.body.classList.remove('portrait', 'landscape')
    document.body.classList.add(this.deviceInfo.orientation)

    // Dispatch orientation change event
    const orientationEvent = new CustomEvent('mobile-orientation-change', {
      detail: { orientation: this.deviceInfo.orientation }
    })
    document.dispatchEvent(orientationEvent)
  }

  private handleResize(): void {
    document.body.classList.remove('mobile', 'tablet', 'desktop')
    document.body.classList.add(this.deviceInfo.type)
  }

  private debounce(func: Function, wait: number): Function {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  public getDeviceInfo(): DeviceInfo {
    return { ...this.deviceInfo }
  }

  public isMobile(): boolean {
    return this.deviceInfo.type === 'mobile'
  }

  public isTablet(): boolean {
    return this.deviceInfo.type === 'tablet'
  }

  public isTouchDevice(): boolean {
    return this.deviceInfo.capabilities.touch
  }

  public enableFeature(feature: keyof MobileOptimizationConfig): void {
    this.config[feature] = true
  }

  public disableFeature(feature: keyof MobileOptimizationConfig): void {
    this.config[feature] = false
  }
}

// Barcode scanner utility for mobile devices
export class MobileBarcodeScanner {
  private stream: MediaStream | null = null
  private video: HTMLVideoElement | null = null

  async startScanning(
    onScan: (result: string) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      // Check if camera is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not available')
      }

      // Request camera permission
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })

      // Create video element
      this.video = document.createElement('video')
      this.video.srcObject = this.stream
      this.video.setAttribute('playsinline', 'true')
      this.video.play()

      // Note: Actual barcode scanning would require a library like QuaggaJS or ZXing
      // This is a placeholder for the scanning implementation
      console.log('Barcode scanner started (implementation needed)')
      
    } catch (error) {
      if (onError) {
        onError(error instanceof Error ? error : new Error(String(error)))
      }
    }
  }

  stopScanning(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop())
      this.stream = null
    }
    if (this.video) {
      this.video.remove()
      this.video = null
    }
  }
}

// React hooks for mobile optimization
import { useState, useEffect } from 'react'

export function useMobileOptimization() {
  const [optimizer] = useState(() => new MobileOptimizer())
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => optimizer.getDeviceInfo())

  useEffect(() => {
    const handleOrientationChange = () => {
      setDeviceInfo(optimizer.getDeviceInfo())
    }

    document.addEventListener('mobile-orientation-change', handleOrientationChange)
    window.addEventListener('resize', handleOrientationChange)

    return () => {
      document.removeEventListener('mobile-orientation-change', handleOrientationChange)
      window.removeEventListener('resize', handleOrientationChange)
    }
  }, [optimizer])

  return {
    deviceInfo,
    isMobile: optimizer.isMobile(),
    isTablet: optimizer.isTablet(),
    isTouchDevice: optimizer.isTouchDevice()
  }
}

export function usePullToRefresh(onRefresh: () => void | Promise<void>) {
  useEffect(() => {
    const handleRefresh = async () => {
      await onRefresh()
    }

    document.addEventListener('mobile-refresh', handleRefresh)
    
    return () => {
      document.removeEventListener('mobile-refresh', handleRefresh)
    }
  }, [onRefresh])
}

export function useSwipeGestures(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void
) {
  useEffect(() => {
    const handleSwipe = (e: CustomEvent) => {
      const { direction } = e.detail
      
      if (direction === 'left' && onSwipeLeft) {
        onSwipeLeft()
      } else if (direction === 'right' && onSwipeRight) {
        onSwipeRight()
      }
    }

    document.addEventListener('mobile-swipe', handleSwipe as EventListener)
    
    return () => {
      document.removeEventListener('mobile-swipe', handleSwipe as EventListener)
    }
  }, [onSwipeLeft, onSwipeRight])
}

export function useBarcodeScanner() {
  const [scanner] = useState(() => new MobileBarcodeScanner())
  const [isScanning, setIsScanning] = useState(false)

  const startScanning = async (onScan: (result: string) => void) => {
    try {
      setIsScanning(true)
      await scanner.startScanning(onScan, (error) => {
        console.error('Barcode scanner error:', error)
        setIsScanning(false)
      })
    } catch (error) {
      setIsScanning(false)
      throw error
    }
  }

  const stopScanning = () => {
    scanner.stopScanning()
    setIsScanning(false)
  }

  return {
    startScanning,
    stopScanning,
    isScanning
  }
}

// Create singleton instance
export const mobileOptimizer = new MobileOptimizer()

// Initialize on load
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    // Auto-initialize mobile optimizations
    mobileOptimizer.getDeviceInfo()
  })
}