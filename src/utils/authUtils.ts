import { supabase } from '@/lib/supabase'

// Clear all authentication data and reset state
export async function clearAuthSession() {
  try {
    console.log('🧹 Clearing auth session...')
    
    // Sign out from Supabase
    await supabase.auth.signOut({ scope: 'local' })
    
    // Clear localStorage items related to auth
    const authKeys = [
      'sb-qeozkzbjvvkgsvtitbny-auth-token',
      'supabase.auth.token',
      'sb-qeozkzbjvvkgsvtitbny-auth-token-refresh',
      'sb-qeozkzbjvvkgsvtitbny-auth-token-code-verifier'
    ]
    
    authKeys.forEach(key => {
      try {
        localStorage.removeItem(key)
      } catch (error) {
        // Silently fail
      }
    })
    
    // Clear any other possible auth storage
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.includes('supabase') || key.includes('auth')) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      // Silently fail
    }
    
    console.log('✅ Auth session cleared')
  } catch (error) {
    console.error('❌ Error clearing auth session:', error)
  }
}

// Check if current session is valid
export async function validateSession() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('❌ Session validation error:', error)
      return false
    }
    
    if (!session) {
      console.log('ℹ️ No session found')
      return false
    }
    
    // Check if token is expired
    const now = Date.now() / 1000
    if (session.expires_at && session.expires_at < now) {
      console.log('⏰ Session expired')
      return false
    }
    
    console.log('✅ Session is valid')
    return true
  } catch (error) {
    console.error('💥 Session validation failed:', error)
    return false
  }
}

// Force refresh session
export async function refreshSession() {
  try {
    console.log('🔄 Refreshing session...')
    
    const { data, error } = await supabase.auth.refreshSession()
    
    if (error) {
      console.error('❌ Session refresh error:', error)
      return false
    }
    
    if (data.session) {
      console.log('✅ Session refreshed successfully')
      return true
    }
    
    return false
  } catch (error) {
    console.error('💥 Session refresh failed:', error)
    return false
  }
}

// Reset auth state completely (nuclear option)
export async function resetAuthState() {
  console.log('💥 Resetting auth state completely...')
  
  // Clear session
  await clearAuthSession()
  
  // Force page reload to reset all state
  setTimeout(() => {
    window.location.href = '/login'
  }, 100)
}