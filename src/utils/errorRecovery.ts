/**
 * Error recovery and retry mechanisms for Mini-ERP system
 */

import { errorLogger } from './errorLogger'

export interface RetryConfig {
  maxAttempts: number
  delay: number
  backoffMultiplier: number
  maxDelay: number
}

export interface RecoveryStrategy {
  name: string
  description: string
  execute: () => Promise<boolean>
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  delay: 1000,
  backoffMultiplier: 2,
  maxDelay: 10000
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  context?: Record<string, any>
): Promise<T> {
  const { maxAttempts, delay, backoffMultiplier, maxDelay } = { ...DEFAULT_RETRY_CONFIG, ...config }
  let lastError: Error

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      errorLogger.logWarning(`Retry attempt ${attempt}/${maxAttempts} failed`, {
        ...context,
        error: lastError.message,
        attempt
      })

      if (attempt === maxAttempts) {
        errorLogger.logError(`All ${maxAttempts} retry attempts failed`, {
          ...context,
          error: lastError.message,
          totalAttempts: maxAttempts
        })
        break
      }

      // Calculate delay with exponential backoff
      const currentDelay = Math.min(delay * Math.pow(backoffMultiplier, attempt - 1), maxDelay)
      await new Promise(resolve => setTimeout(resolve, currentDelay))
    }
  }

  throw lastError!
}

/**
 * Network error recovery strategies
 */
export const networkRecoveryStrategies: RecoveryStrategy[] = [
  {
    name: 'refresh-connection',
    description: 'Refresh network connection',
    execute: async () => {
      try {
        // Test connectivity with a simple ping
        const response = await fetch('/health', { 
          method: 'HEAD',
          cache: 'no-cache'
        })
        return response.ok
      } catch {
        return false
      }
    }
  },
  {
    name: 'clear-cache',
    description: 'Clear application cache',
    execute: async () => {
      try {
        if ('caches' in window) {
          const cacheNames = await caches.keys()
          await Promise.all(cacheNames.map(name => caches.delete(name)))
        }
        return true
      } catch {
        return false
      }
    }
  },
  {
    name: 'reload-page',
    description: 'Reload the page',
    execute: async () => {
      window.location.reload()
      return true // This won't actually return, but satisfies the type
    }
  }
]

/**
 * Database error recovery strategies
 */
export const databaseRecoveryStrategies: RecoveryStrategy[] = [
  {
    name: 'reconnect-supabase',
    description: 'Reconnect to Supabase',
    execute: async () => {
      try {
        // Import Supabase client and test connection
        const { supabase } = await import('@/lib/supabase')
        const { data, error } = await supabase.from('users').select('id').limit(1)
        return !error
      } catch {
        return false
      }
    }
  },
  {
    name: 'switch-to-offline',
    description: 'Switch to offline mode',
    execute: async () => {
      try {
        localStorage.setItem('mini-erp-offline-mode', 'true')
        return true
      } catch {
        return false
      }
    }
  }
]

/**
 * MCP Server error recovery strategies
 */
export const mcpRecoveryStrategies: RecoveryStrategy[] = [
  {
    name: 'reconnect-mcp',
    description: 'Reconnect to MCP server',
    execute: async () => {
      try {
        const response = await fetch('/api/mcp/health')
        return response.ok
      } catch {
        return false
      }
    }
  },
  {
    name: 'fallback-static-printing',
    description: 'Fallback to static printer configuration',
    execute: async () => {
      try {
        localStorage.setItem('mini-erp-mcp-fallback', 'true')
        return true
      } catch {
        return false
      }
    }
  }
]

/**
 * Authentication error recovery strategies
 */
export const authRecoveryStrategies: RecoveryStrategy[] = [
  {
    name: 'refresh-token',
    description: 'Refresh authentication token',
    execute: async () => {
      try {
        const { supabase } = await import('@/lib/supabase')
        const { error } = await supabase.auth.refreshSession()
        return !error
      } catch {
        return false
      }
    }
  },
  {
    name: 'redirect-to-login',
    description: 'Redirect to login page',
    execute: async () => {
      window.location.href = '/login'
      return true
    }
  }
]

/**
 * Execute recovery strategies in sequence
 */
export async function executeRecoveryStrategies(
  strategies: RecoveryStrategy[],
  context?: Record<string, any>
): Promise<{ success: boolean; successfulStrategy?: string }> {
  for (const strategy of strategies) {
    try {
      errorLogger.logInfo(`Attempting recovery strategy: ${strategy.name}`, {
        ...context,
        strategy: strategy.name,
        description: strategy.description
      })

      const success = await strategy.execute()
      
      if (success) {
        errorLogger.logInfo(`Recovery strategy succeeded: ${strategy.name}`, {
          ...context,
          strategy: strategy.name
        })
        return { success: true, successfulStrategy: strategy.name }
      }
    } catch (error) {
      errorLogger.logWarning(`Recovery strategy failed: ${strategy.name}`, {
        ...context,
        strategy: strategy.name,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  errorLogger.logError('All recovery strategies failed', context)
  return { success: false }
}

/**
 * Generic error recovery function
 */
export async function recoverFromError(
  error: Error,
  errorType: 'network' | 'database' | 'mcp' | 'auth' | 'unknown' = 'unknown',
  context?: Record<string, any>
): Promise<{ recovered: boolean; strategy?: string }> {
  const errorContext = {
    ...context,
    errorType,
    errorMessage: error.message,
    errorStack: error.stack
  }

  errorLogger.logError(`Attempting recovery from ${errorType} error`, errorContext)

  let strategies: RecoveryStrategy[]

  switch (errorType) {
    case 'network':
      strategies = networkRecoveryStrategies
      break
    case 'database':
      strategies = databaseRecoveryStrategies
      break
    case 'mcp':
      strategies = mcpRecoveryStrategies
      break
    case 'auth':
      strategies = authRecoveryStrategies
      break
    default:
      strategies = [...networkRecoveryStrategies, ...databaseRecoveryStrategies]
  }

  const result = await executeRecoveryStrategies(strategies, errorContext)
  
  return {
    recovered: result.success,
    strategy: result.successfulStrategy
  }
}

/**
 * Circuit breaker pattern implementation
 */
export class CircuitBreaker {
  private failureCount = 0
  private lastFailureTime = 0
  private state: 'closed' | 'open' | 'half-open' = 'closed'

  constructor(
    private maxFailures: number = 5,
    private resetTimeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>, context?: Record<string, any>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'half-open'
        errorLogger.logInfo('Circuit breaker state changed to half-open', context)
      } else {
        throw new Error('Circuit breaker is open')
      }
    }

    try {
      const result = await operation()
      
      if (this.state === 'half-open') {
        this.reset()
        errorLogger.logInfo('Circuit breaker reset to closed', context)
      }
      
      return result
    } catch (error) {
      this.recordFailure()
      
      if (this.state === 'half-open' || this.failureCount >= this.maxFailures) {
        this.state = 'open'
        this.lastFailureTime = Date.now()
        
        errorLogger.logWarning('Circuit breaker opened', {
          ...context,
          failureCount: this.failureCount,
          maxFailures: this.maxFailures
        })
      }
      
      throw error
    }
  }

  private recordFailure(): void {
    this.failureCount++
  }

  private reset(): void {
    this.failureCount = 0
    this.state = 'closed'
  }

  getState(): { state: string; failureCount: number; lastFailureTime: number } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime
    }
  }
}

// Create circuit breakers for different services
export const supabaseCircuitBreaker = new CircuitBreaker(5, 60000)
export const mcpCircuitBreaker = new CircuitBreaker(3, 30000)

/**
 * React hook for error recovery
 */
import { useCallback, useState } from 'react'

export function useErrorRecovery() {
  const [isRecovering, setIsRecovering] = useState(false)
  const [lastRecoveryAttempt, setLastRecoveryAttempt] = useState<{
    timestamp: number
    success: boolean
    strategy?: string
  } | null>(null)

  const attemptRecovery = useCallback(async (
    error: Error,
    errorType: 'network' | 'database' | 'mcp' | 'auth' | 'unknown' = 'unknown',
    context?: Record<string, any>
  ) => {
    setIsRecovering(true)
    
    try {
      const result = await recoverFromError(error, errorType, context)
      
      setLastRecoveryAttempt({
        timestamp: Date.now(),
        success: result.recovered,
        strategy: result.strategy
      })
      
      return result.recovered
    } finally {
      setIsRecovering(false)
    }
  }, [])

  return {
    attemptRecovery,
    isRecovering,
    lastRecoveryAttempt
  }
}