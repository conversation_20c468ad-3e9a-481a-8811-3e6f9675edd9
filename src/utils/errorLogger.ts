/**
 * Centralized error logging and reporting system
 */

export interface ErrorReport {
  id: string
  timestamp: string
  level: 'error' | 'warning' | 'info'
  message: string
  stack?: string
  context?: Record<string, any>
  userId?: string
  sessionId?: string
  url?: string
  userAgent?: string
  componentStack?: string
}

export interface ErrorLoggerConfig {
  enableConsoleLogging: boolean
  enableRemoteLogging: boolean
  enableLocalStorage: boolean
  maxLocalErrors: number
  remoteEndpoint?: string
  apiKey?: string
}

class ErrorLogger {
  private config: ErrorLoggerConfig
  private sessionId: string
  private localErrors: ErrorReport[] = []

  constructor(config: Partial<ErrorLoggerConfig> = {}) {
    this.config = {
      enableConsoleLogging: process.env.NODE_ENV === 'development',
      enableRemoteLogging: process.env.NODE_ENV === 'production',
      enableLocalStorage: true,
      maxLocalErrors: 100,
      ...config
    }
    
    this.sessionId = this.generateSessionId()
    this.loadLocalErrors()
    this.setupGlobalErrorHandlers()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private loadLocalErrors(): void {
    if (!this.config.enableLocalStorage) return

    try {
      const stored = localStorage.getItem('mini-erp-errors')
      if (stored) {
        this.localErrors = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load stored errors:', error)
    }
  }

  private saveLocalErrors(): void {
    if (!this.config.enableLocalStorage) return

    try {
      // Keep only the most recent errors
      const errors = this.localErrors.slice(-this.config.maxLocalErrors)
      localStorage.setItem('mini-erp-errors', JSON.stringify(errors))
    } catch (error) {
      console.warn('Failed to save errors to localStorage:', error)
    }
  }

  private setupGlobalErrorHandlers(): void {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError(event.error || new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        source: 'global-error'
      })
    })

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError(new Error(event.reason?.toString() || 'Unhandled Promise Rejection'), {
        source: 'unhandled-promise',
        reason: event.reason
      })
    })
  }

  public logError(
    error: Error | string,
    context?: Record<string, any>,
    level: ErrorReport['level'] = 'error'
  ): string {
    const errorReport: ErrorReport = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      level,
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      context,
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    // Console logging
    if (this.config.enableConsoleLogging) {
      const logMethod = level === 'error' ? console.error : level === 'warning' ? console.warn : console.info
      logMethod('Error Report:', errorReport)
    }

    // Local storage
    if (this.config.enableLocalStorage) {
      this.localErrors.push(errorReport)
      this.saveLocalErrors()
    }

    // Remote logging
    if (this.config.enableRemoteLogging) {
      this.sendToRemote(errorReport)
    }

    return errorReport.id
  }

  public logWarning(message: string, context?: Record<string, any>): string {
    return this.logError(message, context, 'warning')
  }

  public logInfo(message: string, context?: Record<string, any>): string {
    return this.logError(message, context, 'info')
  }

  private getCurrentUserId(): string | undefined {
    try {
      // Try to get user ID from localStorage or auth context
      const authData = localStorage.getItem('sb-qeozkzbjvvkgsvtitbny-auth-token')
      if (authData) {
        const parsed = JSON.parse(authData)
        return parsed?.user?.id
      }
    } catch {
      // Ignore errors getting user ID
    }
    return undefined
  }

  private async sendToRemote(errorReport: ErrorReport): Promise<void> {
    if (!this.config.remoteEndpoint) return

    try {
      const response = await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify(errorReport)
      })

      if (!response.ok) {
        throw new Error(`Remote logging failed: ${response.status}`)
      }
    } catch (error) {
      // Don't log this error to avoid infinite loops
      console.warn('Failed to send error to remote endpoint:', error)
    }
  }

  public getLocalErrors(): ErrorReport[] {
    return [...this.localErrors]
  }

  public clearLocalErrors(): void {
    this.localErrors = []
    if (this.config.enableLocalStorage) {
      localStorage.removeItem('mini-erp-errors')
    }
  }

  public exportErrors(): string {
    const data = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      errors: this.localErrors
    }
    return JSON.stringify(data, null, 2)
  }

  public getErrorSummary(): {
    total: number
    byLevel: Record<string, number>
    recent: ErrorReport[]
    timeRange: string
  } {
    const now = Date.now()
    const oneHourAgo = now - (60 * 60 * 1000)
    
    const recentErrors = this.localErrors.filter(
      error => new Date(error.timestamp).getTime() > oneHourAgo
    )

    const byLevel = this.localErrors.reduce((acc, error) => {
      acc[error.level] = (acc[error.level] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total: this.localErrors.length,
      byLevel,
      recent: recentErrors.slice(-10), // Last 10 recent errors
      timeRange: '1 hour'
    }
  }
}

// Create singleton instance
export const errorLogger = new ErrorLogger({
  remoteEndpoint: process.env.NODE_ENV === 'production' ? '/api/errors' : undefined,
  apiKey: import.meta.env.VITE_ERROR_LOGGING_API_KEY
})

// React hook for error logging
import { useCallback } from 'react'

export function useErrorLogger() {
  const logError = useCallback((error: Error | string, context?: Record<string, any>) => {
    return errorLogger.logError(error, context)
  }, [])

  const logWarning = useCallback((message: string, context?: Record<string, any>) => {
    return errorLogger.logWarning(message, context)
  }, [])

  const logInfo = useCallback((message: string, context?: Record<string, any>) => {
    return errorLogger.logInfo(message, context)
  }, [])

  return { logError, logWarning, logInfo }
}