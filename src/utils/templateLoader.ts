import { supabase } from '@/lib/supabase'

// Predefined quick print templates for warehouse operations
export const PREDEFINED_TEMPLATES = [
  {
    name: 'HP External',
    description: 'Standard external shipping label for HP orders',
    category: 'shipping',
    template_type: 'quick_print',
    label_size: '102x152', // 4x6 inches
    zpl_template: `
^XA
^FO50,50^A0N,40,40^FDHP EXTERNAL SHIPMENT^FS
^FO50,120^A0N,30,30^FDHandle with Care^FS
^FO50,170^A0N,25,25^FDESD Sensitive Component^FS
^FO50,220^A0N,30,30^FDDate: {{DATE}}^FS
^FO50,270^A0N,25,25^FDShip To: {{CUSTOMER}}^FS
^FO50,320^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Fragile - Handle with Care',
    description: 'Warning label for fragile components',
    category: 'warning',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO200,50^A0N,50,50^FDFRAGILE^FS
^FO100,130^A0N,35,35^FDHandle with Care^FS
^FO50,200^A0N,25,25^FDThis Side Up^FS
^FO50,250^A0N,20,20^FDDate: {{DATE}}^FS
^FO50,290^A0N,20,20^FDOperator: {{USER}}^FS
^FO50,330^GB500,2,2^FS
^FO50,350^A0N,18,18^FDComponent: {{PART_NUMBER}}^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Priority Processing',
    description: 'High priority order identification label',
    category: 'priority',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO150,40^A0N,60,60^FDPRIORITY^FS
^FO50,120^A0N,25,25^FDRush Order - Process First^FS
^FO50,170^A0N,30,30^FDOrder: {{ORDER_UID}}^FS
^FO50,220^A0N,25,25^FDCustomer: {{CUSTOMER}}^FS
^FO50,270^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,310^A0N,18,18^FDDate: {{DATE}} Time: {{TIME}}^FS
^FO50,350^A0N,18,18^FDRequested by: {{USER}}^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'QC Hold',
    description: 'Quality control hold notification label',
    category: 'qc',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO180,50^A0N,45,45^FDQC HOLD^FS
^FO100,120^A0N,25,25^FDDo Not Process^FS
^FO50,170^A0N,30,30^FDAwaiting QC Approval^FS
^FO50,220^A0N,20,20^FDOrder: {{ORDER_UID}}^FS
^FO50,260^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,300^A0N,18,18^FDHold Date: {{DATE}}^FS
^FO50,330^A0N,18,18^FDInspector: {{USER}}^FS
^FO50,360^A0N,16,16^FDContact QC for release^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Inspection Required',
    description: 'Component requires inspection before processing',
    category: 'inspection',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO120,40^A0N,40,40^FDINSPECTION^FS
^FO150,90^A0N,40,40^FDREQUIRED^FS
^FO50,150^A0N,25,25^FDStop - Inspect Before Use^FS
^FO50,190^A0N,20,20^FDOrder: {{ORDER_UID}}^FS
^FO50,220^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,250^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,280^A0N,18,18^FDDate: {{DATE}}^FS
^FO50,310^A0N,16,16^FDInspected by: ___________^FS
^FO50,340^A0N,16,16^FDApproved: ___________^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Motherboard Testing',
    description: 'NP location motherboard testing label',
    category: 'testing',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO100,40^A0N,35,35^FDMOTHERBOARD^FS
^FO150,80^A0N,35,35^FDTESTING^FS
^FO50,140^A0N,25,25^FDNP Location Only^FS
^FO50,180^A0N,20,20^FDOrder: {{ORDER_UID}}^FS
^FO50,210^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,240^A0N,18,18^FDTesting Date: {{DATE}}^FS
^FO50,270^A0N,18,18^FDTechnician: {{USER}}^FS
^FO50,300^A0N,16,16^FDTest Results: ___________^FS
^FO50,330^A0N,16,16^FDPass/Fail: ___________^FS
^FO50,360^A0N,16,16^FDApproved: ___________^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Return Processing',
    description: 'Customer return item processing label',
    category: 'returns',
    template_type: 'quick_print',
    label_size: '102x152',
    zpl_template: `
^XA
^FO150,40^A0N,45,45^FDRETURN^FS
^FO120,100^A0N,25,25^FDCustomer Return^FS
^FO50,150^A0N,20,20^FDReturn Date: {{DATE}}^FS
^FO50,180^A0N,20,20^FDOriginal Order: {{ORDER_UID}}^FS
^FO50,210^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,240^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,270^A0N,18,18^FDReason: ___________^FS
^FO50,300^A0N,16,16^FDCondition: ___________^FS
^FO50,330^A0N,16,16^FDAction: ___________^FS
^FO50,360^A0N,16,16^FDProcessed by: {{USER}}^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  }
]

// CT Label templates (for CT assignment workflow)
export const CT_LABEL_TEMPLATES = [
  {
    name: 'Default CT Label',
    description: 'Standard CT label with order information and QR code',
    category: 'ct_labels',
    template_type: 'ct_label',
    label_size: '102x152',
    zpl_template: `
^XA
^FO50,30^A0N,25,25^FDOrder: {{ORDER_UID}}^FS
^FO50,65^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,95^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,125^A0N,30,30^FDCT: {{CT_NUMBER}}^FS
^FO50,170^A0N,16,16^FDQty: {{QUANTITY}} | Date: {{DATE}}^FS
^FO50,200^BQN,2,6^FDMA,{{CT_NUMBER}}^FS
^FO350,250^A0N,14,14^FDScan for Details^FS
^FO50,350^A0N,14,14^FDMini-ERP System^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'Compact CT Label',
    description: 'Compact CT label for small components',
    category: 'ct_labels',
    template_type: 'ct_label',
    label_size: '51x25',
    zpl_template: `
^XA
^FO20,20^A0N,20,20^FD{{ORDER_UID}}^FS
^FO20,50^A0N,25,25^FD{{CT_NUMBER}}^FS
^FO20,80^A0N,12,12^FD{{DATE}}^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  },
  {
    name: 'HP CT Label',
    description: 'HP-specific CT label with branding and ESD warning',
    category: 'ct_labels',
    template_type: 'ct_label',
    label_size: '102x152',
    zpl_template: `
^XA
^FO200,20^A0N,20,20^FDHP ORDER^FS
^FO50,50^A0N,25,25^FDOrder: {{ORDER_UID}}^FS
^FO50,85^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,115^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,145^A0N,30,30^FDCT: {{CT_NUMBER}}^FS
^FO50,190^A0N,16,16^FDQty: {{QUANTITY}} | Date: {{DATE}}^FS
^FO50,220^BQN,2,5^FDMA,{{CT_NUMBER}}^FS
^FO300,290^A0N,12,12^FDScan Here^FS
^FO50,330^A0N,14,14^FDHandle with Care^FS
^FO50,350^A0N,14,14^FDESD Sensitive^FS
^XZ`,
    is_active: true,
    created_by: 'system'
  }
]

// Load templates into database
export const loadPredefinedTemplates = async (): Promise<{ 
  success: boolean, 
  loaded: number, 
  errors: string[] 
}> => {
  const errors: string[] = []
  let loadedCount = 0

  try {
    // Combine all templates
    const allTemplates = [...PREDEFINED_TEMPLATES, ...CT_LABEL_TEMPLATES]

    for (const template of allTemplates) {
      try {
        // Check if template already exists
        const { data: existingTemplate } = await supabase
          .from('label_templates')
          .select('id')
          .eq('name', template.name)
          .single()

        if (existingTemplate) {
          console.log(`Template "${template.name}" already exists, skipping...`)
          continue
        }

        // Insert new template
        const { error } = await supabase
          .from('label_templates')
          .insert({
            name: template.name,
            description: template.description,
            category: template.category,
            template_type: template.template_type,
            label_size: template.label_size,
            zpl_template: template.zpl_template,
            is_active: template.is_active,
            created_by: template.created_by,
            created_at: new Date().toISOString()
          })

        if (error) {
          errors.push(`Failed to load template "${template.name}": ${error.message}`)
        } else {
          loadedCount++
          console.log(`✅ Loaded template: ${template.name}`)
        }

      } catch (templateError) {
        const errorMessage = templateError instanceof Error ? templateError.message : 'Unknown error'
        errors.push(`Error processing template "${template.name}": ${errorMessage}`)
      }
    }

    return {
      success: errors.length === 0,
      loaded: loadedCount,
      errors
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return {
      success: false,
      loaded: 0,
      errors: [`Failed to load templates: ${errorMessage}`]
    }
  }
}

// Get templates by category
export const getTemplatesByCategory = async (category: string) => {
  const { data, error } = await supabase
    .from('label_templates')
    .select('*')
    .eq('category', category)
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Failed to fetch templates by category:', error)
    return []
  }

  return data || []
}

// Get templates by type
export const getTemplatesByType = async (templateType: 'quick_print' | 'ct_label') => {
  const { data, error } = await supabase
    .from('label_templates')
    .select('*')
    .eq('template_type', templateType)
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Failed to fetch templates by type:', error)
    return []
  }

  return data || []
}

// Template categories for organization
export const TEMPLATE_CATEGORIES = {
  shipping: 'Shipping & External',
  warning: 'Warning Labels',
  priority: 'Priority & Rush',
  qc: 'Quality Control',
  inspection: 'Inspection & Testing',
  testing: 'Testing & Validation',
  returns: 'Returns & RMA',
  ct_labels: 'CT Number Labels',
  custom: 'Custom Templates'
} as const

// Template validation
export const validateTemplate = (template: any): { valid: boolean, errors: string[] } => {
  const errors: string[] = []

  if (!template.name || template.name.trim().length === 0) {
    errors.push('Template name is required')
  }

  if (!template.description || template.description.trim().length === 0) {
    errors.push('Template description is required')
  }

  if (!template.category || !Object.keys(TEMPLATE_CATEGORIES).includes(template.category)) {
    errors.push('Valid template category is required')
  }

  if (!template.template_type || !['quick_print', 'ct_label'].includes(template.template_type)) {
    errors.push('Template type must be either "quick_print" or "ct_label"')
  }

  if (!template.label_size || template.label_size.trim().length === 0) {
    errors.push('Label size is required (e.g., "102x152")')
  }

  if (!template.zpl_template || template.zpl_template.trim().length === 0) {
    errors.push('ZPL template content is required')
  }

  // Validate ZPL format
  if (template.zpl_template && !template.zpl_template.includes('^XA') && !template.zpl_template.includes('^XZ')) {
    errors.push('ZPL template must contain valid ZPL commands (^XA...^XZ)')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Initialize template system
export const initializeTemplateSystem = async (): Promise<void> => {
  console.log('🔄 Initializing template system...')
  
  const result = await loadPredefinedTemplates()
  
  if (result.success) {
    console.log(`✅ Template system initialized successfully! Loaded ${result.loaded} templates.`)
  } else {
    console.error('❌ Template system initialization had errors:')
    result.errors.forEach(error => console.error(`  - ${error}`))
    console.log(`ℹ️  Successfully loaded ${result.loaded} templates despite errors.`)
  }
}