/**
 * Real-time system stress testing utilities for Mini-ERP
 */

import { supabase } from '@/lib/supabase'
import { errorLogger } from './errorLogger'
import { performanceMonitor } from './monitoring'

export interface StressTestConfig {
  concurrentUsers: number
  testDurationMs: number
  operationsPerSecond: number
  operationTypes: OperationType[]
  realtimeChannels: string[]
}

export interface OperationType {
  name: string
  weight: number // 0-1, relative frequency
  execute: () => Promise<any>
}

export interface StressTestResults {
  testId: string
  config: StressTestConfig
  startTime: number
  endTime: number
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  averageResponseTime: number
  maxResponseTime: number
  minResponseTime: number
  operationsPerSecond: number
  realtimeMetrics: RealtimeMetrics
  errors: Array<{ timestamp: number; error: string; operation: string }>
}

export interface RealtimeMetrics {
  subscriptionCount: number
  messagesSent: number
  messagesReceived: number
  averageLatency: number
  maxLatency: number
  connectionErrors: number
  reconnections: number
}

export class StressTester {
  private isRunning = false
  private workers: Worker[] = []
  private results: Partial<StressTestResults> = {}
  private startTime = 0
  private realtimeConnections: any[] = []

  constructor() {
    this.setupRealtimeMonitoring()
  }

  private setupRealtimeMonitoring() {
    // Monitor realtime connection health
    supabase.realtime.onConnect(() => {
      performanceMonitor.recordMetric('realtime-connect', Date.now())
    })

    supabase.realtime.onDisconnect(() => {
      performanceMonitor.recordMetric('realtime-disconnect', Date.now())
    })
  }

  async runStressTest(config: StressTestConfig): Promise<StressTestResults> {
    if (this.isRunning) {
      throw new Error('Stress test already running')
    }

    this.isRunning = true
    this.startTime = Date.now()
    
    const testId = `stress-test-${this.startTime}`
    
    this.results = {
      testId,
      config,
      startTime: this.startTime,
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      errors: [],
      realtimeMetrics: {
        subscriptionCount: 0,
        messagesSent: 0,
        messagesReceived: 0,
        averageLatency: 0,
        maxLatency: 0,
        connectionErrors: 0,
        reconnections: 0
      }
    }

    errorLogger.logInfo(`Starting stress test: ${testId}`, config)

    try {
      // Setup realtime subscriptions
      await this.setupRealtimeSubscriptions(config)
      
      // Run concurrent user simulations
      await this.runConcurrentOperations(config)
      
      // Collect final results
      const finalResults = await this.collectResults()
      
      errorLogger.logInfo(`Stress test completed: ${testId}`, finalResults)
      
      return finalResults as StressTestResults
    } catch (error) {
      errorLogger.logError('Stress test failed', { testId, error })
      throw error
    } finally {
      await this.cleanup()
      this.isRunning = false
    }
  }

  private async setupRealtimeSubscriptions(config: StressTestConfig): Promise<void> {
    for (const channel of config.realtimeChannels) {
      try {
        const subscription = supabase
          .channel(channel)
          .on('postgres_changes', 
            { 
              event: '*', 
              schema: 'public',
              table: channel 
            }, 
            (payload) => {
              this.results.realtimeMetrics!.messagesReceived++
              performanceMonitor.recordMetric(
                'realtime-message-received', 
                Date.now(),
                { channel, event: payload.eventType }
              )
            }
          )
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              this.results.realtimeMetrics!.subscriptionCount++
            } else if (status === 'CHANNEL_ERROR') {
              this.results.realtimeMetrics!.connectionErrors++
            }
          })

        this.realtimeConnections.push(subscription)
      } catch (error) {
        errorLogger.logError('Failed to setup realtime subscription', {
          channel,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    // Wait for subscriptions to be established
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  private async runConcurrentOperations(config: StressTestConfig): Promise<void> {
    const operationsPerUser = Math.floor(
      (config.operationsPerSecond * config.testDurationMs) / 
      (1000 * config.concurrentUsers)
    )

    const userPromises = Array.from({ length: config.concurrentUsers }, (_, index) =>
      this.simulateUser(index, operationsPerUser, config)
    )

    await Promise.all(userPromises)
  }

  private async simulateUser(
    userId: number, 
    operationCount: number, 
    config: StressTestConfig
  ): Promise<void> {
    const operations: OperationType[] = []
    
    // Build weighted operation list
    for (const opType of config.operationTypes) {
      const count = Math.floor(operationCount * opType.weight)
      for (let i = 0; i < count; i++) {
        operations.push(opType)
      }
    }

    // Shuffle operations to randomize execution order
    for (let i = operations.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [operations[i], operations[j]] = [operations[j], operations[i]]
    }

    // Execute operations with random delays
    for (const operation of operations) {
      if (!this.isRunning) break

      try {
        const startTime = performance.now()
        await operation.execute()
        const endTime = performance.now()
        
        const responseTime = endTime - startTime
        this.updateMetrics(true, responseTime)
        
        performanceMonitor.recordMetric(
          `stress-test-${operation.name}`,
          responseTime,
          { userId, success: true }
        )

        // Random delay between operations (simulate realistic user behavior)
        const delay = Math.random() * 1000 + 500 // 0.5-1.5 seconds
        await new Promise(resolve => setTimeout(resolve, delay))
        
      } catch (error) {
        this.updateMetrics(false, 0)
        this.results.errors!.push({
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : String(error),
          operation: operation.name
        })
        
        performanceMonitor.recordMetric(
          `stress-test-${operation.name}`,
          0,
          { userId, success: false, error: String(error) }
        )
      }
    }
  }

  private updateMetrics(success: boolean, responseTime: number): void {
    this.results.totalOperations = (this.results.totalOperations || 0) + 1
    
    if (success) {
      this.results.successfulOperations = (this.results.successfulOperations || 0) + 1
      
      // Update response time metrics
      if (!this.results.minResponseTime || responseTime < this.results.minResponseTime) {
        this.results.minResponseTime = responseTime
      }
      if (!this.results.maxResponseTime || responseTime > this.results.maxResponseTime) {
        this.results.maxResponseTime = responseTime
      }
    } else {
      this.results.failedOperations = (this.results.failedOperations || 0) + 1
    }
  }

  private async collectResults(): Promise<StressTestResults> {
    const endTime = Date.now()
    const duration = endTime - this.startTime
    
    // Calculate final metrics
    const totalSuccessful = this.results.successfulOperations || 0
    const totalOperations = this.results.totalOperations || 0
    
    const averageResponseTime = totalSuccessful > 0 
      ? performanceMonitor.getMetrics()
          .filter(m => m.name.startsWith('stress-test-'))
          .reduce((sum, m) => sum + m.value, 0) / totalSuccessful
      : 0

    const operationsPerSecond = totalOperations > 0 
      ? (totalOperations / duration) * 1000
      : 0

    return {
      ...this.results,
      endTime,
      averageResponseTime,
      operationsPerSecond
    } as StressTestResults
  }

  private async cleanup(): Promise<void> {
    // Unsubscribe from all realtime channels
    for (const subscription of this.realtimeConnections) {
      try {
        await supabase.removeChannel(subscription)
      } catch (error) {
        errorLogger.logWarning('Failed to cleanup realtime subscription', { error })
      }
    }
    this.realtimeConnections = []
  }

  public isTestRunning(): boolean {
    return this.isRunning
  }

  public stopTest(): void {
    this.isRunning = false
  }
}

// Predefined operation types for different stress test scenarios
export const CommonOperations: Record<string, OperationType> = {
  readOrders: {
    name: 'read-orders',
    weight: 0.4, // 40% of operations
    execute: async () => {
      const { data, error } = await supabase
        .from('order_lines')
        .select('*')
        .limit(50)
        .order('created_at', { ascending: false })
      
      if (error) throw error
      return data
    }
  },

  createOrder: {
    name: 'create-order',
    weight: 0.1, // 10% of operations
    execute: async () => {
      const testOrder = {
        uid: `STRESS-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        customer_id: 'c1234567-1234-1234-1234-123456789012', // Test customer
        part_number: `PART-${Math.random().toString(36).substr(2, 6)}`,
        description: 'Stress test order',
        total_order_quantity: Math.floor(Math.random() * 100) + 1,
        unit_price: Math.floor(Math.random() * 1000) + 10,
        current_status: 'pending_procurement_arrangement'
      }

      const { data, error } = await supabase
        .from('order_lines')
        .insert(testOrder)
        .select()
      
      if (error) throw error
      return data
    }
  },

  updateQuantities: {
    name: 'update-quantities',
    weight: 0.2, // 20% of operations
    execute: async () => {
      // Get a random order to update
      const { data: orders } = await supabase
        .from('order_lines')
        .select('id')
        .limit(1)
        .order('created_at', { ascending: false })
      
      if (orders && orders.length > 0) {
        const { data, error } = await supabase
          .from('order_line_quantities')
          .upsert({
            order_line_id: orders[0].id,
            status: 'in_kitting_packing',
            quantity: Math.floor(Math.random() * 10) + 1,
            updated_at: new Date().toISOString()
          })
        
        if (error) throw error
        return data
      }
    }
  },

  assignCTNumbers: {
    name: 'assign-ct-numbers',
    weight: 0.15, // 15% of operations
    execute: async () => {
      const ctNumber = `STRESS${Date.now()}${Math.random().toString(36).substr(2, 6)}`.toUpperCase()
      
      // Get a random order to assign CT to
      const { data: orders } = await supabase
        .from('order_lines')
        .select('id')
        .limit(1)
        .order('created_at', { ascending: false })
      
      if (orders && orders.length > 0) {
        const { data, error } = await supabase
          .from('ct_numbers')
          .insert({
            ct_number: ctNumber,
            order_line_id: orders[0].id,
            assigned_date: new Date().toISOString()
          })
        
        if (error) throw error
        return data
      }
    }
  },

  searchOperations: {
    name: 'search-operations',
    weight: 0.15, // 15% of operations
    execute: async () => {
      const searchTerms = ['PART', 'TEST', 'HP', 'LENOVO', 'STRESS']
      const term = searchTerms[Math.floor(Math.random() * searchTerms.length)]
      
      const { data, error } = await supabase
        .from('order_lines')
        .select('*')
        .or(`part_number.ilike.%${term}%,description.ilike.%${term}%`)
        .limit(20)
      
      if (error) throw error
      return data
    }
  }
}

// Predefined stress test configurations
export const StressTestConfigurations: Record<string, StressTestConfig> = {
  light: {
    concurrentUsers: 5,
    testDurationMs: 2 * 60 * 1000, // 2 minutes
    operationsPerSecond: 2,
    operationTypes: [
      CommonOperations.readOrders,
      CommonOperations.searchOperations
    ],
    realtimeChannels: ['order_lines', 'ct_numbers']
  },

  moderate: {
    concurrentUsers: 15,
    testDurationMs: 5 * 60 * 1000, // 5 minutes
    operationsPerSecond: 5,
    operationTypes: Object.values(CommonOperations),
    realtimeChannels: ['order_lines', 'ct_numbers', 'order_line_quantities']
  },

  heavy: {
    concurrentUsers: 30,
    testDurationMs: 10 * 60 * 1000, // 10 minutes
    operationsPerSecond: 10,
    operationTypes: Object.values(CommonOperations),
    realtimeChannels: ['order_lines', 'ct_numbers', 'order_line_quantities', 'quantity_logs']
  },

  realtimeFocused: {
    concurrentUsers: 20,
    testDurationMs: 3 * 60 * 1000, // 3 minutes
    operationsPerSecond: 8,
    operationTypes: [
      CommonOperations.createOrder,
      CommonOperations.updateQuantities,
      CommonOperations.assignCTNumbers
    ],
    realtimeChannels: ['order_lines', 'ct_numbers', 'order_line_quantities', 'quantity_logs']
  }
}

// React hook for running stress tests
import { useState, useCallback } from 'react'

export function useStressTesting() {
  const [tester] = useState(() => new StressTester())
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<StressTestResults | null>(null)
  const [error, setError] = useState<string | null>(null)

  const runTest = useCallback(async (config: StressTestConfig) => {
    try {
      setIsRunning(true)
      setError(null)
      setResults(null)
      
      const testResults = await tester.runStressTest(config)
      setResults(testResults)
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err))
    } finally {
      setIsRunning(false)
    }
  }, [tester])

  const stopTest = useCallback(() => {
    tester.stopTest()
    setIsRunning(false)
  }, [tester])

  return {
    runTest,
    stopTest,
    isRunning,
    results,
    error
  }
}