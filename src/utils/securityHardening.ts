/**
 * Security hardening utilities for Mini-ERP system
 */

import { supabase } from '@/lib/supabase'
import { errorLogger } from './errorLogger'

export interface SecurityEvent {
  id: string
  timestamp: string
  event_type: 'login' | 'logout' | 'failed_login' | 'permission_denied' | 'data_access' | 'sensitive_operation'
  user_id?: string
  user_email?: string
  ip_address?: string
  user_agent?: string
  resource?: string
  action?: string
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  details?: Record<string, any>
}

export interface SecurityPolicy {
  name: string
  enabled: boolean
  config: Record<string, any>
}

export interface AuditLogEntry {
  id: string
  timestamp: string
  user_id: string
  action: string
  resource_type: string
  resource_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address?: string
  user_agent?: string
}

class SecurityHardening {
  private securityPolicies: Map<string, SecurityPolicy> = new Map()
  private sessionData: Map<string, any> = new Map()

  constructor() {
    this.initializeSecurityPolicies()
    this.setupSecurityMonitoring()
  }

  private initializeSecurityPolicies(): void {
    const defaultPolicies: SecurityPolicy[] = [
      {
        name: 'password_complexity',
        enabled: true,
        config: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true
        }
      },
      {
        name: 'session_security',
        enabled: true,
        config: {
          maxSessionDuration: 8 * 60 * 60 * 1000, // 8 hours
          idleTimeout: 30 * 60 * 1000, // 30 minutes
          requireReauthForSensitive: true
        }
      },
      {
        name: 'rate_limiting',
        enabled: true,
        config: {
          loginAttempts: 5,
          loginWindow: 15 * 60 * 1000, // 15 minutes
          apiCallsPerMinute: 100
        }
      },
      {
        name: 'data_encryption',
        enabled: true,
        config: {
          encryptSensitiveFields: true,
          encryptPII: true,
          keyRotationInterval: 90 * 24 * 60 * 60 * 1000 // 90 days
        }
      },
      {
        name: 'audit_logging',
        enabled: true,
        config: {
          logAllDataChanges: true,
          logSensitiveAccess: true,
          retentionDays: 365
        }
      }
    ]

    defaultPolicies.forEach(policy => {
      this.securityPolicies.set(policy.name, policy)
    })
  }

  private setupSecurityMonitoring(): void {
    // Monitor authentication events
    this.setupAuthenticationMonitoring()
    
    // Monitor sensitive operations
    this.setupOperationMonitoring()
    
    // Setup session monitoring
    this.setupSessionMonitoring()
  }

  private setupAuthenticationMonitoring(): void {
    // Listen for auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      const securityEvent: Partial<SecurityEvent> = {
        timestamp: new Date().toISOString(),
        user_id: session?.user?.id,
        user_email: session?.user?.email,
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent
      }

      switch (event) {
        case 'SIGNED_IN':
          await this.logSecurityEvent({
            ...securityEvent,
            event_type: 'login',
            risk_level: 'low',
            details: { provider: session?.user?.app_metadata?.provider }
          })
          break
          
        case 'SIGNED_OUT':
          await this.logSecurityEvent({
            ...securityEvent,
            event_type: 'logout',
            risk_level: 'low'
          })
          break
          
        case 'TOKEN_REFRESHED':
          // Update session tracking
          if (session?.user?.id) {
            this.sessionData.set(session.user.id, {
              lastActivity: Date.now(),
              sessionStart: this.sessionData.get(session.user.id)?.sessionStart || Date.now()
            })
          }
          break
      }
    })
  }

  private setupOperationMonitoring(): void {
    // Monitor database operations
    this.monitorDatabaseOperations()
    
    // Monitor file access
    this.monitorFileAccess()
    
    // Monitor API calls
    this.monitorAPIAccess()
  }

  private setupSessionMonitoring(): void {
    const sessionPolicy = this.securityPolicies.get('session_security')
    if (!sessionPolicy?.enabled) return

    // Check for idle sessions
    setInterval(() => {
      this.checkIdleSessions()
    }, 60000) // Check every minute

    // Track user activity
    const events = ['click', 'keypress', 'scroll', 'mousemove']
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.updateLastActivity()
      }, { passive: true })
    })
  }

  private async getClientIP(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json')
      const data = await response.json()
      return data.ip
    } catch {
      return 'unknown'
    }
  }

  public async logSecurityEvent(event: Partial<SecurityEvent>): Promise<void> {
    const fullEvent: SecurityEvent = {
      id: `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      risk_level: 'low',
      ...event
    } as SecurityEvent

    try {
      // Log to security_events table
      const { error } = await supabase
        .from('security_events')
        .insert(fullEvent)

      if (error) {
        errorLogger.logError('Failed to log security event', { error, event: fullEvent })
      }

      // Alert on high-risk events
      if (fullEvent.risk_level === 'high' || fullEvent.risk_level === 'critical') {
        await this.handleHighRiskEvent(fullEvent)
      }
    } catch (error) {
      errorLogger.logError('Security event logging failed', { error, event: fullEvent })
    }
  }

  public async logAuditEvent(entry: Partial<AuditLogEntry>): Promise<void> {
    const auditPolicy = this.securityPolicies.get('audit_logging')
    if (!auditPolicy?.enabled) return

    const fullEntry: AuditLogEntry = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ip_address: await this.getClientIP(),
      user_agent: navigator.userAgent,
      ...entry
    } as AuditLogEntry

    try {
      const { error } = await supabase
        .from('audit_logs')
        .insert(fullEntry)

      if (error) {
        errorLogger.logError('Failed to log audit event', { error, entry: fullEntry })
      }
    } catch (error) {
      errorLogger.logError('Audit logging failed', { error, entry: fullEntry })
    }
  }

  private async handleHighRiskEvent(event: SecurityEvent): Promise<void> {
    // Send alert notifications
    errorLogger.logError(`High-risk security event: ${event.event_type}`, {
      securityEvent: event,
      requiresAttention: true
    })

    // Could implement additional alerting mechanisms:
    // - Email alerts
    // - WhatsApp notifications
    // - Slack notifications
    // - Dashboard alerts
  }

  private monitorDatabaseOperations(): void {
    // Wrap database operations to add audit logging
    const originalFrom = supabase.from.bind(supabase)
    
    // This is a conceptual example - in practice, you'd implement this 
    // at the query level or use database triggers
  }

  private monitorFileAccess(): void {
    // Monitor access to sensitive files and documents
    const originalFetch = window.fetch
    
    window.fetch = async (...args) => {
      const response = await originalFetch(...args)
      const url = args[0] as string
      
      // Log access to sensitive endpoints
      if (this.isSensitiveURL(url)) {
        await this.logSecurityEvent({
          event_type: 'data_access',
          risk_level: 'medium',
          resource: url,
          action: 'fetch',
          details: { status: response.status }
        })
      }
      
      return response
    }
  }

  private monitorAPIAccess(): void {
    // Monitor API access patterns for anomalies
    const apiCallCounts: Map<string, number> = new Map()
    
    // This would be implemented with more sophisticated tracking
  }

  private isSensitiveURL(url: string): boolean {
    const sensitivePatterns = [
      '/api/users',
      '/api/admin',
      '/storage/fai_documents',
      '/api/mcp',
      'password',
      'sensitive'
    ]
    
    return sensitivePatterns.some(pattern => url.includes(pattern))
  }

  private checkIdleSessions(): void {
    const sessionPolicy = this.securityPolicies.get('session_security')
    if (!sessionPolicy?.enabled) return

    const idleTimeout = sessionPolicy.config.idleTimeout
    const now = Date.now()

    for (const [userId, sessionInfo] of this.sessionData.entries()) {
      if (now - sessionInfo.lastActivity > idleTimeout) {
        this.handleIdleSession(userId)
      }
    }
  }

  private async handleIdleSession(userId: string): Promise<void> {
    await this.logSecurityEvent({
      event_type: 'logout',
      user_id: userId,
      risk_level: 'low',
      details: { reason: 'idle_timeout' }
    })

    // Force logout
    await supabase.auth.signOut()
    
    // Redirect to login
    window.location.href = '/login'
  }

  private updateLastActivity(): void {
    const user = supabase.auth.getUser()
    if (user) {
      // Update activity timestamp
      const userId = (user as any)?.data?.user?.id
      if (userId && this.sessionData.has(userId)) {
        const sessionInfo = this.sessionData.get(userId)
        sessionInfo.lastActivity = Date.now()
        this.sessionData.set(userId, sessionInfo)
      }
    }
  }

  public validatePasswordComplexity(password: string): { valid: boolean; errors: string[] } {
    const policy = this.securityPolicies.get('password_complexity')
    if (!policy?.enabled) return { valid: true, errors: [] }

    const errors: string[] = []
    const config = policy.config

    if (password.length < config.minLength) {
      errors.push(`Password must be at least ${config.minLength} characters long`)
    }

    if (config.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (config.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }

    if (config.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    if (config.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    return { valid: errors.length === 0, errors }
  }

  public async checkSuspiciousActivity(userId: string): Promise<{
    suspicious: boolean
    reasons: string[]
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
  }> {
    const reasons: string[] = []
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'

    try {
      // Check for multiple failed login attempts
      const { data: failedLogins } = await supabase
        .from('security_events')
        .select('*')
        .eq('user_id', userId)
        .eq('event_type', 'failed_login')
        .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours

      if (failedLogins && failedLogins.length > 3) {
        reasons.push('Multiple failed login attempts in 24 hours')
        riskLevel = 'medium'
      }

      // Check for unusual access patterns
      const { data: accessEvents } = await supabase
        .from('security_events')
        .select('*')
        .eq('user_id', userId)
        .eq('event_type', 'data_access')
        .gte('timestamp', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour

      if (accessEvents && accessEvents.length > 50) {
        reasons.push('Unusually high data access rate')
        riskLevel = 'high'
      }

      // Check for access from multiple IPs
      const uniqueIPs = new Set(accessEvents?.map(event => event.details?.ip_address).filter(Boolean))
      if (uniqueIPs.size > 3) {
        reasons.push('Access from multiple IP addresses')
        riskLevel = 'medium'
      }

      return {
        suspicious: reasons.length > 0,
        reasons,
        riskLevel
      }
    } catch (error) {
      errorLogger.logError('Failed to check suspicious activity', { userId, error })
      return { suspicious: false, reasons: [], riskLevel: 'low' }
    }
  }

  public async generateSecurityReport(): Promise<{
    summary: {
      totalEvents: number
      highRiskEvents: number
      failedLogins: number
      suspiciousUsers: number
    }
    recentEvents: SecurityEvent[]
    recommendations: string[]
  }> {
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()

      // Get security events summary
      const { data: events } = await supabase
        .from('security_events')
        .select('*')
        .gte('timestamp', last24Hours)
        .order('timestamp', { ascending: false })

      const totalEvents = events?.length || 0
      const highRiskEvents = events?.filter(e => e.risk_level === 'high' || e.risk_level === 'critical').length || 0
      const failedLogins = events?.filter(e => e.event_type === 'failed_login').length || 0

      // Generate recommendations
      const recommendations: string[] = []
      
      if (failedLogins > 10) {
        recommendations.push('Consider implementing stronger rate limiting for login attempts')
      }
      
      if (highRiskEvents > 5) {
        recommendations.push('Review high-risk security events and consider additional monitoring')
      }
      
      if (totalEvents > 1000) {
        recommendations.push('High security event volume - consider adjusting monitoring thresholds')
      }

      return {
        summary: {
          totalEvents,
          highRiskEvents,
          failedLogins,
          suspiciousUsers: 0 // Would be calculated from user analysis
        },
        recentEvents: events?.slice(0, 20) || [],
        recommendations
      }
    } catch (error) {
      errorLogger.logError('Failed to generate security report', { error })
      throw error
    }
  }

  public getSecurityPolicies(): SecurityPolicy[] {
    return Array.from(this.securityPolicies.values())
  }

  public updateSecurityPolicy(name: string, policy: Partial<SecurityPolicy>): void {
    const existing = this.securityPolicies.get(name)
    if (existing) {
      this.securityPolicies.set(name, { ...existing, ...policy })
    }
  }
}

// Create singleton instance
export const securityHardening = new SecurityHardening()

// React hooks for security features
import { useEffect, useState } from 'react'

export function useSecurityMonitoring() {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadEvents = async () => {
      try {
        const { data } = await supabase
          .from('security_events')
          .select('*')
          .order('timestamp', { ascending: false })
          .limit(50)

        setSecurityEvents(data || [])
      } catch (error) {
        errorLogger.logError('Failed to load security events', { error })
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [])

  return { securityEvents, loading }
}

export function useAuditLog(resourceType?: string, resourceId?: string) {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadAuditLogs = async () => {
      try {
        let query = supabase
          .from('audit_logs')
          .select('*')
          .order('timestamp', { ascending: false })
          .limit(100)

        if (resourceType) {
          query = query.eq('resource_type', resourceType)
        }

        if (resourceId) {
          query = query.eq('resource_id', resourceId)
        }

        const { data } = await query
        setAuditLogs(data || [])
      } catch (error) {
        errorLogger.logError('Failed to load audit logs', { error })
      } finally {
        setLoading(false)
      }
    }

    loadAuditLogs()
  }, [resourceType, resourceId])

  return { auditLogs, loading }
}