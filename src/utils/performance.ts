/**
 * Performance optimization utilities for Mini-ERP system
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react'

// Debounce hook for search and input optimization
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttle hook for scroll and resize events
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef(0)
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall.current >= delay) {
      lastCall.current = now
      return callback(...args)
    }
  }, [callback, delay]) as T
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  { threshold = 0.1, root = null, rootMargin = '0%' } = {}
) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
      },
      { threshold, root, rootMargin }
    )

    observer.observe(element)
    
    return () => observer.disconnect()
  }, [elementRef, threshold, root, rootMargin])

  return isVisible
}

// Virtual scrolling for large lists
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0)
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )
    
    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, items.length])

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex).map((item, index) => ({
      item,
      index: visibleRange.startIndex + index
    }))
  }, [items, visibleRange])

  const totalHeight = items.length * itemHeight

  return {
    visibleItems,
    totalHeight,
    scrollTop,
    setScrollTop,
    offsetY: visibleRange.startIndex * itemHeight
  }
}

// Memory optimization for large data sets
export function useMemoizedData<T>(
  data: T[],
  keyFn: (item: T) => string | number
) {
  return useMemo(() => {
    const dataMap = new Map<string | number, T>()
    data.forEach(item => {
      dataMap.set(keyFn(item), item)
    })
    return dataMap
  }, [data, keyFn])
}

// Optimized search with highlighting
export function useOptimizedSearch<T>(
  items: T[],
  searchTerm: string,
  searchFields: (keyof T)[],
  options: {
    caseSensitive?: boolean
    highlightMatches?: boolean
    maxResults?: number
  } = {}
) {
  const {
    caseSensitive = false,
    highlightMatches = false,
    maxResults = 100
  } = options

  return useMemo(() => {
    if (!searchTerm.trim()) return items.slice(0, maxResults)

    const normalizedSearch = caseSensitive 
      ? searchTerm 
      : searchTerm.toLowerCase()

    const results = items.filter(item => {
      return searchFields.some(field => {
        const fieldValue = item[field]
        if (typeof fieldValue !== 'string') return false
        
        const normalizedField = caseSensitive 
          ? fieldValue 
          : fieldValue.toLowerCase()
          
        return normalizedField.includes(normalizedSearch)
      })
    })

    if (highlightMatches) {
      return results.map(item => ({
        ...item,
        _highlighted: searchFields.reduce((acc, field) => {
          const fieldValue = item[field] as string
          if (typeof fieldValue === 'string') {
            const regex = new RegExp(
              `(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
              caseSensitive ? 'g' : 'gi'
            )
            acc[field] = fieldValue.replace(regex, '<mark>$1</mark>')
          }
          return acc
        }, {} as Record<string, string>)
      }))
    }

    return results.slice(0, maxResults)
  }, [items, searchTerm, searchFields, caseSensitive, highlightMatches, maxResults])
}

// Image lazy loading hook (returns props for img element)
export function useLazyImage(src?: string, placeholder?: string) {
  const imgRef = useRef<HTMLImageElement>(null)
  const isVisible = useIntersectionObserver(imgRef)
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)

  const imgProps = useMemo(() => ({
    ref: imgRef,
    src: isVisible ? (hasError ? placeholder : src) : placeholder,
    onLoad: () => setIsLoaded(true),
    onError: () => setHasError(true),
    loading: 'lazy' as const
  }), [isVisible, hasError, placeholder, src])

  return { imgProps, isLoaded, hasError, isVisible }
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderCount = useRef(0)
  const startTime = useRef(performance.now())

  useEffect(() => {
    renderCount.current++
    const endTime = performance.now()
    const renderTime = endTime - startTime.current
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`)
    }
    
    startTime.current = performance.now()
  })

  return { renderCount: renderCount.current }
}

// Bundle size analysis utility
export function getBundleInfo() {
  if (typeof window !== 'undefined' && window.performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      domInteractive: navigation.domInteractive - navigation.navigationStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
    }
  }
  return null
}

