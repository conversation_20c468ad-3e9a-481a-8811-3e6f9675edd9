/**
 * Utility functions for enhanced state persistence across workflow components
 */

export interface PersistenceOptions {
  key: string
  storage?: 'localStorage' | 'sessionStorage'
  expirationTime?: number // milliseconds
  compress?: boolean
  version?: string
}

export interface PersistedState<T> {
  data: T
  timestamp: number
  version: string
  expiresAt?: number
}

/**
 * Save state to local storage with expiration and versioning
 */
export function saveState<T>(data: T, options: PersistenceOptions): boolean {
  try {
    const storage = options.storage === 'sessionStorage' ? sessionStorage : localStorage
    
    const persistedState: PersistedState<T> = {
      data,
      timestamp: Date.now(),
      version: options.version || '1.0.0',
      expiresAt: options.expirationTime ? Date.now() + options.expirationTime : undefined
    }

    let serializedData = JSON.stringify(persistedState)
    
    // Simple compression for large states
    if (options.compress && serializedData.length > 10000) {
      // Note: In a real implementation, you might use a compression library like LZ-string
      console.log('State size is large, consider implementing compression for', options.key)
    }

    storage.setItem(options.key, serializedData)
    
    console.log(`💾 State persisted: ${options.key} (${(serializedData.length / 1024).toFixed(2)}KB)`)
    return true
  } catch (error) {
    console.error(`Failed to save state for ${options.key}:`, error)
    return false
  }
}

/**
 * Load state from local storage with expiration and version checking
 */
export function loadState<T>(options: PersistenceOptions): T | null {
  try {
    const storage = options.storage === 'sessionStorage' ? sessionStorage : localStorage
    const serializedData = storage.getItem(options.key)
    
    if (!serializedData) {
      return null
    }

    const persistedState: PersistedState<T> = JSON.parse(serializedData)
    
    // Check expiration
    if (persistedState.expiresAt && Date.now() > persistedState.expiresAt) {
      console.log(`⏰ State expired for ${options.key}, removing`)
      removeState(options)
      return null
    }

    // Check version compatibility
    if (options.version && persistedState.version !== options.version) {
      console.log(`🔄 State version mismatch for ${options.key}, removing old version`)
      removeState(options)
      return null
    }

    console.log(`📥 State loaded: ${options.key} (age: ${Math.round((Date.now() - persistedState.timestamp) / 1000)}s)`)
    return persistedState.data
  } catch (error) {
    console.error(`Failed to load state for ${options.key}:`, error)
    removeState(options) // Clean up corrupted state
    return null
  }
}

/**
 * Remove state from storage
 */
export function removeState(options: PersistenceOptions): void {
  try {
    const storage = options.storage === 'sessionStorage' ? sessionStorage : localStorage
    storage.removeItem(options.key)
    console.log(`🗑️ State removed: ${options.key}`)
  } catch (error) {
    console.error(`Failed to remove state for ${options.key}:`, error)
  }
}

/**
 * Check if state exists and is valid
 */
export function hasValidState(options: PersistenceOptions): boolean {
  const state = loadState(options)
  return state !== null
}

/**
 * Get state metadata without loading the full data
 */
export function getStateMetadata(options: PersistenceOptions): {
  exists: boolean
  timestamp?: number
  version?: string
  expiresAt?: number
  size?: number
} {
  try {
    const storage = options.storage === 'sessionStorage' ? sessionStorage : localStorage
    const serializedData = storage.getItem(options.key)
    
    if (!serializedData) {
      return { exists: false }
    }

    const persistedState: PersistedState<any> = JSON.parse(serializedData)
    
    return {
      exists: true,
      timestamp: persistedState.timestamp,
      version: persistedState.version,
      expiresAt: persistedState.expiresAt,
      size: serializedData.length
    }
  } catch (error) {
    console.error(`Failed to get state metadata for ${options.key}:`, error)
    return { exists: false }
  }
}

/**
 * Clean up expired states
 */
export function cleanupExpiredStates(keyPrefix: string = 'workflow-'): void {
  try {
    const now = Date.now()
    const keysToRemove: string[] = []

    // Check localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (!key || !key.startsWith(keyPrefix)) continue

      try {
        const serializedData = localStorage.getItem(key)
        if (!serializedData) continue

        const persistedState: PersistedState<any> = JSON.parse(serializedData)
        if (persistedState.expiresAt && now > persistedState.expiresAt) {
          keysToRemove.push(key)
        }
      } catch {
        // Corrupted data, mark for removal
        keysToRemove.push(key)
      }
    }

    // Remove expired/corrupted states
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    if (keysToRemove.length > 0) {
      console.log(`🧹 Cleaned up ${keysToRemove.length} expired states`)
    }
  } catch (error) {
    console.error('Failed to cleanup expired states:', error)
  }
}

/**
 * Hook for automatic state persistence
 */
export function usePersistentState<T>(
  initialState: T,
  options: PersistenceOptions
): [T, (state: T) => void] {
  // Load initial state
  const loadedState = loadState<T>(options)
  const currentState = loadedState !== null ? loadedState : initialState

  const setState = (newState: T) => {
    saveState(newState, options)
  }

  return [currentState, setState]
}

/**
 * Initialize state persistence system
 */
export function initializeStatePersistence(): void {
  console.log('🚀 Initializing state persistence system')
  
  // Clean up expired states on initialization
  cleanupExpiredStates()

  // Set up periodic cleanup (every 5 minutes)
  setInterval(() => {
    cleanupExpiredStates()
  }, 5 * 60 * 1000)

  // Listen for storage events to sync across tabs
  window.addEventListener('storage', (event) => {
    if (event.key?.startsWith('workflow-')) {
      console.log('🔄 Cross-tab state sync detected:', event.key)
      // Custom event for components to listen to
      window.dispatchEvent(new CustomEvent('workflow-state-sync', {
        detail: { key: event.key, newValue: event.newValue, oldValue: event.oldValue }
      }))
    }
  })

  console.log('✅ State persistence system initialized')
}