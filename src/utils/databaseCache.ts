/**
 * Database caching utilities for Mini-ERP system
 */

interface CacheEntry<T> {
  data: T
  timestamp: number
  expiresAt: number
  key: string
}

interface CacheConfig {
  defaultTTL: number // milliseconds
  maxSize: number
  persistToLocalStorage: boolean
  keyPrefix: string
}

const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 1000,
  persistToLocalStorage: true,
  keyPrefix: 'mini-erp-cache'
}

export class DatabaseCache {
  private cache = new Map<string, CacheEntry<any>>()
  private config: CacheConfig
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    evictions: 0
  }

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.loadFromLocalStorage()
    this.setupPeriodicCleanup()
  }

  private generateKey(table: string, query: string, params?: any): string {
    const paramString = params ? JSON.stringify(params) : ''
    return `${this.config.keyPrefix}:${table}:${btoa(query + paramString)}`
  }

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() > entry.expiresAt
  }

  private evictLRU(): void {
    let oldestEntry: { key: string; timestamp: number } | null = null
    
    for (const [key, entry] of this.cache.entries()) {
      if (!oldestEntry || entry.timestamp < oldestEntry.timestamp) {
        oldestEntry = { key, timestamp: entry.timestamp }
      }
    }
    
    if (oldestEntry) {
      this.cache.delete(oldestEntry.key)
      this.stats.evictions++
    }
  }

  private loadFromLocalStorage(): void {
    if (!this.config.persistToLocalStorage) return

    try {
      const stored = localStorage.getItem(`${this.config.keyPrefix}-data`)
      if (stored) {
        const entries = JSON.parse(stored)
        for (const entry of entries) {
          if (!this.isExpired(entry)) {
            this.cache.set(entry.key, entry)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error)
    }
  }

  private saveToLocalStorage(): void {
    if (!this.config.persistToLocalStorage) return

    try {
      const entries = Array.from(this.cache.values())
      localStorage.setItem(
        `${this.config.keyPrefix}-data`,
        JSON.stringify(entries)
      )
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error)
    }
  }

  private setupPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanup()
    }, 60000) // Cleanup every minute
  }

  private cleanup(): void {
    const expiredKeys: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key)
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key))
    
    if (expiredKeys.length > 0) {
      this.saveToLocalStorage()
    }
  }

  public get<T>(
    table: string,
    query: string,
    params?: any
  ): T | null {
    const key = this.generateKey(table, query, params)
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }
    
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.stats.misses++
      return null
    }
    
    // Update timestamp for LRU
    entry.timestamp = Date.now()
    this.stats.hits++
    
    return entry.data
  }

  public set<T>(
    table: string,
    query: string,
    data: T,
    params?: any,
    ttl?: number
  ): void {
    const key = this.generateKey(table, query, params)
    const now = Date.now()
    const expiresAt = now + (ttl || this.config.defaultTTL)
    
    // Evict if at capacity
    if (this.cache.size >= this.config.maxSize) {
      this.evictLRU()
    }
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt,
      key
    }
    
    this.cache.set(key, entry)
    this.stats.sets++
    
    // Periodically save to localStorage
    if (this.stats.sets % 10 === 0) {
      this.saveToLocalStorage()
    }
  }

  public invalidate(table: string, pattern?: string): number {
    let removed = 0
    const keysToRemove: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      const keyParts = key.split(':')
      const entryTable = keyParts[1]
      
      if (entryTable === table) {
        if (!pattern || key.includes(pattern)) {
          keysToRemove.push(key)
        }
      }
    }
    
    keysToRemove.forEach(key => {
      this.cache.delete(key)
      removed++
    })
    
    if (removed > 0) {
      this.saveToLocalStorage()
    }
    
    return removed
  }

  public clear(): void {
    this.cache.clear()
    this.stats = { hits: 0, misses: 0, sets: 0, evictions: 0 }
    
    if (this.config.persistToLocalStorage) {
      localStorage.removeItem(`${this.config.keyPrefix}-data`)
    }
  }

  public getStats(): typeof this.stats & { 
    size: number
    hitRate: number
    memoryUsage: string
  } {
    const total = this.stats.hits + this.stats.misses
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
    
    // Estimate memory usage
    const estimatedSize = Array.from(this.cache.values())
      .reduce((size, entry) => {
        return size + JSON.stringify(entry.data).length
      }, 0)
    
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: Number(hitRate.toFixed(2)),
      memoryUsage: `${(estimatedSize / 1024).toFixed(2)} KB`
    }
  }
}

// Create singleton cache instances for different use cases
export const queryCache = new DatabaseCache({
  keyPrefix: 'query-cache',
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 500
})

export const metadataCache = new DatabaseCache({
  keyPrefix: 'metadata-cache',
  defaultTTL: 30 * 60 * 1000, // 30 minutes
  maxSize: 100
})

export const userCache = new DatabaseCache({
  keyPrefix: 'user-cache',
  defaultTTL: 15 * 60 * 1000, // 15 minutes
  maxSize: 50
})

// Cache invalidation strategies
export const CacheInvalidationStrategies = {
  // Invalidate cache when orders are modified
  onOrderChange: (orderId?: string) => {
    queryCache.invalidate('order_lines')
    queryCache.invalidate('order_line_quantities')
    if (orderId) {
      queryCache.invalidate('ct_numbers', orderId)
    }
  },
  
  // Invalidate cache when CT numbers are assigned
  onCTAssignment: (orderLineId: string) => {
    queryCache.invalidate('ct_numbers')
    queryCache.invalidate('order_lines', orderLineId)
    queryCache.invalidate('order_line_quantities', orderLineId)
  },
  
  // Invalidate cache when quantities change
  onQuantityChange: (orderLineId: string) => {
    queryCache.invalidate('order_line_quantities', orderLineId)
    queryCache.invalidate('quantity_logs', orderLineId)
  },
  
  // Invalidate user-related cache
  onUserChange: (userId?: string) => {
    userCache.invalidate('users')
    metadataCache.invalidate('roles')
    if (userId) {
      userCache.invalidate('users', userId)
    }
  },
  
  // Invalidate all caches (use sparingly)
  invalidateAll: () => {
    queryCache.clear()
    metadataCache.clear()
    userCache.clear()
  }
}

// React hook for cached queries
import { useCallback, useEffect, useState } from 'react'

export function useCachedQuery<T>(
  table: string,
  query: string,
  queryFn: () => Promise<T>,
  params?: any,
  ttl?: number,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const executeQuery = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Try cache first
      const cachedData = queryCache.get<T>(table, query, params)
      if (cachedData) {
        setData(cachedData)
        setLoading(false)
        return
      }
      
      // Execute query
      const result = await queryFn()
      
      // Cache the result
      queryCache.set(table, query, result, params, ttl)
      
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)))
    } finally {
      setLoading(false)
    }
  }, [table, query, queryFn, params, ttl, ...dependencies])

  useEffect(() => {
    executeQuery()
  }, [executeQuery])

  const refresh = useCallback(() => {
    // Invalidate cache and refetch
    queryCache.invalidate(table, query)
    executeQuery()
  }, [table, query, executeQuery])

  return { data, loading, error, refresh }
}

// Cached versions of common database operations
export const CachedOperations = {
  // Get customer list with caching
  getCustomers: async () => {
    const cached = metadataCache.get<any[]>('customers', 'list')
    if (cached) return cached
    
    // This would be replaced with actual Supabase query
    const { supabase } = await import('@/lib/supabase')
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('name')
    
    if (error) throw error
    
    metadataCache.set('customers', 'list', data)
    return data
  },
  
  // Get product categories with caching
  getCategories: async () => {
    const cached = metadataCache.get<any[]>('product_categories', 'list')
    if (cached) return cached
    
    const { supabase } = await import('@/lib/supabase')
    const { data, error } = await supabase
      .from('product_categories')
      .select('*')
      .order('name')
    
    if (error) throw error
    
    metadataCache.set('product_categories', 'list', data)
    return data
  },
  
  // Get user roles with caching
  getRoles: async () => {
    const cached = metadataCache.get<any[]>('roles', 'list')
    if (cached) return cached
    
    const { supabase } = await import('@/lib/supabase')
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .order('name')
    
    if (error) throw error
    
    metadataCache.set('roles', 'list', data)
    return data
  }
}

// Export cache monitoring utilities
export const CacheMonitoring = {
  getOverallStats: () => {
    return {
      queryCache: queryCache.getStats(),
      metadataCache: metadataCache.getStats(),
      userCache: userCache.getStats()
    }
  },
  
  exportCacheData: () => {
    return {
      timestamp: new Date().toISOString(),
      stats: CacheMonitoring.getOverallStats(),
      config: {
        queryCache: DEFAULT_CONFIG,
        metadataCache: DEFAULT_CONFIG,
        userCache: DEFAULT_CONFIG
      }
    }
  }
}