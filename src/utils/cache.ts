/**
 * Caching utilities for performance optimization
 */

// Memory cache with TTL
class MemoryCache<T> {
  private cache = new Map<string, { data: T; expires: number }>()
  private defaultTTL: number

  constructor(defaultTTLMs: number = 5 * 60 * 1000) { // 5 minutes default
    this.defaultTTL = defaultTTLMs
  }

  set(key: string, data: T, ttlMs?: number): void {
    const expires = Date.now() + (ttlMs ?? this.defaultTTL)
    this.cache.set(key, { data, expires })
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() > entry.expires) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expires) {
        this.cache.delete(key)
      }
    }
  }

  size(): number {
    return this.cache.size
  }
}

// Singleton cache instances
export const orderCache = new MemoryCache<any>(10 * 60 * 1000) // 10 minutes for orders
export const userCache = new MemoryCache<any>(30 * 60 * 1000) // 30 minutes for users
export const systemCache = new MemoryCache<any>(60 * 60 * 1000) // 1 hour for system data

// Auto cleanup every 5 minutes
setInterval(() => {
  orderCache.cleanup()
  userCache.cleanup()
  systemCache.cleanup()
}, 5 * 60 * 1000)

// React Query cache keys
export const cacheKeys = {
  orders: {
    all: ['orders'] as const,
    lists: () => [...cacheKeys.orders.all, 'list'] as const,
    list: (filters: any) => [...cacheKeys.orders.lists(), filters] as const,
    details: () => [...cacheKeys.orders.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.orders.details(), id] as const,
  },
  users: {
    all: ['users'] as const,
    lists: () => [...cacheKeys.users.all, 'list'] as const,
    list: (filters: any) => [...cacheKeys.users.lists(), filters] as const,
    details: () => [...cacheKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.users.details(), id] as const,
  },
  quantities: {
    all: ['quantities'] as const,
    byOrder: (orderId: string) => [...cacheKeys.quantities.all, orderId] as const,
  },
  ctNumbers: {
    all: ['ctNumbers'] as const,
    validation: (ctNumber: string) => [...cacheKeys.ctNumbers.all, 'validation', ctNumber] as const,
  },
  templates: {
    all: ['templates'] as const,
    labels: () => [...cacheKeys.templates.all, 'labels'] as const,
  },
  workflows: {
    all: ['workflows'] as const,
    kitting: () => [...cacheKeys.workflows.all, 'kitting'] as const,
    qc: () => [...cacheKeys.workflows.all, 'qc'] as const,
    invoicing: () => [...cacheKeys.workflows.all, 'invoicing'] as const,
  }
}

// Cache invalidation utilities
export const invalidateCache = {
  orders: (queryClient: any) => {
    queryClient.invalidateQueries({ queryKey: cacheKeys.orders.all })
    orderCache.clear()
  },
  users: (queryClient: any) => {
    queryClient.invalidateQueries({ queryKey: cacheKeys.users.all })
    userCache.clear()
  },
  quantities: (queryClient: any, orderId?: string) => {
    if (orderId) {
      queryClient.invalidateQueries({ queryKey: cacheKeys.quantities.byOrder(orderId) })
    } else {
      queryClient.invalidateQueries({ queryKey: cacheKeys.quantities.all })
    }
  },
  workflows: (queryClient: any, workflowType?: string) => {
    if (workflowType) {
      queryClient.invalidateQueries({ queryKey: [...cacheKeys.workflows.all, workflowType] })
    } else {
      queryClient.invalidateQueries({ queryKey: cacheKeys.workflows.all })
    }
  }
}

// Local storage cache with compression
export class PersistentCache {
  private prefix: string

  constructor(prefix: string = 'minierp_cache_') {
    this.prefix = prefix
  }

  set(key: string, data: any, expiresInMs: number = 24 * 60 * 60 * 1000): void {
    try {
      const item = {
        data,
        expires: Date.now() + expiresInMs,
        timestamp: Date.now()
      }
      localStorage.setItem(this.prefix + key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to cache data:', error)
    }
  }

  get<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return null

      const parsed = JSON.parse(item)
      
      if (Date.now() > parsed.expires) {
        localStorage.removeItem(this.prefix + key)
        return null
      }

      return parsed.data
    } catch (error) {
      console.warn('Failed to retrieve cached data:', error)
      return null
    }
  }

  delete(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }

  clear(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key)
      }
    })
  }

  cleanup(): void {
    const keys = Object.keys(localStorage)
    const now = Date.now()
    
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}')
          if (item.expires && now > item.expires) {
            localStorage.removeItem(key)
          }
        } catch {
          localStorage.removeItem(key)
        }
      }
    })
  }
}

export const persistentCache = new PersistentCache()

// Cleanup persistent cache on app start
persistentCache.cleanup()