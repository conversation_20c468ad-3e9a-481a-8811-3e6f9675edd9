{"permissions": {"allow": ["Bash(npm create:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npm install:*)", "Bash(npx tailwindcss init:*)", "Bash(npx:*)", "WebFetch(domain:supabase.com)", "Bash(npm run dev:*)", "mcp__supabase__execute_sql", "Bash(find:*)", "Bash(npm run build:*)", "mcp__supabase__apply_migration", "Bash(git add:*)", "Bash(git push:*)", "Bash(git commit:*)", "Bash(git fsck:*)", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "Bash(grep:*)", "WebFetch(domain:www.pulsemcp.com)", "WebFetch(domain:docs.anthropic.com)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run test:gemma:*)", "Bash(git checkout:*)", "Bash(lsof:*)", "Bash(rg:*)", "mcp__supabase__search_docs", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_navigate", "Bash(TZ='Asia/Kolkata' date '+%d/%m/%y %I:%M %p')", "Bash(ls:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}