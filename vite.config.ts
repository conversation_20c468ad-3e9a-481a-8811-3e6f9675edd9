import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { VitePWA } from 'vite-plugin-pwa'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/qeozkzbjvvkgsvtitbny\.supabase\.co\/rest\/v1\/.*/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'supabase-api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 5 // 5 minutes
              },
              cacheKeyWillBeUsed: async ({ request }) => {
                return `${request.url}?t=${Math.floor(Date.now() / (1000 * 60))}`
              }
            }
          },
          {
            urlPattern: /^https:\/\/qeozkzbjvvkgsvtitbny\.supabase\.co\/storage\/v1\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'supabase-storage-cache',
              expiration: {
                maxEntries: 200,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              }
            }
          }
        ]
      },
      manifest: {
        name: 'Mini-ERP Order Management QC App',
        short_name: 'Mini-ERP',
        description: 'Warehouse scanning and quality control workflow system',
        theme_color: '#2563eb',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: '/icons/icon-192x192.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any maskable'
          },
          {
            src: '/icons/icon-512x512.png', 
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ],
        shortcuts: [
          {
            name: 'Kitting Station',
            short_name: 'Kitting',
            description: 'Scan-based kitting workstation',
            url: '/kitting/station',
            icons: [{ src: '/icons/shortcut-kitting.png', sizes: '96x96' }]
          },
          {
            name: 'QC Station',
            short_name: 'QC',
            description: 'Scan-based QC workstation',
            url: '/qc/station',
            icons: [{ src: '/icons/shortcut-qc.png', sizes: '96x96' }]
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: ['react', 'react-dom', '@supabase/supabase-js', 'fabric']
  },
  build: {
    // Optimize production builds
    target: 'es2020',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunk for large libraries
          vendor: ['react', 'react-dom', 'react-router-dom'],
          // Supabase chunk
          supabase: ['@supabase/supabase-js'],
          // UI components chunk
          ui: ['lucide-react'],
          // Fabric.js separate chunk (large)
          fabric: ['fabric']
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    reportCompressedSize: false,
    sourcemap: false
  },
  server: {
    port: 5173,
    strictPort: false,
    hmr: {
      overlay: false
    }
  }
})