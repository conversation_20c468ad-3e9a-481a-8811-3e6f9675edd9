{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"api/health.js": {"maxDuration": 10}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}