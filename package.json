{"name": "mini-erp-order-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test:gemma": "vitest src/tests/gemma --run", "test:gemma:watch": "vitest src/tests/gemma", "test:foundation": "vitest src/tests/gemma --run --testNamePattern=\"^(?!.*Chain)\"", "test:chains": "vitest src/tests/gemma --run --testNamePattern=\"Chain\"", "test:chains:watch": "vitest src/tests/gemma --testNamePattern=\"Chain\""}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@types/fabric": "^5.3.10", "@types/papaparse": "^5.3.16", "@types/qrcode": "^1.5.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "fabric": "^6.7.0", "jsbarcode": "^3.11.6", "lucide-react": "^0.513.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "pg": "^8.16.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "xlsx": "^0.18.5", "zod": "^3.25.55", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.21", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.4", "typescript": "~5.6.2", "vite": "^6.0.1", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.3", "workbox-window": "^7.3.0"}}