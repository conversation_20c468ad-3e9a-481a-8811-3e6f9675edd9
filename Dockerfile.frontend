# Multi-stage build for Mini-ERP Frontend
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build arguments for environment variables
ARG VITE_SUPABASE_URL
ARG VITE_SUPABASE_ANON_KEY
ARG VITE_N8N_WEBHOOK_URL
ARG VITE_MCP_API_URL
ARG VITE_MCP_API_KEY
ARG VITE_LOCATION
ARG VITE_APP_TITLE
ARG VITE_UID_PREFIX

# Set environment variables
ENV VITE_SUPABASE_URL=$VITE_SUPABASE_URL
ENV VITE_SUPABASE_ANON_KEY=$VITE_SUPABASE_ANON_KEY
ENV VITE_N8N_WEBHOOK_URL=$VITE_N8N_WEBHOOK_URL
ENV VITE_MCP_API_URL=$VITE_MCP_API_URL
ENV VITE_MCP_API_KEY=$VITE_MCP_API_KEY
ENV VITE_LOCATION=$VITE_LOCATION
ENV VITE_APP_TITLE=$VITE_APP_TITLE
ENV VITE_UID_PREFIX=$VITE_UID_PREFIX

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Install additional packages for health checks
RUN apk add --no-cache wget curl

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy health check script
COPY health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html
RUN chown -R nginx:nginx /var/cache/nginx

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD /usr/local/bin/health-check.sh

# Start nginx
CMD ["nginx", "-g", "daemon off;"]