#!/bin/sh

# Health check script for Mini-ERP Frontend

# Check if nginx is running
if ! pgrep nginx > /dev/null; then
    echo "ERROR: nginx is not running"
    exit 1
fi

# Check if the main page is accessible
if ! wget --quiet --tries=1 --spider http://localhost/; then
    echo "ERROR: Main page is not accessible"
    exit 1
fi

# Check if health endpoint responds
if ! wget --quiet --tries=1 --spider http://localhost/health; then
    echo "ERROR: Health endpoint is not responding"
    exit 1
fi

# Optional: Check if MCP server is accessible through proxy
if ! wget --quiet --tries=1 --spider http://localhost/api/mcp/health; then
    echo "WARNING: MCP server health check failed (non-critical)"
    # Don't exit with error for this check as MCP server might be starting
fi

echo "SUCCESS: All health checks passed"
exit 0