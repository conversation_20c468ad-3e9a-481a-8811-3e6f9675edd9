{"root": ["./src/app-minimal.tsx", "./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/debugcomponent.tsx", "./src/components/errorboundary.tsx", "./src/components/admin/approvalworkflowdashboard.tsx", "./src/components/admin/errormanagement.tsx", "./src/components/admin/invoicetemplatemanager.tsx", "./src/components/admin/mcpsettings.tsx", "./src/components/admin/quantityholdsmanager.tsx", "./src/components/admin/rolemanagement.tsx", "./src/components/admin/stresstestdashboard.tsx", "./src/components/admin/systemsettingsmanager.tsx", "./src/components/admin/templatemanager.tsx", "./src/components/admin/usermanagement.tsx", "./src/components/admin/userrolemanagement.tsx", "./src/components/admin/whatsappprovidersettings.tsx", "./src/components/admin/whatsappsettings.tsx", "./src/components/admin/workflowmonitoring.tsx", "./src/components/auth/loginform.tsx", "./src/components/kitting/ctnumbercard.tsx", "./src/components/kitting/kittingtaskcard.tsx", "./src/components/labeldesigner/labeldesigner.tsx", "./src/components/layout/dashboardlayout.tsx", "./src/components/layout/navigation.tsx", "./src/components/layout/navigationwithsubmenus.tsx", "./src/components/optimization/lazyroute.tsx", "./src/components/orders/ctapprovalmodal.tsx", "./src/components/orders/ctnumberdisplay.tsx", "./src/components/orders/ctnumbermodal.tsx", "./src/components/orders/faiuploadmodal.tsx", "./src/components/orders/globalpendingdisplay.tsx", "./src/components/orders/imageviewermodal.tsx", "./src/components/orders/masterimageuploadmodal.tsx", "./src/components/orders/ordercard.tsx", "./src/components/orders/ordercreationmodal.tsx", "./src/components/orders/orderimportmodal.tsx", "./src/components/orders/orderrowcard.tsx", "./src/components/orders/procurementquerymodal.tsx", "./src/components/orders/progressivequantitybar.tsx", "./src/components/orders/qcapprovalmodal.tsx", "./src/components/orders/quantityprogressbar.tsx", "./src/components/orders/quantitystatebadges.tsx", "./src/components/orders/quantitytransitionmodal.tsx", "./src/components/orders/quickprintmodal.tsx", "./src/components/orders/quicktransitionbuttons.tsx", "./src/components/orders/requestcancelmodal.tsx", "./src/components/orders/transferapprovalmodal.tsx", "./src/components/orders/updatestatusmodal.tsx", "./src/components/orders/viewhistorymodal.tsx", "./src/components/orders/whatsappmodal.tsx", "./src/components/scanning/cameracapture.tsx", "./src/components/scanning/scanningworkinterface.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/connection-status.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/loading.tsx", "./src/components/ui/mcp-connection-status.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/realtime-indicator.tsx", "./src/components/ui/select.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/textarea.tsx", "./src/hooks/useapprovalworkflow.ts", "./src/hooks/useauth.ts", "./src/hooks/usebarcodescanner.ts", "./src/hooks/usectauditlog.ts", "./src/hooks/usectnumbers.ts", "./src/hooks/usecreateorder.ts", "./src/hooks/usefai.ts", "./src/hooks/useinvoicetemplates.ts", "./src/hooks/useinvoicing.ts", "./src/hooks/usekittingqueue.ts", "./src/hooks/uselabelprinting.ts", "./src/hooks/usemcpprinting.ts", "./src/hooks/useoptimisticupdates.ts", "./src/hooks/useorderimport.ts", "./src/hooks/useorders.ts", "./src/hooks/useprinterconfig.ts", "./src/hooks/useqcqueue.ts", "./src/hooks/usequantityholds.ts", "./src/hooks/usequantitytracking.ts", "./src/hooks/usesystemsettings.ts", "./src/hooks/useusermanagement.ts", "./src/hooks/usewhatsapp.ts", "./src/hooks/useworkflowcoordinator.ts", "./src/hooks/useworkflowstate.ts", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/pages/dashboard.tsx", "./src/pages/invoicingdashboard.tsx", "./src/pages/kittingdashboard.tsx", "./src/pages/kittingworkstation.tsx", "./src/pages/labeldesignerpage.tsx", "./src/pages/orders.tsx", "./src/pages/qcdashboard.tsx", "./src/pages/qcworkstation.tsx", "./src/pages/settings.tsx", "./src/pages/printing/printqueuepage.tsx", "./src/pages/printing/quickprintpage.tsx", "./src/pages/printing/templatemanagerpage.tsx", "./src/providers/authprovider.tsx", "./src/providers/queryprovider.tsx", "./src/services/approvalworkflowmanager.ts", "./src/services/quantitytransitionservice.ts", "./src/services/whatsappprovidermanager.ts", "./src/services/workflowstatemanager.ts", "./src/services/whatsappproviders/metacloudapiprovider.ts", "./src/services/whatsappproviders/n8nevolutionprovider.ts", "./src/stores/authstore.ts", "./src/tests/gemma/chain-order-realtime.gemma.test.ts", "./src/tests/gemma/chain-permissions-workflow.gemma.test.ts", "./src/tests/gemma/chain-quantity-tracking-integration.gemma.test.ts", "./src/tests/gemma/chain-state-audit.gemma.test.ts", "./src/tests/gemma/chain-user-management-integration.gemma.test.ts", "./src/tests/gemma/chain-whatsapp-notifications.gemma.test.ts", "./src/tests/gemma/chain-workflow-coordination.gemma.test.ts", "./src/tests/gemma/ct-validation.gemma.test.ts", "./src/tests/gemma/database.gemma.test.ts", "./src/tests/gemma/date-validation.gemma.test.ts", "./src/tests/gemma/order-creation.gemma.test.ts", "./src/tests/gemma/quantity-basics.gemma.test.ts", "./src/types/fai.ts", "./src/types/index.ts", "./src/types/labeldesigner.ts", "./src/types/whatsapp.ts", "./src/types/whatsappprovider.ts", "./src/utils/authutils.ts", "./src/utils/cache.ts", "./src/utils/csvtemplate.ts", "./src/utils/ctnumbervalidation.ts", "./src/utils/databasecache.ts", "./src/utils/errorboundary.tsx", "./src/utils/errorlogger.ts", "./src/utils/errorrecovery.ts", "./src/utils/labelgeneration.ts", "./src/utils/mobileoptimization.ts", "./src/utils/monitoring.ts", "./src/utils/orderimportvalidation.ts", "./src/utils/performance.ts", "./src/utils/securityhardening.ts", "./src/utils/statepersistence.ts", "./src/utils/stresstesting.ts", "./src/utils/templateloader.ts", "./src/utils/whatsappnotifications.ts", "./src/utils/zplgenerator.ts"], "errors": true, "version": "5.6.3"}