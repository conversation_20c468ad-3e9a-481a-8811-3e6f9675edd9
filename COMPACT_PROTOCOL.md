# COMPACT Mode Context Recovery Protocol

## 🎯 PURPOSE
When <PERSON>'s context window reaches the 200k limit, the system automatically compresses ("compact" mode) the conversation history from 200k tokens down to approximately 5k tokens. This protocol ensures agents can recover detailed context and continue working effectively.

## 🚨 CRITICAL DETECTION
**When you see "compact" mentioned or suspect context compression has occurred:**
1. **IMMEDIATELY** read current project status
2. **NEVER** assume you have complete context
3. **ALWAYS** verify current implementation before making changes

## 📋 MANDATORY RECOVERY SEQUENCE

### Step 1: Read Current Project Status
```bash
# Always read these files first when context is compressed:
1. CLAUDE.md - Current project status and recent changes
2. docs/TODO.md - Current task priorities and completion status  
3. docs/DEVELOPMENT_STATUS.md - Implementation progress
4. FRONTEND_STATUS.md - Real vs placeholder functionality (when created)
```

### Step 2: Read Task-Specific Context
**For Frontend Development:**
- `src/pages/Orders.tsx` - Main order interface
- `src/components/orders/OrderCard.tsx` - Core UI component
- `src/hooks/useOrders.ts` - Data management hooks

**For Backend/Database:**
- `supabase/schema.sql` - Database structure
- `src/lib/supabase.ts` - Database connection

**For Workflows:**
- `docs/requirements/[specific-workflow].md` - Feature requirements
- Current implementation files for the specific workflow

**For Testing:**
- `src/tests/gemma/README.md` - Testing strategy
- Relevant test files for current feature

### Step 3: Verify Current Branch and Git Status
```bash
git branch          # Confirm on sprint-by-claude
git status          # Check uncommitted changes
git log --oneline -5 # Recent commits
```

## 🎯 TASK EXECUTION PROTOCOL

### Before Starting Any Task:
1. **Read CLAUDE.md sections relevant to current task**
2. **Check TODO.md for current priorities**
3. **Verify what's actually implemented vs documented**
4. **Check for any critical integration points (CT + Printing, etc.)**

### During Task Execution:
1. **Think deeply about complex tasks** - use CLAUDE.md guidance
2. **Never hallucinate code** - verify against existing implementation
3. **Read docs when in doubt** - don't assume functionality
4. **Test integration points** - especially CT assignment + printing workflow

### After Task Completion:
1. **Update relevant documentation** (CLAUDE.md, TODO.md, status files)
2. **Run and update GEMMA tests if applicable**
3. **Commit with comprehensive notes**
4. **Update todo list with completion status**

## ⚠️ CRITICAL WARNINGS

### NEVER DO:
- Assume complete context after compression
- Implement without reading current state
- Modify files without understanding existing logic
- Skip testing for integration changes
- Make breaking changes without verification

### ALWAYS DO:
- Read context recovery files first
- Verify current implementation before changes
- Test critical workflows (CT + Printing)
- Update documentation progressively
- Maintain GEMMA testing coverage

## 🔧 INTEGRATION CRITICAL POINTS

### CT Assignment + Printing Workflow
**CRITICAL**: These must work seamlessly together
- CT assignment should immediately trigger print option
- Print templates must integrate CT numbers correctly
- Both systems use MCP printing infrastructure
- Any changes require testing of complete workflow

### Real-time Updates
**CRITICAL**: All changes must propagate via Supabase realtime
- Order status changes
- Quantity transitions  
- CT assignments
- Workflow completions

## 📊 CONTEXT COMPRESSION INDICATORS

**You're likely in compact mode if:**
- Previous conversation seems truncated
- Missing recent implementation details
- Uncertain about current project status
- File contents don't match expectations

**Recovery Action:** Follow Step 1-3 sequence immediately before proceeding.

---

**Created**: January 10, 2025  
**Purpose**: Ensure effective agent operation during context compression  
**Critical**: Read this file whenever context seems incomplete