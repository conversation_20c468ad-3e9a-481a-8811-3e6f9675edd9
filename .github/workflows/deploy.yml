name: Deploy Mini-ERP to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # Test and build job
  test-and-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linter
      run: npm run lint --if-present
      continue-on-error: true
      
    - name: Run tests
      run: npm run test --if-present
      continue-on-error: true
      
    - name: Build application
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
        VITE_N8N_WEBHOOK_URL: ${{ secrets.VITE_N8N_WEBHOOK_URL }}
        VITE_MCP_API_URL: ${{ secrets.VITE_MCP_API_URL }}
        VITE_MCP_API_KEY: ${{ secrets.VITE_MCP_API_KEY }}
        VITE_LOCATION: ${{ secrets.VITE_LOCATION }}
        VITE_APP_TITLE: "Mini-ERP Order Management QC App"
        VITE_UID_PREFIX: ${{ secrets.VITE_UID_PREFIX }}
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: dist/
        retention-days: 30

  # Security scan job
  security-scan:
    runs-on: ubuntu-latest
    needs: test-and-build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level moderate
      continue-on-error: true
      
    - name: Run dependency check
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'dependency-check.sarif'
      continue-on-error: true

  # Deploy to staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test-and-build, security-scan]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: staging
      url: https://staging.minierp.internal
      
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: dist/
        
    - name: Deploy to staging server
      run: |
        echo "🚀 Deploying to staging environment"
        # Add your staging deployment commands here
        # Examples:
        # - rsync to staging server
        # - Upload to cloud storage (AWS S3, Azure Blob, GCS)
        # - Deploy to Netlify/Vercel
        # - Update Docker containers
        echo "✅ Staging deployment completed"

  # Deploy to production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: https://minierp.internal
      
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: dist/
        
    - name: Deploy to production server
      run: |
        echo "🚀 Deploying to production environment"
        # Add your production deployment commands here
        # Examples:
        # - rsync to production server
        # - Upload to cloud storage
        # - Deploy to Kubernetes cluster
        # - Update load balancer configuration
        echo "✅ Production deployment completed"
        
    - name: Notify deployment success
      run: |
        echo "📢 Sending deployment notifications"
        # Add notification commands here
        # Examples:
        # - Slack webhook notification
        # - Email notification
        # - WhatsApp notification via N8N
        echo "✅ Notifications sent"

  # Health check job
  post-deploy-health-check:
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Wait for deployment to settle
      run: sleep 30
      
    - name: Health check - Basic connectivity
      run: |
        echo "🏥 Running health checks"
        # Add health check commands here
        # curl -f https://minierp.internal/health || exit 1
        echo "✅ Basic connectivity check passed"
        
    - name: Health check - Database connectivity
      run: |
        echo "🗄️ Checking database connectivity"
        # Add database health check
        echo "✅ Database connectivity check passed"
        
    - name: Health check - MCP Server connectivity
      run: |
        echo "🖨️ Checking MCP server connectivity"
        # Add MCP server health check
        echo "✅ MCP server connectivity check passed"
        
    - name: Performance test
      run: |
        echo "⚡ Running performance tests"
        # Add basic performance tests
        # artillery quick --count 10 --num 5 https://minierp.internal
        echo "✅ Performance tests passed"