-- Label Templates Database Schema
-- This schema supports the Label Designer template management system

-- Create label_templates table
CREATE TABLE IF NOT EXISTS label_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL CHECK (category IN ('shipping', 'warning', 'priority', 'qc', 'inspection', 'testing', 'returns', 'ct_labels', 'custom')),
    template_type TEXT NOT NULL CHECK (template_type IN ('quick_print', 'ct_label')),
    label_size JSONB NOT NULL DEFAULT '{"width": 4, "height": 6, "unit": "inch"}',
    zpl_template TEXT,
    canvas_data JSONB,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_label_templates_category ON label_templates(category);
CREATE INDEX IF NOT EXISTS idx_label_templates_type ON label_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_label_templates_active ON label_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_label_templates_created_by ON label_templates(created_by);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_label_templates_updated_at 
    BEFORE UPDATE ON label_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- RLS (Row Level Security) Policies
ALTER TABLE label_templates ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to read templates
CREATE POLICY "Users can view all label templates" ON label_templates
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow users to create templates
CREATE POLICY "Users can create label templates" ON label_templates
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow users to update their own templates or if they have admin role
CREATE POLICY "Users can update their own templates" ON label_templates
    FOR UPDATE USING (
        auth.uid() = created_by OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('director', 'admin')
        )
    );

-- Allow admins to delete templates
CREATE POLICY "Admins can delete templates" ON label_templates
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('director', 'admin')
        )
    );

-- Insert some sample templates to test with
INSERT INTO label_templates (name, description, category, template_type, label_size, zpl_template, is_active, created_by) VALUES
(
    'HP External Shipping', 
    'Standard external shipping label for HP orders with handling instructions',
    'shipping',
    'quick_print',
    '{"width": 4, "height": 6, "unit": "inch"}',
    '^XA
^FO50,50^A0N,40,40^FDHP EXTERNAL SHIPMENT^FS
^FO50,120^A0N,30,30^FDHandle with Care^FS
^FO50,170^A0N,25,25^FDESD Sensitive Component^FS
^FO50,220^A0N,30,30^FDDate: {{DATE}}^FS
^FO50,270^A0N,25,25^FDShip To: {{CUSTOMER}}^FS
^FO50,320^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^XZ',
    true,
    (SELECT id FROM auth.users LIMIT 1)
),
(
    'Fragile Warning Label',
    'Warning label for fragile and sensitive components',
    'warning', 
    'quick_print',
    '{"width": 4, "height": 6, "unit": "inch"}',
    '^XA
^FO200,50^A0N,50,50^FDFRAGILE^FS
^FO100,130^A0N,35,35^FDHandle with Care^FS
^FO50,200^A0N,25,25^FDThis Side Up^FS
^FO50,250^A0N,20,20^FDDate: {{DATE}}^FS
^FO50,290^A0N,20,20^FDOperator: {{USER}}^FS
^FO50,330^GB500,2,2^FS
^FO50,350^A0N,18,18^FDComponent: {{PART_NUMBER}}^FS
^XZ',
    true,
    (SELECT id FROM auth.users LIMIT 1)
),
(
    'Standard CT Label',
    'Default CT number label with order information and QR code',
    'ct_labels',
    'ct_label', 
    '{"width": 4, "height": 6, "unit": "inch"}',
    '^XA
^FO50,30^A0N,25,25^FDOrder: {{ORDER_UID}}^FS
^FO50,65^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,95^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,125^A0N,30,30^FDCT: {{CT_NUMBER}}^FS
^FO50,170^A0N,16,16^FDQty: {{QUANTITY}} | Date: {{DATE}}^FS
^FO50,200^BQN,2,6^FDMA,{{CT_NUMBER}}^FS
^FO350,250^A0N,14,14^FDScan for Details^FS
^FO50,350^A0N,14,14^FDMini-ERP System^FS
^XZ',
    true,
    (SELECT id FROM auth.users LIMIT 1)
);

-- Grant permissions to authenticated users
GRANT ALL ON label_templates TO authenticated;
GRANT USAGE ON SEQUENCE label_templates_id_seq TO authenticated;