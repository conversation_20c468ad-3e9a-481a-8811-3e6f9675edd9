-- Fix real-time triggers for kitting dashboard updates
-- This ensures that when quantity transitions happen, all related tables are properly updated

-- First, ensure RLS is enabled on all relevant tables
ALTER TABLE order_line_quantities ENABLE ROW LEVEL SECURITY;
ALTER TABLE quantity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ct_numbers ENABLE ROW LEVEL SECURITY;

-- Create a function to notify changes across related tables
CREATE OR REPLACE FUNCTION notify_quantity_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the change for debugging
  RAISE NOTICE 'Quantity change detected: % -> %', OLD, NEW;
  
  -- Force a timestamp update on the order line to trigger subscriptions
  UPDATE order_lines 
  SET updated_at = NOW()
  WHERE id = COALESCE(NEW.order_line_id, OLD.order_line_id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to notify on quantity changes
DROP TRIGGER IF EXISTS notify_quantity_change_trigger ON order_line_quantities;
CREATE TRIGGER notify_quantity_change_trigger
  AFTER INSERT OR UPDATE OR DELETE ON order_line_quantities
  FOR EACH ROW
  EXECUTE FUNCTION notify_quantity_change();

-- Ensure the transition_quantity function properly handles all updates
CREATE OR REPLACE FUNCTION transition_quantity(
  p_order_line_id UUID,
  p_from_state TEXT,
  p_to_state TEXT,
  p_quantity INTEGER,
  p_reason TEXT DEFAULT NULL,
  p_rejection_reason TEXT DEFAULT NULL,
  p_ct_numbers TEXT[] DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_log_id UUID;
  v_current_quantity INTEGER;
  v_result JSON;
  v_from_column_name TEXT;
  v_to_column_name TEXT;
BEGIN
  -- Start transaction
  BEGIN
    -- Map state names to actual column names in the database
    v_from_column_name := CASE p_from_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement'
        WHEN 'pending_procurement' THEN 'pending_procurement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_kitting'
        WHEN 'on_hold_kitting' THEN 'on_hold_kitting'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_awaiting_qc'
        WHEN 'kitted_awaiting_qc' THEN 'kitted_awaiting_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_qc'
        WHEN 'on_hold_qc' THEN 'on_hold_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'qc_passed_ready_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'screening_qc_rejected' THEN 'qc_rejected'
        WHEN 'qc_rejected' THEN 'qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE NULL
    END;
    
    v_to_column_name := CASE p_to_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement'
        WHEN 'pending_procurement' THEN 'pending_procurement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_kitting'
        WHEN 'on_hold_kitting' THEN 'on_hold_kitting'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_awaiting_qc'
        WHEN 'kitted_awaiting_qc' THEN 'kitted_awaiting_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_qc'
        WHEN 'on_hold_qc' THEN 'on_hold_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'qc_passed_ready_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'screening_qc_rejected' THEN 'qc_rejected'
        WHEN 'qc_rejected' THEN 'qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE NULL
    END;
    
    -- Check if mapping was successful
    IF v_from_column_name IS NULL THEN
      RAISE EXCEPTION 'Invalid from_state: %', p_from_state;
    END IF;
    
    IF v_to_column_name IS NULL THEN
      RAISE EXCEPTION 'Invalid to_state: %', p_to_state;
    END IF;
    
    -- Validate that we have enough quantity in the from_state
    EXECUTE format('SELECT %I FROM order_line_quantities WHERE order_line_id = $1', v_from_column_name)
    INTO v_current_quantity
    USING p_order_line_id;
    
    -- Handle NULL as 0
    v_current_quantity := COALESCE(v_current_quantity, 0);
    
    IF v_current_quantity < p_quantity THEN
      RAISE EXCEPTION 'Insufficient quantity in state %: has %, requested %', 
        p_from_state, v_current_quantity, p_quantity;
    END IF;
    
    -- Update quantities: subtract from source, add to destination
    EXECUTE format('UPDATE order_line_quantities SET %I = %I - $1, %I = %I + $1, updated_at = NOW(), last_updated_by = $3 WHERE order_line_id = $2',
      v_from_column_name, v_from_column_name, v_to_column_name, v_to_column_name)
    USING p_quantity, p_order_line_id, p_user_id;
    
    -- Create audit log entry
    INSERT INTO quantity_logs (
      order_line_id, from_state, to_state, quantity_moved, reason_text, 
      rejection_reason, associated_ct_numbers, metadata, user_id
    ) VALUES (
      p_order_line_id, p_from_state, p_to_state, p_quantity, p_reason,
      p_rejection_reason, p_ct_numbers, p_metadata, p_user_id
    ) RETURNING id INTO v_log_id;
    
    -- Update CT numbers if provided
    IF p_ct_numbers IS NOT NULL AND array_length(p_ct_numbers, 1) > 0 THEN
      UPDATE ct_numbers 
      SET current_state = p_to_state,
          updated_at = NOW()
      WHERE ct_number = ANY(p_ct_numbers);
    END IF;
    
    -- Force update on order line to trigger subscriptions
    UPDATE order_lines 
    SET updated_at = NOW()
    WHERE id = p_order_line_id;
    
    -- Return success result
    v_result := json_build_object(
      'success', true,
      'log_id', v_log_id,
      'message', 'Quantity transition completed successfully'
    );
    
    RETURN v_result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Return error details
    v_result := json_build_object(
      'success', false,
      'message', SQLERRM,
      'detail', SQLSTATE
    );
    RETURN v_result;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION transition_quantity TO authenticated;

-- Create index for better performance on quantity lookups
CREATE INDEX IF NOT EXISTS idx_order_line_quantities_states ON order_line_quantities 
  (awaiting_kitting_packing, in_kitting_packing, kitted_awaiting_qc);

-- Create index for CT number lookups
CREATE INDEX IF NOT EXISTS idx_ct_numbers_state ON ct_numbers (current_state);

-- Enable real-time for all relevant tables (if not already enabled)
ALTER PUBLICATION supabase_realtime ADD TABLE order_lines;
ALTER PUBLICATION supabase_realtime ADD TABLE order_line_quantities;
ALTER PUBLICATION supabase_realtime ADD TABLE quantity_logs;
ALTER PUBLICATION supabase_realtime ADD TABLE ct_numbers;

-- Add comment for documentation
COMMENT ON FUNCTION transition_quantity IS 'Handles quantity state transitions with proper validation, logging, and real-time updates';

-- Create trigger for CT number state changes to update order line quantities timestamp
CREATE OR REPLACE FUNCTION notify_ct_number_change()
RETURNS TRIGGER AS $$
BEGIN
  -- When CT number changes state, update the order line quantities timestamp
  -- This will trigger real-time subscriptions that listen to quantity changes
  IF (TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status) OR 
     (TG_OP = 'UPDATE' AND OLD.current_state IS DISTINCT FROM NEW.current_state) OR
     TG_OP = 'INSERT' OR TG_OP = 'DELETE' THEN
    
    -- Update the timestamp on order_line_quantities to trigger subscriptions
    UPDATE order_line_quantities 
    SET updated_at = NOW()
    WHERE order_line_id = COALESCE(NEW.order_line_id, OLD.order_line_id);
    
    -- Also update the order line itself
    UPDATE order_lines 
    SET updated_at = NOW()
    WHERE id = COALESCE(NEW.order_line_id, OLD.order_line_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on CT numbers table
DROP TRIGGER IF EXISTS notify_ct_number_change_trigger ON ct_numbers;
CREATE TRIGGER notify_ct_number_change_trigger
  AFTER INSERT OR UPDATE OR DELETE ON ct_numbers
  FOR EACH ROW
  EXECUTE FUNCTION notify_ct_number_change();

-- Add index for better performance on CT number lookups by order line
CREATE INDEX IF NOT EXISTS idx_ct_numbers_order_line ON ct_numbers (order_line_id, status);

-- Ensure ct_numbers table has proper columns
ALTER TABLE ct_numbers ADD COLUMN IF NOT EXISTS current_state TEXT;

-- Grant permissions
GRANT EXECUTE ON FUNCTION notify_ct_number_change TO authenticated;