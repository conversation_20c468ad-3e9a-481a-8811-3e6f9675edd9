-- Invoice Templates Schema
-- This schema defines the invoice_templates table that is referenced in the useInvoicing hook
-- but was missing from the database

-- Create invoice_templates table
CREATE TABLE IF NOT EXISTS invoice_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    customer_name VARCHAR(255),
    is_default BOOLEAN DEFAULT false,
    payment_terms TEXT DEFAULT 'Net 30',
    notes TEXT,
    header_content TEXT,
    footer_content TEXT,
    template_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_invoice_templates_customer ON invoice_templates(customer_name);
CREATE INDEX IF NOT EXISTS idx_invoice_templates_default ON invoice_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_invoice_templates_active ON invoice_templates(is_active);

-- Row Level Security (RLS) policies
ALTER TABLE invoice_templates ENABLE ROW LEVEL SECURITY;

-- Policy: Users with ACCESS_INVOICING can view all templates
CREATE POLICY "Users with invoicing access can view invoice templates"
ON invoice_templates FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid()
        AND (r.permissions->>'ACCESS_INVOICING')::boolean = true
    )
);

-- Policy: Users with CREATE_INVOICES can insert templates
CREATE POLICY "Users with invoice creation can insert templates"
ON invoice_templates FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid()
        AND (r.permissions->>'CREATE_INVOICES')::boolean = true
    )
);

-- Policy: Users with CREATE_INVOICES can update templates
CREATE POLICY "Users with invoice creation can update templates"
ON invoice_templates FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid()
        AND (r.permissions->>'CREATE_INVOICES')::boolean = true
    )
);

-- Policy: Admin users can delete templates
CREATE POLICY "Admin users can delete invoice templates"
ON invoice_templates FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid()
        AND (r.permissions->>'MANAGE_SYSTEM_SETTINGS')::boolean = true
    )
);

-- Insert default invoice templates
INSERT INTO invoice_templates (name, customer_name, is_default, payment_terms, notes, header_content, footer_content, template_data)
VALUES 
(
    'Standard Invoice Template',
    NULL,
    true,
    'Net 30',
    'Standard invoice template for all customers',
    '<div class="invoice-header"><h1>INVOICE</h1><div class="company-info"><strong>BPI Ltd.</strong><br>Address Line 1<br>Address Line 2<br>Phone: (*************</div></div>',
    '<div class="invoice-footer"><p>Thank you for your business!</p><p>Payment terms: {{payment_terms}}</p></div>',
    '{"showQuantity": true, "showUnitPrice": true, "showLineTotal": true, "showCTNumbers": true, "logoPosition": "top-left"}'
),
(
    'HP Customer Template',
    'HP Inc.',
    false,
    'Net 45',
    'Specialized template for HP customer orders with specific formatting requirements',
    '<div class="invoice-header hp-style"><h1>INVOICE - HP ORDER</h1><div class="company-info"><strong>BPI Ltd.</strong><br>HP Certified Supplier<br>Supplier ID: HP-BPI-2024</div></div>',
    '<div class="invoice-footer hp-footer"><p>HP Approved Supplier - Quality Guaranteed</p><p>Payment terms: {{payment_terms}}</p><p>For HP support: <EMAIL></p></div>',
    '{"showQuantity": true, "showUnitPrice": true, "showLineTotal": true, "showCTNumbers": true, "showPartMapping": true, "logoPosition": "top-center", "hpSpecific": true}'
),
(
    'Lenovo Customer Template',
    'Lenovo Group Ltd.',
    false,
    'Net 30',
    'Template optimized for Lenovo customer requirements',
    '<div class="invoice-header lenovo-style"><h1>INVOICE - LENOVO ORDER</h1><div class="company-info"><strong>BPI Ltd.</strong><br>Lenovo Partner<br>Partner Code: LNV-BPI-001</div></div>',
    '<div class="invoice-footer lenovo-footer"><p>Lenovo Certified Partner</p><p>Payment terms: {{payment_terms}}</p><p>Lenovo support line: <EMAIL></p></div>',
    '{"showQuantity": true, "showUnitPrice": true, "showLineTotal": true, "showCTNumbers": true, "groupByCategory": true, "logoPosition": "top-right"}'
)
ON CONFLICT DO NOTHING;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_invoice_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_invoice_templates_updated_at
    BEFORE UPDATE ON invoice_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_invoice_templates_updated_at();

-- Comments for documentation
COMMENT ON TABLE invoice_templates IS 'Store invoice templates for different customers and use cases';
COMMENT ON COLUMN invoice_templates.template_data IS 'JSON configuration for template display options and formatting';
COMMENT ON COLUMN invoice_templates.is_default IS 'Indicates if this is the default template to use when none specified';
COMMENT ON COLUMN invoice_templates.payment_terms IS 'Default payment terms for invoices using this template';