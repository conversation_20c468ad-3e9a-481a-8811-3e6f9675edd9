-- Function to ensure order_line_quantities record exists for an order
-- This initializes quantities if they don't exist yet

CREATE OR REPLACE FUNCTION ensure_order_quantities_initialized(p_order_line_id UUID)
RETURNS VOID AS $$
DECLARE
  v_exists BOOLEAN;
  v_order_quantity INTEGER;
BEGIN
  -- Check if quantities record exists
  SELECT EXISTS(
    SELECT 1 FROM order_line_quantities WHERE order_line_id = p_order_line_id
  ) INTO v_exists;
  
  IF NOT v_exists THEN
    -- Get the order quantity from order_lines
    SELECT order_quantity INTO v_order_quantity
    FROM order_lines
    WHERE id = p_order_line_id;
    
    -- Create quantities record with initial state
    INSERT INTO order_line_quantities (
      order_line_id,
      total_order_quantity,
      pending_procurement,
      awaiting_kitting_packing,
      updated_at
    ) VALUES (
      p_order_line_id,
      v_order_quantity,
      v_order_quantity,  -- Initially all in pending procurement
      0,  -- None in kitting yet
      NOW()
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION ensure_order_quantities_initialized TO authenticated;

-- Trigger to automatically initialize quantities when order is created
CREATE OR REPLACE FUNCTION auto_initialize_order_quantities()
RETURNS TRIGGER AS $$
BEGIN
  -- Initialize quantities for new order
  INSERT INTO order_line_quantities (
    order_line_id,
    total_order_quantity,
    pending_procurement,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.order_quantity,
    NEW.order_quantity,
    NOW()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS initialize_order_quantities_trigger ON order_lines;
CREATE TRIGGER initialize_order_quantities_trigger
  AFTER INSERT ON order_lines
  FOR EACH ROW
  EXECUTE FUNCTION auto_initialize_order_quantities();