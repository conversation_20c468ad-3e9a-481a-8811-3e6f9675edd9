-- Add the 'standard-ct-label' template referenced in environment configuration
-- This ensures the default template exists for the workflow integration

-- First check if the template exists, if not insert it
INSERT INTO label_templates (
    name, 
    description, 
    category, 
    template_type, 
    label_size, 
    zpl_template, 
    is_active
) 
SELECT 
    'Standard CT Label',
    'Default CT number label with order information and QR code for workflow integration',
    'ct_labels',
    'ct_label',
    '{"width": 4, "height": 6, "unit": "inch"}',
    '^XA
^FO50,30^A0N,25,25^FDOrder: {{ORDER_UID}}^FS
^FO50,65^A0N,20,20^FDPart: {{PART_NUMBER}}^FS
^FO50,95^A0N,18,18^FDCustomer: {{CUSTOMER}}^FS
^FO50,125^A0N,30,30^FDCT: {{CT_NUMBER}}^FS
^FO50,170^A0N,16,16^FDQty: {{QUANTITY}} | Date: {{DATE}}^FS
^FO50,200^BQN,2,6^FDMA,{{CT_NUMBER}}^FS
^FO350,250^A0N,14,14^FDScan for Details^FS
^FO50,350^A0N,14,14^FDMini-ERP System^FS
^XZ',
    true
WHERE NOT EXISTS (
    SELECT 1 FROM label_templates 
    WHERE name = 'Standard CT Label' 
    AND category = 'ct_labels'
);

-- Update the template id to match the environment variable reference
-- We need to ensure the template can be found by the name 'standard-ct-label'
-- So we'll add an alias or update existing template

-- If we need to reference by ID, we could add a configuration table
-- For now, let's ensure the template generation utility can find it by name

-- Optional: Add a simple configuration table for default templates
CREATE TABLE IF NOT EXISTS printing_defaults (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_name TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default template reference
INSERT INTO printing_defaults (setting_name, setting_value, description)
VALUES 
    ('default_ct_template', 'Standard CT Label', 'Default template name for CT assignment printing workflow'),
    ('auto_print_enabled', 'true', 'Enable automatic printing after CT assignment'),
    ('auto_advance_enabled', 'true', 'Enable automatic quantity stage advancement after successful printing'),
    ('print_quantity_mode', 'in_hand', 'Default print quantity calculation mode (in_hand, all_assigned, custom)')
ON CONFLICT (setting_name) DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- Grant permissions
GRANT ALL ON printing_defaults TO authenticated;