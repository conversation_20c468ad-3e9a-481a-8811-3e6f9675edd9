-- Database Performance Optimization for Mini-ERP
-- Run these optimizations to improve query performance

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Order Lines - Core table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_uid ON order_lines(uid);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_customer_id ON order_lines(customer_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_category_id ON order_lines(product_category_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_created_at ON order_lines(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_status ON order_lines(current_status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_part_number ON order_lines(part_number);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_customer_status ON order_lines(customer_id, current_status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_lines_status_created ON order_lines(current_status, created_at DESC);

-- CT Numbers - Critical for uniqueness checks
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ct_numbers_number ON ct_numbers(ct_number);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ct_numbers_order_line ON ct_numbers(order_line_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ct_numbers_assigned_date ON ct_numbers(assigned_date DESC);

-- Order Line Quantities - Heavy read table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantities_order_line ON order_line_quantities(order_line_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantities_status ON order_line_quantities(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantities_updated ON order_line_quantities(updated_at DESC);

-- Quantity Logs - Audit trail optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantity_logs_order_line ON quantity_logs(order_line_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantity_logs_timestamp ON quantity_logs(timestamp DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_quantity_logs_action ON quantity_logs(action);

-- Users and Authentication
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role ON users(role_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_location ON users(location);

-- FAI Documents and Images
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fai_documents_order_line ON fai_documents(order_line_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fai_images_document ON fai_images(fai_document_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_master_images_part ON master_images(part_number);

-- Label Templates and Print Jobs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_label_templates_category ON label_templates(category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_print_jobs_status ON print_jobs(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_print_jobs_created ON print_jobs(created_at DESC);

-- Approval Workflows
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflows_status ON approval_workflows(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflows_type ON approval_workflows(workflow_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workflows_created ON approval_workflows(created_at DESC);

-- =====================================================
-- MATERIALIZED VIEWS FOR COMPLEX QUERIES
-- =====================================================

-- Order Statistics View
CREATE MATERIALIZED VIEW IF NOT EXISTS order_stats_mv AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    customer_id,
    current_status,
    COUNT(*) as order_count,
    SUM(total_order_quantity) as total_quantity,
    AVG(unit_price) as avg_price
FROM order_lines 
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE_TRUNC('day', created_at), customer_id, current_status;

CREATE UNIQUE INDEX IF NOT EXISTS idx_order_stats_mv_unique 
ON order_stats_mv(date, customer_id, current_status);

-- Quantity Status Summary View
CREATE MATERIALIZED VIEW IF NOT EXISTS quantity_status_mv AS
SELECT 
    order_line_id,
    status,
    SUM(quantity) as total_quantity,
    MAX(updated_at) as last_updated
FROM order_line_quantities
GROUP BY order_line_id, status;

CREATE UNIQUE INDEX IF NOT EXISTS idx_quantity_status_mv_unique 
ON quantity_status_mv(order_line_id, status);

-- CT Assignment Summary View
CREATE MATERIALIZED VIEW IF NOT EXISTS ct_assignment_mv AS
SELECT 
    ol.customer_id,
    ol.product_category_id,
    COUNT(ct.id) as ct_count,
    COUNT(DISTINCT ol.id) as order_count,
    DATE_TRUNC('day', ct.assigned_date) as assignment_date
FROM order_lines ol
LEFT JOIN ct_numbers ct ON ol.id = ct.order_line_id
WHERE ct.assigned_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY ol.customer_id, ol.product_category_id, DATE_TRUNC('day', ct.assigned_date);

CREATE UNIQUE INDEX IF NOT EXISTS idx_ct_assignment_mv_unique 
ON ct_assignment_mv(customer_id, product_category_id, assignment_date);

-- =====================================================
-- FUNCTIONS FOR REFRESHING MATERIALIZED VIEWS
-- =====================================================

-- Function to refresh all materialized views
CREATE OR REPLACE FUNCTION refresh_all_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY order_stats_mv;
    REFRESH MATERIALIZED VIEW CONCURRENTLY quantity_status_mv;
    REFRESH MATERIALIZED VIEW CONCURRENTLY ct_assignment_mv;
    
    -- Log the refresh
    INSERT INTO system_logs (level, message, context, timestamp)
    VALUES ('info', 'Materialized views refreshed', '{"views": ["order_stats_mv", "quantity_status_mv", "ct_assignment_mv"]}', NOW());
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- QUERY OPTIMIZATION FUNCTIONS
-- =====================================================

-- Optimized function for getting order dashboard data
CREATE OR REPLACE FUNCTION get_order_dashboard_data(
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_customer_id UUID DEFAULT NULL,
    p_status TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    uid TEXT,
    customer_name TEXT,
    part_number TEXT,
    description TEXT,
    current_status TEXT,
    total_order_quantity INTEGER,
    unit_price DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE,
    ct_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ol.id,
        ol.uid,
        c.name as customer_name,
        ol.part_number,
        ol.description,
        ol.current_status,
        ol.total_order_quantity,
        ol.unit_price,
        ol.created_at,
        COUNT(ct.id)::INTEGER as ct_count
    FROM order_lines ol
    LEFT JOIN customers c ON ol.customer_id = c.id
    LEFT JOIN ct_numbers ct ON ol.id = ct.order_line_id
    WHERE 
        (p_customer_id IS NULL OR ol.customer_id = p_customer_id)
        AND (p_status IS NULL OR ol.current_status = p_status)
    GROUP BY ol.id, c.name
    ORDER BY ol.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Optimized function for CT number validation
CREATE OR REPLACE FUNCTION validate_ct_number_batch(
    p_ct_numbers TEXT[]
)
RETURNS TABLE (
    ct_number TEXT,
    is_valid BOOLEAN,
    existing_order_uid TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH input_numbers AS (
        SELECT unnest(p_ct_numbers) as ct_number
    )
    SELECT 
        i.ct_number,
        CASE 
            WHEN ct.ct_number IS NULL THEN TRUE 
            ELSE FALSE 
        END as is_valid,
        ol.uid as existing_order_uid
    FROM input_numbers i
    LEFT JOIN ct_numbers ct ON i.ct_number = ct.ct_number
    LEFT JOIN order_lines ol ON ct.order_line_id = ol.id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERFORMANCE MONITORING FUNCTIONS
-- =====================================================

-- Function to analyze slow queries
CREATE OR REPLACE FUNCTION analyze_slow_queries()
RETURNS TABLE (
    query TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    rows BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pg_stat_statements.query,
        pg_stat_statements.calls,
        pg_stat_statements.total_exec_time,
        pg_stat_statements.mean_exec_time,
        pg_stat_statements.rows
    FROM pg_stat_statements
    WHERE pg_stat_statements.mean_exec_time > 100 -- queries taking more than 100ms
    ORDER BY pg_stat_statements.mean_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CLEANUP AND MAINTENANCE
-- =====================================================

-- Function to clean up old logs
CREATE OR REPLACE FUNCTION cleanup_old_logs(
    p_days_to_keep INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM quantity_logs 
    WHERE timestamp < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    DELETE FROM system_logs 
    WHERE timestamp < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep;
    
    DELETE FROM print_jobs 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep
    AND status IN ('completed', 'failed');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- AUTOMATED MAINTENANCE SCHEDULE
-- =====================================================

-- Create a function to run daily maintenance
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS void AS $$
BEGIN
    -- Refresh materialized views
    PERFORM refresh_all_materialized_views();
    
    -- Clean up old logs (keep 30 days)
    PERFORM cleanup_old_logs(30);
    
    -- Update table statistics
    ANALYZE order_lines;
    ANALYZE ct_numbers;
    ANALYZE order_line_quantities;
    ANALYZE quantity_logs;
    
    -- Log maintenance completion
    INSERT INTO system_logs (level, message, context, timestamp)
    VALUES ('info', 'Daily maintenance completed', '{"maintenance_type": "daily"}', NOW());
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TABLE STATISTICS AND MONITORING
-- =====================================================

-- View for monitoring table sizes and performance
CREATE OR REPLACE VIEW table_performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals
FROM pg_stats 
WHERE schemaname = 'public'
    AND tablename IN ('order_lines', 'ct_numbers', 'order_line_quantities', 'quantity_logs')
ORDER BY tablename, attname;

-- View for monitoring index usage
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions for the functions
GRANT EXECUTE ON FUNCTION refresh_all_materialized_views() TO authenticated;
GRANT EXECUTE ON FUNCTION get_order_dashboard_data(INTEGER, INTEGER, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_ct_number_batch(TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION analyze_slow_queries() TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_logs(INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION daily_maintenance() TO service_role;

-- Grant read access to performance views
GRANT SELECT ON table_performance_stats TO authenticated;
GRANT SELECT ON index_usage_stats TO authenticated;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION refresh_all_materialized_views() IS 'Refreshes all materialized views for performance optimization';
COMMENT ON FUNCTION get_order_dashboard_data(INTEGER, INTEGER, UUID, TEXT) IS 'Optimized function for dashboard data retrieval with pagination';
COMMENT ON FUNCTION validate_ct_number_batch(TEXT[]) IS 'Batch validation of CT numbers for performance';
COMMENT ON FUNCTION daily_maintenance() IS 'Automated daily maintenance tasks including view refresh and cleanup';

-- =====================================================
-- SETUP COMPLETION
-- =====================================================

-- Log the completion of performance optimization setup
INSERT INTO system_logs (level, message, context, timestamp)
VALUES ('info', 'Database performance optimization completed', '{"indexes_created": true, "views_created": true, "functions_created": true}', NOW());

-- Display completion message
SELECT 'Database performance optimization setup completed successfully!' as message;