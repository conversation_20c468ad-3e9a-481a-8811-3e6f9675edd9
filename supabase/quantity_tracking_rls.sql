-- Row Level Security Policies for Quantity Tracking
-- Author: Implementation Plan - Phase 6
-- Date: June 8, 2025
-- Purpose: Secure access to enhanced quantity tracking tables

-- =============================================
-- RLS POLICIES: quantity_state_definitions
-- =============================================

-- Enable RLS
ALTER TABLE quantity_state_definitions ENABLE ROW LEVEL SECURITY;

-- Read access for all authenticated users (states are system-wide)
CREATE POLICY "quantity_state_definitions_read" ON quantity_state_definitions
    FOR SELECT TO authenticated
    USING (true);

-- Only admins can modify state definitions
CREATE POLICY "quantity_state_definitions_admin_modify" ON quantity_state_definitions
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (r.permissions->>'MANAGE_SYSTEM_SETTINGS')::boolean = true
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (r.permissions->>'MANAGE_SYSTEM_SETTINGS')::boolean = true
        )
    );

-- =============================================
-- RLS POLICIES: quantity_holds
-- =============================================

-- Enable RLS
ALTER TABLE quantity_holds ENABLE ROW LEVEL SECURITY;

-- Users can view holds they created or have permission to manage
CREATE POLICY "quantity_holds_read" ON quantity_holds
    FOR SELECT TO authenticated
    USING (
        held_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'MANAGE_HOLDS')::boolean = true OR
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'VIEW_ALL_ORDERS')::boolean = true
            )
        )
    );

-- Only authorized users can create holds
CREATE POLICY "quantity_holds_insert" ON quantity_holds
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'MANAGE_HOLDS')::boolean = true OR
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true
            )
        )
    );

-- Only creators or managers can update holds
CREATE POLICY "quantity_holds_update" ON quantity_holds
    FOR UPDATE TO authenticated
    USING (
        held_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'MANAGE_HOLDS')::boolean = true OR
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true
            )
        )
    );

-- =============================================
-- ENHANCED RLS POLICIES: quantity_logs  
-- =============================================

-- Enhanced policy for quantity logs with location-based access
CREATE POLICY "quantity_logs_read_enhanced" ON quantity_logs
    FOR SELECT TO authenticated
    USING (
        -- User can see logs they created
        created_by = auth.uid() OR
        -- User can see logs for their location or if they have global access
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'VIEW_ALL_ORDERS')::boolean = true OR
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'MANAGE_NPQC')::boolean = true
            )
        )
    );

-- Enhanced insert policy for quantity logs
CREATE POLICY "quantity_logs_insert_enhanced" ON quantity_logs
    FOR INSERT TO authenticated
    WITH CHECK (
        -- User must have permission to perform quantity operations
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'MANAGE_NPQC')::boolean = true OR
                (r.permissions->>'MANAGE_PROCUREMENT')::boolean = true OR
                (r.permissions->>'MANAGE_INVOICING')::boolean = true
            )
        )
    );

-- =============================================
-- ENHANCED RLS POLICIES: order_line_quantities
-- =============================================

-- Enhanced read policy for order line quantities
CREATE POLICY "order_line_quantities_read_enhanced" ON order_line_quantities
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'VIEW_ALL_ORDERS')::boolean = true OR
                (r.permissions->>'VIEW_OWN_ORDERS')::boolean = true OR
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'MANAGE_NPQC')::boolean = true
            )
        )
    );

-- Enhanced update policy for order line quantities
CREATE POLICY "order_line_quantities_update_enhanced" ON order_line_quantities
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE u.id = auth.uid() 
            AND (
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'MANAGE_NPQC')::boolean = true OR
                (r.permissions->>'MANAGE_PROCUREMENT')::boolean = true OR
                (r.permissions->>'MANAGE_INVOICING')::boolean = true
            )
        )
    );

-- =============================================
-- HELPER FUNCTIONS FOR RLS
-- =============================================

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid() 
        AND (r.permissions->>permission_name)::boolean = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's location preference
CREATE OR REPLACE FUNCTION get_user_location()
RETURNS TEXT AS $$
DECLARE
    v_location TEXT;
BEGIN
    -- Default to 'SB' if not specified
    -- This can be enhanced to read from user preferences
    RETURN 'SB';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can access specific location data
CREATE OR REPLACE FUNCTION user_can_access_location(location_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id = auth.uid() 
        AND (
            (r.permissions->>'VIEW_ALL_ORDERS')::boolean = true OR
            (location_name = 'SB' AND (
                (r.permissions->>'MANAGE_QC')::boolean = true OR
                (r.permissions->>'MANAGE_KITTING')::boolean = true OR
                (r.permissions->>'MANAGE_PROCUREMENT')::boolean = true
            )) OR
            (location_name = 'NP' AND (
                (r.permissions->>'MANAGE_NPQC')::boolean = true
            ))
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON TABLE quantity_state_definitions IS 'Centralized definition of all quantity states with transition rules and permissions';
COMMENT ON TABLE quantity_holds IS 'Structured tracking of quantity holds with reason and resolution management';

COMMENT ON COLUMN quantity_state_definitions.allowed_transitions IS 'JSON array of valid next states for workflow progression';
COMMENT ON COLUMN quantity_state_definitions.required_permissions IS 'JSON array of user permissions required to transition from this state';
COMMENT ON COLUMN quantity_state_definitions.stage_category IS 'Workflow stage grouping: procurement, kitting, qc, final';

COMMENT ON COLUMN quantity_holds.hold_stage IS 'Stage where hold occurred: kitting_packing or screening_qc';
COMMENT ON COLUMN quantity_holds.resolution_action IS 'How the hold was resolved: released, rejected, or escalated';

COMMENT ON FUNCTION validate_quantity_transition(TEXT, TEXT, TEXT[]) IS 'Validates if a quantity state transition is allowed for given user permissions';
COMMENT ON FUNCTION get_available_transitions(TEXT, TEXT[]) IS 'Returns list of valid next states for given current state and user permissions';
COMMENT ON FUNCTION transition_quantity(UUID, TEXT, TEXT, INTEGER, TEXT, TEXT, TEXT[], JSONB, UUID) IS 'Core function to move quantity between states with full audit trail';