-- Remove duplicate LONG columns from order_line_quantities table
-- These columns are empty and causing confusion
-- Data lives in SHORT columns only

-- IMPORTANT: Verify this is safe by checking all LONG columns have zero values
DO $$
DECLARE
    v_has_data BOOLEAN;
BEGIN
    -- Check if any LONG columns have non-zero data
    SELECT EXISTS (
        SELECT 1 FROM order_line_quantities
        WHERE pending_procurement_arrangement > 0
           OR awaiting_kitting_packing_new > 0
           OR in_kitting_packing_new > 0
           OR on_hold_at_kitting_packing > 0
           OR kitted_packed_awaiting_screening_qc > 0
           OR in_screening_qc_new > 0
           OR on_hold_at_screening_qc > 0
           OR screening_qc_passed_ready_for_invoice > 0
           OR screening_qc_rejected > 0
           OR invoiced_new > 0
           OR shipped_delivered_new > 0
           OR cancelled_new > 0
    ) INTO v_has_data;
    
    IF v_has_data THEN
        RAISE EXCEPTION 'STOP: Some LONG columns have data. Manual review required!';
    END IF;
END $$;

-- Drop the duplicate LONG columns
ALTER TABLE order_line_quantities 
    DROP COLUMN IF EXISTS pending_procurement_arrangement,
    DROP COLUMN IF EXISTS awaiting_kitting_packing_new,
    DROP COLUMN IF EXISTS in_kitting_packing_new,
    DROP COLUMN IF EXISTS on_hold_at_kitting_packing,
    DROP COLUMN IF EXISTS kitted_packed_awaiting_screening_qc,
    DROP COLUMN IF EXISTS in_screening_qc_new,
    DROP COLUMN IF EXISTS on_hold_at_screening_qc,
    DROP COLUMN IF EXISTS screening_qc_passed_ready_for_invoice,
    DROP COLUMN IF EXISTS screening_qc_rejected,
    DROP COLUMN IF EXISTS invoiced_new,
    DROP COLUMN IF EXISTS shipped_delivered_new,
    DROP COLUMN IF EXISTS cancelled_new;

-- The SHORT columns remain:
-- pending_procurement, awaiting_kitting_packing, in_kitting_packing, 
-- on_hold_kitting, kitted_awaiting_qc, in_screening_qc, on_hold_qc,
-- qc_passed_ready_invoice, qc_rejected, invoiced, shipped_delivered, cancelled