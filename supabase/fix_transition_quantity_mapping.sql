-- Fix the transition_quantity function to map state names to actual column names correctly
-- This addresses the mismatch between expected state names and actual database column names

CREATE OR REPLACE FUNCTION transition_quantity(
  p_order_line_id UUID,
  p_from_state TEXT,
  p_to_state TEXT,
  p_quantity INTEGER,
  p_reason TEXT DEFAULT NULL,
  p_rejection_reason TEXT DEFAULT NULL,
  p_ct_numbers TEXT[] DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_log_id UUID;
  v_current_quantity INTEGER;
  v_result JSON;
  v_from_column_name TEXT;
  v_to_column_name TEXT;
BEGIN
  -- Start transaction
  BEGIN
    -- Map state names to actual column names in the database
    v_from_column_name := CASE p_from_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement'
        WHEN 'pending_procurement' THEN 'pending_procurement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_kitting'
        WHEN 'on_hold_kitting' THEN 'on_hold_kitting'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_awaiting_qc'
        WHEN 'kitted_awaiting_qc' THEN 'kitted_awaiting_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_qc'
        WHEN 'on_hold_qc' THEN 'on_hold_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'qc_passed_ready_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'screening_qc_rejected' THEN 'qc_rejected'
        WHEN 'qc_rejected' THEN 'qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE NULL
    END;
    
    v_to_column_name := CASE p_to_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement'
        WHEN 'pending_procurement' THEN 'pending_procurement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_kitting'
        WHEN 'on_hold_kitting' THEN 'on_hold_kitting'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_awaiting_qc'
        WHEN 'kitted_awaiting_qc' THEN 'kitted_awaiting_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_qc'
        WHEN 'on_hold_qc' THEN 'on_hold_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'qc_passed_ready_invoice' THEN 'qc_passed_ready_invoice'
        WHEN 'screening_qc_rejected' THEN 'qc_rejected'
        WHEN 'qc_rejected' THEN 'qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE NULL
    END;
    
    -- Check if mapping was successful
    IF v_from_column_name IS NULL THEN
      RAISE EXCEPTION 'Invalid from_state: %', p_from_state;
    END IF;
    
    IF v_to_column_name IS NULL THEN
      RAISE EXCEPTION 'Invalid to_state: %', p_to_state;
    END IF;
    
    -- Validate that we have enough quantity in the from_state
    EXECUTE format('SELECT %I FROM order_line_quantities WHERE order_line_id = $1', v_from_column_name)
    INTO v_current_quantity
    USING p_order_line_id;
    
    -- Handle NULL as 0
    v_current_quantity := COALESCE(v_current_quantity, 0);
    
    IF v_current_quantity < p_quantity THEN
      RAISE EXCEPTION 'Insufficient quantity in state %: has %, requested %', 
        p_from_state, v_current_quantity, p_quantity;
    END IF;
    
    -- Update quantities: subtract from source, add to destination
    EXECUTE format('UPDATE order_line_quantities SET %I = %I - $1, %I = %I + $1, updated_at = NOW() WHERE order_line_id = $2',
      v_from_column_name, v_from_column_name, v_to_column_name, v_to_column_name)
    USING p_quantity, p_order_line_id;
    
    -- Create audit log entry
    INSERT INTO quantity_logs (
      order_line_id, from_state, to_state, quantity_moved, reason_text, 
      rejection_reason, associated_ct_numbers, metadata, user_id
    ) VALUES (
      p_order_line_id, p_from_state, p_to_state, p_quantity, p_reason,
      p_rejection_reason, p_ct_numbers, p_metadata, p_user_id
    ) RETURNING id INTO v_log_id;
    
    -- Return success result
    v_result := json_build_object(
      'success', true,
      'log_id', v_log_id,
      'message', 'Quantity transition completed successfully'
    );
    
    RETURN v_result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Return error details
    v_result := json_build_object(
      'success', false,
      'message', SQLERRM,
      'detail', SQLSTATE
    );
    RETURN v_result;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION transition_quantity TO authenticated;