-- Enhanced Quantity Tracking Schema
-- Author: Implementation Plan - Phase 6
-- Date: June 8, 2025
-- Purpose: Enhance existing quantity tracking with 12-state progressive system

-- =============================================
-- NEW: Quantity State Definitions Table
-- =============================================

CREATE TABLE IF NOT EXISTS quantity_state_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    state_name TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    sort_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    allowed_transitions TEXT[], -- Valid next states
    required_permissions TEXT[], -- Required user permissions
    stage_category TEXT NOT NULL CHECK (stage_category IN ('procurement', 'kitting', 'qc', 'final')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ENHANCED: Order Line Quantities (12 Progressive States)
-- =============================================

-- Add missing columns and rename existing ones for consistency
ALTER TABLE order_line_quantities 
ADD COLUMN IF NOT EXISTS pending_procurement_arrangement INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS requested_from_stock INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS awaiting_kitting_packing INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS in_kitting_packing INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS on_hold_at_kitting_packing INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS kitted_packed_awaiting_screening_qc INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS in_screening_qc INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS on_hold_at_screening_qc INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS screening_qc_passed_ready_for_invoice INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS screening_qc_rejected INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS invoiced INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS shipped_delivered INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS cancelled INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_updated_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Migrate data from old column names to new ones (if old columns exist)
DO $$
BEGIN
    -- Copy data from old columns to new ones
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'pending_procurement') THEN
        UPDATE order_line_quantities SET pending_procurement_arrangement = pending_procurement WHERE pending_procurement_arrangement = 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'on_hold_kitting') THEN
        UPDATE order_line_quantities SET on_hold_at_kitting_packing = on_hold_kitting WHERE on_hold_at_kitting_packing = 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'kitted_awaiting_qc') THEN
        UPDATE order_line_quantities SET kitted_packed_awaiting_screening_qc = kitted_awaiting_qc WHERE kitted_packed_awaiting_screening_qc = 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'on_hold_qc') THEN
        UPDATE order_line_quantities SET on_hold_at_screening_qc = on_hold_qc WHERE on_hold_at_screening_qc = 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'qc_passed_ready_invoice') THEN
        UPDATE order_line_quantities SET screening_qc_passed_ready_for_invoice = qc_passed_ready_invoice WHERE screening_qc_passed_ready_for_invoice = 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'order_line_quantities' AND column_name = 'qc_rejected') THEN
        UPDATE order_line_quantities SET screening_qc_rejected = qc_rejected WHERE screening_qc_rejected = 0;
    END IF;
END $$;

-- =============================================
-- ENHANCED: Quantity Logs Table
-- =============================================

ALTER TABLE quantity_logs
ADD COLUMN IF NOT EXISTS from_state TEXT,
ADD COLUMN IF NOT EXISTS to_state TEXT,
ADD COLUMN IF NOT EXISTS quantity_moved INTEGER,
ADD COLUMN IF NOT EXISTS reason TEXT,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS ct_numbers TEXT[],
ADD COLUMN IF NOT EXISTS location TEXT DEFAULT 'SB' CHECK (location IN ('SB', 'NP')),
ADD COLUMN IF NOT EXISTS metadata JSONB,
ADD COLUMN IF NOT EXISTS parent_log_id UUID REFERENCES quantity_logs(id),
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Migrate existing data to new structure
UPDATE quantity_logs 
SET 
    quantity_moved = quantity_moved,
    reason = reason_text,
    ct_numbers = associated_ct_numbers,
    created_by = user_id
WHERE quantity_moved IS NULL;

-- =============================================
-- NEW: Quantity Hold Records
-- =============================================

CREATE TABLE IF NOT EXISTS quantity_holds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_line_id UUID REFERENCES order_lines(id) NOT NULL,
    quantity_held INTEGER NOT NULL CHECK (quantity_held > 0),
    hold_stage TEXT NOT NULL CHECK (hold_stage IN ('kitting_packing', 'screening_qc')),
    hold_reason TEXT NOT NULL,
    hold_notes TEXT,
    
    -- Hold Management
    held_by UUID REFERENCES auth.users(id),
    held_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    released_by UUID REFERENCES auth.users(id),
    released_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    -- Resolution
    resolution_action TEXT CHECK (resolution_action IN ('released', 'rejected', 'escalated')),
    resolution_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SEED DATA: Quantity State Definitions
-- =============================================

INSERT INTO quantity_state_definitions 
(state_name, display_name, description, sort_order, stage_category, allowed_transitions, required_permissions) 
VALUES 
-- Procurement Stage
('total_order_quantity', 'Total Order Quantity', 'Initial order quantity from customer', 1, 'procurement', 
 ARRAY['pending_procurement_arrangement'], ARRAY['VIEW_ORDERS']),

('pending_procurement_arrangement', 'Pending Procurement', 'Awaiting procurement action', 2, 'procurement', 
 ARRAY['requested_from_stock'], ARRAY['MANAGE_PROCUREMENT']),

('requested_from_stock', 'Requested from Stock', 'Parts requested from inventory', 3, 'procurement', 
 ARRAY['awaiting_kitting_packing'], ARRAY['MANAGE_STOCK']),

-- Kitting Stage  
('awaiting_kitting_packing', 'Awaiting Kitting/Packing', 'Ready for kitting process', 4, 'kitting', 
 ARRAY['in_kitting_packing'], ARRAY['MANAGE_KITTING']),

('in_kitting_packing', 'In Kitting/Packing', 'Currently being kitted/packed', 5, 'kitting', 
 ARRAY['on_hold_at_kitting_packing', 'kitted_packed_awaiting_screening_qc'], ARRAY['MANAGE_KITTING']),

('on_hold_at_kitting_packing', 'On Hold - Kitting/Packing', 'Held during kitting process', 6, 'kitting', 
 ARRAY['in_kitting_packing', 'cancelled'], ARRAY['MANAGE_KITTING', 'MANAGE_HOLDS']),

-- QC Stage
('kitted_packed_awaiting_screening_qc', 'Awaiting Screening/QC', 'Ready for quality control', 7, 'qc', 
 ARRAY['in_screening_qc'], ARRAY['MANAGE_QC']),

('in_screening_qc', 'In Screening/QC', 'Currently in quality control', 8, 'qc', 
 ARRAY['on_hold_at_screening_qc', 'screening_qc_passed_ready_for_invoice', 'screening_qc_rejected'], ARRAY['MANAGE_QC']),

('on_hold_at_screening_qc', 'On Hold - Screening/QC', 'Held during QC process', 9, 'qc', 
 ARRAY['in_screening_qc', 'screening_qc_rejected'], ARRAY['MANAGE_QC', 'MANAGE_HOLDS']),

-- Final Stages
('screening_qc_passed_ready_for_invoice', 'QC Passed - Ready for Invoice', 'Passed QC, ready for invoicing', 10, 'final', 
 ARRAY['invoiced'], ARRAY['MANAGE_INVOICING']),

('screening_qc_rejected', 'QC Rejected', 'Failed quality control', 11, 'final', 
 ARRAY['cancelled'], ARRAY['MANAGE_QC']),

('invoiced', 'Invoiced', 'Invoice generated', 12, 'final', 
 ARRAY['shipped_delivered'], ARRAY['MANAGE_INVOICING']),

('shipped_delivered', 'Shipped/Delivered', 'Order completed', 13, 'final', 
 ARRAY[], ARRAY['VIEW_ORDERS']),

('cancelled', 'Cancelled', 'Order cancelled', 14, 'final', 
 ARRAY[], ARRAY['MANAGE_ORDERS'])
ON CONFLICT (state_name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    sort_order = EXCLUDED.sort_order,
    stage_category = EXCLUDED.stage_category,
    allowed_transitions = EXCLUDED.allowed_transitions,
    required_permissions = EXCLUDED.required_permissions,
    updated_at = NOW();

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Quantity State Definitions indexes
CREATE INDEX IF NOT EXISTS idx_quantity_state_definitions_stage_category ON quantity_state_definitions(stage_category);
CREATE INDEX IF NOT EXISTS idx_quantity_state_definitions_sort_order ON quantity_state_definitions(sort_order);

-- Quantity Holds indexes
CREATE INDEX IF NOT EXISTS idx_quantity_holds_order_line_id ON quantity_holds(order_line_id);
CREATE INDEX IF NOT EXISTS idx_quantity_holds_is_active ON quantity_holds(is_active);
CREATE INDEX IF NOT EXISTS idx_quantity_holds_held_at ON quantity_holds(held_at);

-- Enhanced Quantity Logs indexes
CREATE INDEX IF NOT EXISTS idx_quantity_logs_from_to_state ON quantity_logs(from_state, to_state);
CREATE INDEX IF NOT EXISTS idx_quantity_logs_location ON quantity_logs(location);
CREATE INDEX IF NOT EXISTS idx_quantity_logs_created_by ON quantity_logs(created_by);

-- =============================================
-- FUNCTIONS FOR STATE MANAGEMENT
-- =============================================

-- Enhanced function to update updated_at timestamp for new tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for new tables
CREATE TRIGGER update_quantity_state_definitions_updated_at 
    BEFORE UPDATE ON quantity_state_definitions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quantity_holds_updated_at 
    BEFORE UPDATE ON quantity_holds 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to validate quantity state transitions
CREATE OR REPLACE FUNCTION validate_quantity_transition(
    p_from_state TEXT,
    p_to_state TEXT,
    p_user_permissions TEXT[]
) RETURNS BOOLEAN AS $$
DECLARE
    v_allowed_transitions TEXT[];
    v_required_permissions TEXT[];
    v_permission TEXT;
BEGIN
    -- Get allowed transitions and required permissions for the from_state
    SELECT allowed_transitions, required_permissions
    INTO v_allowed_transitions, v_required_permissions
    FROM quantity_state_definitions
    WHERE state_name = p_from_state AND is_active = true;
    
    -- Check if transition is allowed
    IF NOT (p_to_state = ANY(v_allowed_transitions)) THEN
        RETURN FALSE;
    END IF;
    
    -- Check if user has required permissions
    FOREACH v_permission IN ARRAY v_required_permissions
    LOOP
        IF NOT (v_permission = ANY(p_user_permissions)) THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get available quantity transitions for a user
CREATE OR REPLACE FUNCTION get_available_transitions(
    p_from_state TEXT,
    p_user_permissions TEXT[]
) RETURNS TEXT[] AS $$
DECLARE
    v_allowed_transitions TEXT[];
    v_required_permissions TEXT[];
    v_available_transitions TEXT[] := '{}';
    v_transition TEXT;
    v_permission TEXT;
    v_has_permission BOOLEAN;
BEGIN
    -- Get allowed transitions for the from_state
    SELECT allowed_transitions
    INTO v_allowed_transitions
    FROM quantity_state_definitions
    WHERE state_name = p_from_state AND is_active = true;
    
    -- Check each transition for permission requirements
    FOREACH v_transition IN ARRAY v_allowed_transitions
    LOOP
        SELECT required_permissions
        INTO v_required_permissions
        FROM quantity_state_definitions
        WHERE state_name = v_transition AND is_active = true;
        
        v_has_permission := TRUE;
        
        -- Check if user has all required permissions
        FOREACH v_permission IN ARRAY v_required_permissions
        LOOP
            IF NOT (v_permission = ANY(p_user_permissions)) THEN
                v_has_permission := FALSE;
                EXIT;
            END IF;
        END LOOP;
        
        -- Add to available transitions if user has permission
        IF v_has_permission THEN
            v_available_transitions := array_append(v_available_transitions, v_transition);
        END IF;
    END LOOP;
    
    RETURN v_available_transitions;
END;
$$ LANGUAGE plpgsql;

-- Function to transition quantity between states
CREATE OR REPLACE FUNCTION transition_quantity(
  p_order_line_id UUID,
  p_from_state TEXT,
  p_to_state TEXT,
  p_quantity INTEGER,
  p_reason TEXT DEFAULT NULL,
  p_rejection_reason TEXT DEFAULT NULL,
  p_ct_numbers TEXT[] DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_log_id UUID;
  v_current_quantity INTEGER;
  v_result JSON;
  v_from_column_name TEXT;
  v_to_column_name TEXT;
BEGIN
  -- Start transaction
  BEGIN
    -- Map state names to column names
    v_from_column_name := CASE p_from_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement_arrangement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_at_kitting_packing'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_packed_awaiting_screening_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_at_screening_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'screening_qc_passed_ready_for_invoice'
        WHEN 'screening_qc_rejected' THEN 'screening_qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE p_from_state
    END;
    
    v_to_column_name := CASE p_to_state
        WHEN 'total_order_quantity' THEN 'total_order_quantity'
        WHEN 'pending_procurement_arrangement' THEN 'pending_procurement_arrangement'
        WHEN 'requested_from_stock' THEN 'requested_from_stock'
        WHEN 'awaiting_kitting_packing' THEN 'awaiting_kitting_packing'
        WHEN 'in_kitting_packing' THEN 'in_kitting_packing'
        WHEN 'on_hold_at_kitting_packing' THEN 'on_hold_at_kitting_packing'
        WHEN 'kitted_packed_awaiting_screening_qc' THEN 'kitted_packed_awaiting_screening_qc'
        WHEN 'in_screening_qc' THEN 'in_screening_qc'
        WHEN 'on_hold_at_screening_qc' THEN 'on_hold_at_screening_qc'
        WHEN 'screening_qc_passed_ready_for_invoice' THEN 'screening_qc_passed_ready_for_invoice'
        WHEN 'screening_qc_rejected' THEN 'screening_qc_rejected'
        WHEN 'invoiced' THEN 'invoiced'
        WHEN 'shipped_delivered' THEN 'shipped_delivered'
        WHEN 'cancelled' THEN 'cancelled'
        ELSE p_to_state
    END;
    
    -- Validate that we have enough quantity in the from_state
    EXECUTE format('SELECT %I FROM order_line_quantities WHERE order_line_id = $1', v_from_column_name)
    INTO v_current_quantity
    USING p_order_line_id;
    
    IF v_current_quantity < p_quantity THEN
      RAISE EXCEPTION 'Insufficient quantity in state %: has %, requested %', 
        p_from_state, v_current_quantity, p_quantity;
    END IF;
    
    -- Update quantities: subtract from source, add to destination
    EXECUTE format('UPDATE order_line_quantities SET %I = %I - $1, %I = %I + $1, last_updated_at = NOW(), last_updated_by = $2 WHERE order_line_id = $3',
      v_from_column_name, v_from_column_name, v_to_column_name, v_to_column_name)
    USING p_quantity, p_user_id, p_order_line_id;
    
    -- Create audit log entry
    INSERT INTO quantity_logs (
      order_line_id, from_state, to_state, quantity_moved, reason, 
      rejection_reason, ct_numbers, metadata, created_by
    ) VALUES (
      p_order_line_id, p_from_state, p_to_state, p_quantity, p_reason,
      p_rejection_reason, p_ct_numbers, p_metadata, p_user_id
    ) RETURNING id INTO v_log_id;
    
    -- Return success result
    v_result := json_build_object(
      'success', true,
      'log_id', v_log_id,
      'message', 'Quantity transition completed successfully'
    );
    
    RETURN v_result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Rollback and return error
    RAISE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;