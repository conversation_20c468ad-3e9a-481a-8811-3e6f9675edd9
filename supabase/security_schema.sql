-- Security and Audit Schema for Mini-ERP
-- This file creates the necessary tables and functions for security hardening

-- =====================================================
-- SECURITY EVENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    event_type TEXT NOT NULL CHECK (event_type IN ('login', 'logout', 'failed_login', 'permission_denied', 'data_access', 'sensitive_operation')),
    user_id UUID REFERENCES auth.users(id),
    user_email TEXT,
    ip_address INET,
    user_agent TEXT,
    resource TEXT,
    action TEXT,
    risk_level TEXT NOT NULL DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_risk_level ON security_events(risk_level);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip_address);

-- =====================================================
-- AUDIT LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);

-- =====================================================
-- SECURITY POLICIES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS security_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT UNIQUE NOT NULL,
    enabled BOOLEAN DEFAULT true,
    config JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id)
);

-- Insert default security policies
INSERT INTO security_policies (name, enabled, config) VALUES
('password_complexity', true, '{
    "minLength": 8,
    "requireUppercase": true,
    "requireLowercase": true,
    "requireNumbers": true,
    "requireSpecialChars": true
}'),
('session_security', true, '{
    "maxSessionDuration": 28800000,
    "idleTimeout": 1800000,
    "requireReauthForSensitive": true
}'),
('rate_limiting', true, '{
    "loginAttempts": 5,
    "loginWindow": 900000,
    "apiCallsPerMinute": 100
}'),
('data_encryption', true, '{
    "encryptSensitiveFields": true,
    "encryptPII": true,
    "keyRotationInterval": 7776000000
}'),
('audit_logging', true, '{
    "logAllDataChanges": true,
    "logSensitiveAccess": true,
    "retentionDays": 365
}')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- FAILED LOGIN ATTEMPTS TRACKING
-- =====================================================

CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT,
    ip_address INET,
    attempt_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_agent TEXT,
    details JSONB
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_failed_logins_email ON failed_login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_failed_logins_ip ON failed_login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_failed_logins_time ON failed_login_attempts(attempt_time DESC);

-- =====================================================
-- USER SESSIONS TRACKING
-- =====================================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    session_token TEXT UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_activity ON user_sessions(last_activity DESC);

-- =====================================================
-- SECURITY FUNCTIONS
-- =====================================================

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type TEXT,
    p_user_id UUID DEFAULT NULL,
    p_user_email TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_resource TEXT DEFAULT NULL,
    p_action TEXT DEFAULT NULL,
    p_risk_level TEXT DEFAULT 'low',
    p_details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO security_events (
        event_type, user_id, user_email, ip_address, user_agent,
        resource, action, risk_level, details
    ) VALUES (
        p_event_type, p_user_id, p_user_email, p_ip_address, p_user_agent,
        p_resource, p_action, p_risk_level, p_details
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_user_id UUID,
    p_action TEXT,
    p_resource_type TEXT,
    p_resource_id TEXT DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id,
        old_values, new_values, ip_address, user_agent
    ) VALUES (
        p_user_id, p_action, p_resource_type, p_resource_id,
        p_old_values, p_new_values, p_ip_address, p_user_agent
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check rate limiting
CREATE OR REPLACE FUNCTION check_rate_limit(
    p_identifier TEXT, -- email or IP
    p_action TEXT,
    p_limit INTEGER DEFAULT 5,
    p_window_minutes INTEGER DEFAULT 15
)
RETURNS BOOLEAN AS $$
DECLARE
    attempt_count INTEGER;
    window_start TIMESTAMP WITH TIME ZONE;
BEGIN
    window_start := NOW() - (p_window_minutes || ' minutes')::INTERVAL;
    
    -- Count attempts in the window
    SELECT COUNT(*) INTO attempt_count
    FROM failed_login_attempts
    WHERE (email = p_identifier OR ip_address::TEXT = p_identifier)
    AND attempt_time >= window_start;
    
    RETURN attempt_count < p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to record failed login attempt
CREATE OR REPLACE FUNCTION record_failed_login(
    p_email TEXT,
    p_ip_address INET,
    p_user_agent TEXT DEFAULT NULL,
    p_details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    attempt_id UUID;
BEGIN
    INSERT INTO failed_login_attempts (email, ip_address, user_agent, details)
    VALUES (p_email, p_ip_address, p_user_agent, p_details)
    RETURNING id INTO attempt_id;
    
    -- Also log as security event
    PERFORM log_security_event(
        'failed_login',
        NULL,
        p_email,
        p_ip_address,
        p_user_agent,
        'authentication',
        'login_attempt',
        'medium',
        p_details
    );
    
    RETURN attempt_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old security data
CREATE OR REPLACE FUNCTION cleanup_security_data(
    p_retention_days INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    cutoff_date := NOW() - (p_retention_days || ' days')::INTERVAL;
    
    -- Clean up old security events (except critical ones)
    DELETE FROM security_events 
    WHERE timestamp < cutoff_date 
    AND risk_level NOT IN ('high', 'critical');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Clean up old failed login attempts
    DELETE FROM failed_login_attempts 
    WHERE attempt_time < cutoff_date;
    
    -- Clean up inactive sessions
    DELETE FROM user_sessions 
    WHERE (expires_at < NOW() OR last_activity < NOW() - INTERVAL '30 days')
    AND is_active = false;
    
    -- Clean up old audit logs (keep longer retention for compliance)
    DELETE FROM audit_logs 
    WHERE timestamp < NOW() - INTERVAL '2 years';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get security summary
CREATE OR REPLACE FUNCTION get_security_summary(
    p_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
    total_events BIGINT,
    failed_logins BIGINT,
    high_risk_events BIGINT,
    unique_users BIGINT,
    unique_ips BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE event_type = 'failed_login') as failed_logins,
        COUNT(*) FILTER (WHERE risk_level IN ('high', 'critical')) as high_risk_events,
        COUNT(DISTINCT user_id) FILTER (WHERE user_id IS NOT NULL) as unique_users,
        COUNT(DISTINCT ip_address) FILTER (WHERE ip_address IS NOT NULL) as unique_ips
    FROM security_events
    WHERE timestamp >= NOW() - (p_hours || ' hours')::INTERVAL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC AUDIT LOGGING
-- =====================================================

-- Function to automatically log data changes
CREATE OR REPLACE FUNCTION audit_data_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Log INSERT operations
    IF TG_OP = 'INSERT' THEN
        PERFORM log_audit_event(
            auth.uid(),
            'INSERT',
            TG_TABLE_NAME,
            COALESCE(NEW.id::TEXT, NEW.uid),
            NULL,
            to_jsonb(NEW)
        );
        RETURN NEW;
    END IF;
    
    -- Log UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        PERFORM log_audit_event(
            auth.uid(),
            'UPDATE',
            TG_TABLE_NAME,
            COALESCE(NEW.id::TEXT, NEW.uid),
            to_jsonb(OLD),
            to_jsonb(NEW)
        );
        RETURN NEW;
    END IF;
    
    -- Log DELETE operations
    IF TG_OP = 'DELETE' THEN
        PERFORM log_audit_event(
            auth.uid(),
            'DELETE',
            TG_TABLE_NAME,
            COALESCE(OLD.id::TEXT, OLD.uid),
            to_jsonb(OLD),
            NULL
        );
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for important tables
CREATE TRIGGER audit_order_lines
    AFTER INSERT OR UPDATE OR DELETE ON order_lines
    FOR EACH ROW EXECUTE FUNCTION audit_data_changes();

CREATE TRIGGER audit_ct_numbers
    AFTER INSERT OR UPDATE OR DELETE ON ct_numbers
    FOR EACH ROW EXECUTE FUNCTION audit_data_changes();

CREATE TRIGGER audit_users
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_data_changes();

CREATE TRIGGER audit_quantities
    AFTER INSERT OR UPDATE OR DELETE ON order_line_quantities
    FOR EACH ROW EXECUTE FUNCTION audit_data_changes();

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on security tables
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE failed_login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- Security events policies
CREATE POLICY "Users can view their own security events" ON security_events
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all security events" ON security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND r.name IN ('Director/Admin', 'System Administrator')
        )
    );

CREATE POLICY "System can insert security events" ON security_events
    FOR INSERT WITH CHECK (true);

-- Audit logs policies  
CREATE POLICY "Users can view audit logs for their actions" ON audit_logs
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all audit logs" ON audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND r.name IN ('Director/Admin', 'System Administrator')
        )
    );

CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- Security policies - admin only
CREATE POLICY "Only admins can manage security policies" ON security_policies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND r.name = 'Director/Admin'
        )
    );

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Security dashboard view
CREATE OR REPLACE VIEW security_dashboard AS
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    event_type,
    risk_level,
    COUNT(*) as event_count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT ip_address) as unique_ips
FROM security_events
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', timestamp), event_type, risk_level
ORDER BY hour DESC;

-- Failed login summary
CREATE OR REPLACE VIEW failed_login_summary AS
SELECT 
    DATE_TRUNC('hour', attempt_time) as hour,
    COUNT(*) as attempts,
    COUNT(DISTINCT email) as unique_emails,
    COUNT(DISTINCT ip_address) as unique_ips
FROM failed_login_attempts
WHERE attempt_time >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', attempt_time)
ORDER BY hour DESC;

-- User activity summary
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT 
    u.email,
    u.full_name,
    r.name as role,
    COUNT(se.id) as security_events,
    COUNT(al.id) as audit_events,
    MAX(se.timestamp) as last_security_event,
    MAX(al.timestamp) as last_audit_event
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN security_events se ON u.id = se.user_id 
    AND se.timestamp >= NOW() - INTERVAL '24 hours'
LEFT JOIN audit_logs al ON u.id = al.user_id 
    AND al.timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY u.id, u.email, u.full_name, r.name
ORDER BY security_events DESC, audit_events DESC;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant access to authenticated users
GRANT SELECT ON security_events TO authenticated;
GRANT SELECT ON audit_logs TO authenticated;
GRANT SELECT ON security_policies TO authenticated;
GRANT SELECT ON security_dashboard TO authenticated;
GRANT SELECT ON failed_login_summary TO authenticated;
GRANT SELECT ON user_activity_summary TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION log_security_event TO authenticated;
GRANT EXECUTE ON FUNCTION log_audit_event TO authenticated;
GRANT EXECUTE ON FUNCTION check_rate_limit TO authenticated;
GRANT EXECUTE ON FUNCTION record_failed_login TO authenticated;
GRANT EXECUTE ON FUNCTION get_security_summary TO authenticated;

-- Service role permissions for maintenance
GRANT EXECUTE ON FUNCTION cleanup_security_data TO service_role;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE security_events IS 'Logs all security-related events in the system';
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail of all data changes';
COMMENT ON TABLE security_policies IS 'Configurable security policies and settings';
COMMENT ON TABLE failed_login_attempts IS 'Tracks failed authentication attempts for rate limiting';
COMMENT ON TABLE user_sessions IS 'Active user session tracking';

COMMENT ON FUNCTION log_security_event IS 'Logs a security event with risk assessment';
COMMENT ON FUNCTION log_audit_event IS 'Logs an audit trail entry for data changes';
COMMENT ON FUNCTION cleanup_security_data IS 'Periodic cleanup of old security and audit data';

-- =====================================================
-- INITIAL SETUP COMPLETION
-- =====================================================

-- Log the completion of security schema setup
INSERT INTO system_logs (level, message, context, timestamp)
VALUES ('info', 'Security schema setup completed', '{"tables_created": true, "policies_created": true, "triggers_created": true}', NOW());

-- Display completion message
SELECT 'Security schema setup completed successfully!' as message;