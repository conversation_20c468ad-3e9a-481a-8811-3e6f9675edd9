# Mini-ERP Deployment Guide

## 🚀 Production Deployment Guide

This guide covers the complete deployment process for the Mini-ERP Order Management QC App.

## 📋 Prerequisites

### System Requirements
- **Server OS**: Ubuntu 20.04+ or RHEL 8+
- **CPU**: 4+ cores (recommended: 8+ cores for 20-30 users)
- **RAM**: 8GB minimum (recommended: 16GB)
- **Storage**: 100GB SSD minimum
- **Network**: 1Gbps connection for printing operations

### Software Dependencies
- Docker Engine 20.10+
- Docker Compose v2.0+
- Git 2.30+
- Node.js 18+ (for local development)

## 🏗️ Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   Load Balancer │◄──►│  Mini-ERP App   │◄──►│  MCP Server     │
│   (Nginx)       │    │  (React + Vite) │    │  (Express.js)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   Supabase      │    │   File Storage  │    │  Network Printers│
│   (PostgreSQL)  │    │   (Local/Cloud) │    │  (Zebra/Generic)│
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Installation Steps

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo apt install docker-compose-plugin

# Create deployment user
sudo useradd -m -s /bin/bash minierp
sudo usermod -aG docker minierp
```

### 2. Clone Repository

```bash
# Clone the repository
git clone https://github.com/bpipl/minierp2.git
cd minierp2

# Switch to production branch (if exists)
git checkout production || git checkout main
```

### 3. Environment Configuration

```bash
# Copy production environment file
cp .env.production .env

# Edit environment variables
nano .env

# Key variables to update:
# - VITE_SUPABASE_URL: Your production Supabase URL
# - VITE_SUPABASE_ANON_KEY: Your production Supabase anon key
# - VITE_MCP_API_KEY: Strong API key for MCP server
# - VITE_N8N_WEBHOOK_URL: Your N8N webhook endpoint
```

### 4. SSL Certificate Setup

```bash
# Create SSL directory
mkdir -p ssl

# Generate self-signed certificate (for testing)
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# Or copy your actual certificates
# cp your-cert.pem ssl/cert.pem
# cp your-key.pem ssl/key.pem
```

### 5. Deploy Application

```bash
# Build and start services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f mini-erp-frontend
docker-compose logs -f mcp-printing-server
```

## 🔍 Verification Steps

### 1. Application Health Check

```bash
# Check frontend health
curl -f http://localhost/health

# Check MCP server health
curl -f http://localhost:3001/api/mcp/health

# Check services status
docker-compose ps
```

### 2. Functional Testing

1. **Access the application**: http://your-server-ip
2. **Login**: <EMAIL> / Admin@123
3. **Test printing**: Try to print a test label
4. **Test real-time**: Open multiple tabs and test live updates

## 📊 Monitoring & Logging

### Application Logs
```bash
# Frontend logs
docker-compose logs -f mini-erp-frontend

# MCP server logs
docker-compose logs -f mcp-printing-server

# System logs
journalctl -u docker -f
```

### Performance Monitoring
```bash
# Container resource usage
docker stats

# System resource usage
htop
iostat -x 1
```

### Health Monitoring
```bash
# Setup cron job for health checks
crontab -e

# Add this line for every 5 minutes health check
*/5 * * * * curl -f http://localhost/health || echo "Health check failed" | mail -s "Mini-ERP Health Alert" <EMAIL>
```

## 🔄 Update Procedures

### Standard Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build
docker-compose up -d

# Verify deployment
docker-compose ps
```

### Zero-Downtime Updates
```bash
# For blue-green deployment
docker-compose -f docker-compose.blue.yml up -d
# Switch load balancer to new version
# Test thoroughly
# Remove old version
```

## 🚨 Backup Procedures

### Database Backup
```bash
# Backup Supabase data (run daily)
pg_dump -h db.qeozkzbjvvkgsvtitbny.supabase.co -U postgres -d postgres > backup-$(date +%Y%m%d).sql
```

### Application Backup
```bash
# Backup application data
tar -czf minierp-backup-$(date +%Y%m%d).tar.gz \
  mcp-printing-server/data \
  mcp-printing-server/queue \
  ssl \
  .env
```

### Restore Procedures
```bash
# Restore database
psql -h db.qeozkzbjvvkgsvtitbny.supabase.co -U postgres -d postgres < backup-********.sql

# Restore application data
tar -xzf minierp-backup-********.tar.gz
```

## 🔐 Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Firewall configured (ports 80, 443, 3001 only)
- [ ] Strong passwords for all accounts
- [ ] Regular security updates applied
- [ ] Database access restricted to application only
- [ ] API keys rotated regularly
- [ ] Backup encryption enabled
- [ ] Log monitoring configured

## 🚀 Performance Optimization

### Database Optimization
```sql
-- Add indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_order_lines_uid ON order_lines(uid);
CREATE INDEX IF NOT EXISTS idx_order_lines_customer ON order_lines(customer_id);
CREATE INDEX IF NOT EXISTS idx_ct_numbers_number ON ct_numbers(ct_number);
```

### Application Optimization
```bash
# Enable compression in nginx
# Configure appropriate cache headers
# Monitor and optimize slow queries
# Use CDN for static assets (if needed)
```

## 📞 Support & Troubleshooting

### Common Issues

**Issue 1: MCP Server Not Connecting**
```bash
# Check MCP server logs
docker-compose logs mcp-printing-server

# Restart MCP server
docker-compose restart mcp-printing-server
```

**Issue 2: Database Connection Issues**
```bash
# Test database connection
psql -h db.qeozkzbjvvkgsvtitbny.supabase.co -U postgres -d postgres

# Check environment variables
docker-compose exec mini-erp-frontend printenv | grep SUPABASE
```

**Issue 3: Printing Issues**
```bash
# Check printer discovery
docker-compose exec mcp-printing-server npm run discover-printers

# Test network connectivity to printers
ping printer-ip-address
```

### Emergency Procedures

**Complete System Failure**
1. Stop all services: `docker-compose down`
2. Check system resources: `df -h`, `free -h`
3. Check logs: `journalctl -xe`
4. Restore from backup if needed
5. Contact system administrator

**Security Incident**
1. Immediately change all passwords
2. Rotate API keys
3. Check access logs for unauthorized access
4. Update security patches
5. Notify relevant stakeholders

## 📈 Scaling Considerations

### Horizontal Scaling
- Add load balancer for multiple frontend instances
- Use Redis for session management
- Implement database read replicas
- Configure container orchestration (Kubernetes)

### Vertical Scaling
- Increase server resources (CPU, RAM)
- Optimize database configuration
- Tune application performance settings
- Monitor and adjust based on usage patterns

## 📝 Maintenance Schedule

### Daily
- [ ] Health check verification
- [ ] Log review for errors
- [ ] Backup verification

### Weekly
- [ ] Performance review
- [ ] Security log analysis
- [ ] Update monitoring

### Monthly
- [ ] Security patches
- [ ] Performance optimization
- [ ] Backup testing
- [ ] Capacity planning review

---

**Last Updated**: January 10, 2025  
**Version**: 1.0.0  
**Contact**: System Administrator