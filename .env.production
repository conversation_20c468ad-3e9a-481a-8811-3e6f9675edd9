# Production Environment Configuration for Mini-ERP

# Application Settings
NODE_ENV=production
VITE_APP_TITLE="Mini-ERP Order Management QC App"
VITE_UID_PREFIX=A
VITE_LOCATION=SB

# Supabase Configuration (Replace with production values)
VITE_SUPABASE_URL=https://qeozkzbjvvkgsvtitbny.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFlb3premJqdnZrZ3N2dGl0Ym55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMDMwOTgsImV4cCI6MjA2NDc3OTA5OH0.YG0iIRPdS-YZWg2WC8C2jKeul_qvp36F3889nVJbet4

# MCP Printing Server Configuration
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_MCP_API_KEY=your-secure-production-api-key-here

# N8N Webhook Integration
VITE_N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/minierp

# Security Settings
ENABLE_HTTPS=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=/app/logs/app.log

# Performance Settings
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9464
HEALTH_CHECK_INTERVAL=30

# Database Settings (if using local database)
DB_POOL_SIZE=20
DB_TIMEOUT=30000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Session Configuration
SESSION_SECRET=your-super-secure-session-secret-here
SESSION_TIMEOUT=86400

# CORS Configuration
CORS_ORIGIN=https://minierp.internal,https://staging.minierp.internal

# Email Configuration (for notifications)
SMTP_HOST=smtp.company.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_FROM=Mini-ERP System <<EMAIL>>

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_ERROR_TRACKING=true
ENABLE_PERFORMANCE_MONITORING=true

# Third-party Services
SENTRY_DSN=https://your-sentry-dsn-here
ANALYTICS_ID=your-analytics-id-here