// Service Worker for Mini-ERP PWA
const CACHE_NAME = 'mini-erp-v1.0.0'
const API_CACHE_NAME = 'mini-erp-api-v1.0.0'

// Assets to cache for offline usage
const STATIC_ASSETS = [
  '/',
  '/login',
  '/orders',
  '/dashboard',
  '/manifest.json',
  // Add critical CSS and JS files here when built
]

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/orders',
  '/api/users',
  '/api/quantities',
  // Add more API endpoints as needed
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Mini-ERP Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Static assets cached successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Failed to cache static assets:', error)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Mini-ERP Service Worker activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Old caches cleaned up')
        return self.clients.claim()
      })
  )
})

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Handle API requests
  if (url.pathname.startsWith('/api/') || url.hostname.includes('supabase')) {
    event.respondWith(handleAPIRequest(request))
    return
  }

  // Handle static assets
  if (request.method === 'GET') {
    event.respondWith(handleStaticRequest(request))
    return
  }
})

// Handle API requests with cache-first strategy for GET, network-only for mutations
async function handleAPIRequest(request) {
  const url = new URL(request.url)
  
  try {
    if (request.method === 'GET') {
      // Try cache first for GET requests
      const cachedResponse = await caches.match(request)
      if (cachedResponse && isResponseFresh(cachedResponse)) {
        console.log('Serving API response from cache:', url.pathname)
        return cachedResponse
      }

      // Fetch from network and update cache
      const networkResponse = await fetch(request)
      if (networkResponse.ok) {
        const cache = await caches.open(API_CACHE_NAME)
        await cache.put(request, networkResponse.clone())
        console.log('API response cached:', url.pathname)
      }
      return networkResponse
    } else {
      // Network-only for mutations (POST, PUT, DELETE)
      const response = await fetch(request)
      
      // Invalidate related cache entries on successful mutations
      if (response.ok && (request.method === 'POST' || request.method === 'PUT' || request.method === 'DELETE')) {
        await invalidateRelatedCache(url.pathname)
      }
      
      return response
    }
  } catch (error) {
    console.error('API request failed:', error)
    
    // For GET requests, try to serve stale cache if available
    if (request.method === 'GET') {
      const cachedResponse = await caches.match(request)
      if (cachedResponse) {
        console.log('Serving stale cache due to network error:', url.pathname)
        return cachedResponse
      }
    }
    
    // Return error response for mutations or if no cache available
    return new Response(
      JSON.stringify({ error: 'Network error and no cache available' }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Handle static assets with cache-first strategy
async function handleStaticRequest(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      console.log('Serving static asset from cache:', request.url)
      return cachedResponse
    }

    // Fetch from network and cache
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME)
      await cache.put(request, networkResponse.clone())
      console.log('Static asset cached:', request.url)
    }
    return networkResponse
  } catch (error) {
    console.error('Static request failed:', error)
    
    // Try to serve from cache even if network failed
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // Fallback to offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline.html') || new Response('Offline')
    }
    
    return new Response('Resource not available offline', { status: 503 })
  }
}

// Check if cached response is fresh (less than 5 minutes old)
function isResponseFresh(response) {
  const cacheDate = response.headers.get('sw-cache-date')
  if (!cacheDate) return false
  
  const ageMs = Date.now() - new Date(cacheDate).getTime()
  return ageMs < 5 * 60 * 1000 // 5 minutes
}

// Invalidate related cache entries after mutations
async function invalidateRelatedCache(pathname) {
  const cache = await caches.open(API_CACHE_NAME)
  const keys = await cache.keys()
  
  const relatedKeys = keys.filter(request => {
    const requestPath = new URL(request.url).pathname
    
    // Invalidate related resources based on pathname
    if (pathname.includes('/orders')) {
      return requestPath.includes('/orders') || requestPath.includes('/quantities')
    }
    if (pathname.includes('/users')) {
      return requestPath.includes('/users') || requestPath.includes('/roles')
    }
    if (pathname.includes('/quantities')) {
      return requestPath.includes('/quantities') || requestPath.includes('/workflows')
    }
    
    return false
  })
  
  await Promise.all(relatedKeys.map(key => cache.delete(key)))
  console.log(`Invalidated ${relatedKeys.length} related cache entries`)
}

// Handle background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'background-sync-orders') {
    event.waitUntil(syncOfflineActions())
  }
})

// Sync offline actions when connection is restored
async function syncOfflineActions() {
  try {
    // Get offline actions from IndexedDB or localStorage
    const offlineActions = getOfflineActions()
    
    for (const action of offlineActions) {
      try {
        await fetch(action.url, action.options)
        removeOfflineAction(action.id)
        console.log('Offline action synced:', action.id)
      } catch (error) {
        console.error('Failed to sync offline action:', action.id, error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// Push notification handling
self.addEventListener('push', (event) => {
  if (!event.data) return
  
  const data = event.data.json()
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    data: data.data,
    actions: data.actions || [],
    requireInteraction: true,
    tag: data.tag || 'default'
  }
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  )
})

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  const urlToOpen = event.notification.data?.url || '/'
  
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clients) => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clients) {
          if (client.url === urlToOpen && 'focus' in client) {
            return client.focus()
          }
        }
        
        // Open new window/tab
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen)
        }
      })
  )
})

// Utility functions (implement based on your offline storage strategy)
function getOfflineActions() {
  // Implement retrieval of offline actions from storage
  return []
}

function removeOfflineAction(actionId) {
  // Implement removal of synced offline action
  console.log('Removing offline action:', actionId)
}

// Message handling from main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type) {
    switch (event.data.type) {
      case 'SKIP_WAITING':
        self.skipWaiting()
        break
      case 'GET_VERSION':
        event.ports[0].postMessage({ version: CACHE_NAME })
        break
      case 'CLEAR_CACHE':
        clearAllCaches()
        break
      default:
        console.log('Unknown message type:', event.data.type)
    }
  }
})

// Clear all caches
async function clearAllCaches() {
  const cacheNames = await caches.keys()
  await Promise.all(cacheNames.map(name => caches.delete(name)))
  console.log('All caches cleared')
}