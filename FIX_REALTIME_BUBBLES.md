# Fix for Real-Time Quantity State Bubbles Not Updating

## Problem Description
The quantity state badges (bubbles) showing CT number states are not updating in real-time when quantity transitions occur. Users need to manually refresh the page to see updated states.

## Root Cause
The issue is caused by missing database triggers that properly notify all related tables when quantity transitions happen. The SQL file `supabase/fix_realtime_triggers.sql` contains the necessary triggers but hasn't been applied to the database yet.

## Temporary Frontend Fixes Applied
While waiting for database access to apply the SQL triggers, I've implemented these frontend improvements:

1. **Enhanced Real-Time Subscriptions in CTNumberDisplay**
   - Added subscription to `order_line_quantities` table changes
   - Now listens to both CT number changes and quantity changes

2. **Force Query Invalidation in useQuantityTracking**
   - After successful transitions, manually invalidates related queries
   - Ensures UI components refresh their data

3. **Manual Refresh Button**
   - Added a "Refresh" button to the Orders page header
   - Provides users with a quick way to force data refresh

4. **Timestamp Updates in QuantityTransitionService**
   - Forces update of `updated_at` timestamp on order lines after transitions
   - Helps trigger some real-time subscriptions

## Permanent Solution Required

### Apply Database Triggers
To properly fix the real-time updates, you need to apply the SQL triggers to your Supabase database:

```bash
# Option 1: Using Supabase CLI (if you have local access)
supabase db push < supabase/fix_realtime_triggers.sql

# Option 2: Using psql directly
psql "postgresql://postgres:<EMAIL>:5432/postgres" < supabase/fix_realtime_triggers.sql

# Option 3: Via Supabase Dashboard
# 1. Go to your Supabase project dashboard
# 2. Navigate to SQL Editor
# 3. Copy and paste the contents of supabase/fix_realtime_triggers.sql
# 4. Run the query
```

### What the SQL Triggers Do
1. **Enable RLS on relevant tables** for proper security
2. **Create trigger functions** that notify changes across related tables
3. **Update timestamps** on parent tables when child tables change
4. **Enable real-time publications** for all relevant tables
5. **Create indexes** for better performance

### Tables Affected
- `order_lines`
- `order_line_quantities`
- `quantity_logs`
- `ct_numbers`

## Testing After Fix
1. Open the Orders page
2. Perform a quantity transition (e.g., move items to "In Kitting")
3. The quantity state badges should update immediately without refresh
4. CT number display states should also update in real-time

## Rollback
If issues occur after applying the SQL triggers, the frontend fixes will continue to work as a fallback mechanism.