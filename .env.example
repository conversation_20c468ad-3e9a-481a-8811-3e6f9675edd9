# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://qeozkzbjvvkgsvtitbny.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Application Settings (REQUIRED)
VITE_APP_TITLE="Mini-ERP Order Management QC App"
VITE_LOCATION=SB
VITE_UID_PREFIX=A

# MCP Backend Configuration (OPTIONAL - Currently disabled)
VITE_MCP_API_URL=http://localhost:3001/api/mcp

# WhatsApp Configuration (OPTIONAL - Meta Cloud API)
VITE_WHATSAPP_API_URL=https://graph.facebook.com/v17.0
VITE_WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
VITE_WHATSAPP_ACCESS_TOKEN=your_access_token

# N8N Webhooks (OPTIONAL - WhatsApp Integration)
VITE_N8N_WEBHOOK_URL=your_n8n_webhook_url

# Network Print Server (OPTIONAL - Legacy)
VITE_NPS_URL=your_network_print_server_url

# Feature Flags (OPTIONAL)
VITE_ENABLE_MCP=false
VITE_ENABLE_WHATSAPP=false
VITE_ENABLE_N8N=false

# Debug Mode (OPTIONAL)
VITE_DEBUG_MODE=false