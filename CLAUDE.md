# CLAUDE.md - AI Development Context & Memory

## 🎯 Project Overview
**Mini-ERP Order Management QC App** - Internal web application for managing laptop part orders, quality control, and inter-location operations. Supports 20-30 concurrent users, replacing Google Sheets for HP and Lenovo operations.

## 👨‍💼 Director Profile
- **Background**: Vibe coder, built 3+ apps in 15 days, manages Proxmox clusters as hobby
- **Communication**: Prefers clear, simple explanations without deep technical jargon
- **Project Priority**: Mission-critical system for HP and Lenovo customer satisfaction

## 🛠️ Tech Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + Shadcn/ui
- **Backend**: Supabase (PostgreSQL + Auth + Realtime + Storage)
- **State**: React Context + React Query
- **Printing**: MCP Server (intentionally disabled) + ZPL generation + Comprehensive printing navigation
- **Integration**: WhatsApp (Meta Cloud API), N8N webhooks

## 📊 Current System Status
**Overall Completion**: ~90% Production Ready (Verified June 2025)

### ✅ Completed Features
1. **Authentication & User Management** - 8 roles, RLS policies, session management
2. **Order Management** - CRUD operations, real-time updates, dual layout (cards/rows)
3. **CT Number System** - 14-digit validation, duplicate detection, unified workflow, auto-progress fixed
4. **WhatsApp Integration** - 4 approval workflows, Meta Cloud API, admin dashboard
5. **FAI System** - Document management, master images, Excel extraction architecture
6. **Label Designer** - Fabric.js WYSIWYG, toolbar, save to database
7. **Template Management** - 10 predefined templates, categories, CRUD interface, dedicated page
8. **Quantity Tracking** - State badges, global pending display, quick transitions
9. **Professional Dashboards** - Analytics, Operations, Reports (Sprint branch additions)
10. **Printing Navigation** - Dedicated menu with Template Manager, Quick Print, Print Queue pages

### 🚨 Critical Gaps (P0 - Production Blockers)
1. **527 TypeScript Build Errors** - Cannot build for production (1-2 days)
2. **MCP Backend Server** - Not implemented, frontend complete (2-3 days)

### ⚠️ High Priority Gaps (P1)
1. **WhatsApp External Config** - Meta API credentials needed
2. **FAI Image Extraction** - Background service missing
3. **Template Delete Handler** - UI exists, function missing
4. **CSV Import** - In requirements but not built

## 🔧 Development Environment

### Key Commands
```bash
npm run dev      # Start development server
npm run build    # Build for production (currently fails)
npm run lint     # Run ESLint
npm install      # Install dependencies
```

### Critical Environment Variables
```bash
VITE_SUPABASE_URL=https://qeozkzbjvvkgsvtitbny.supabase.co
VITE_SUPABASE_ANON_KEY=[configured]
VITE_MCP_API_URL=http://localhost:3001/api/mcp
VITE_LOCATION=SB
VITE_APP_TITLE="Mini-ERP Order Management QC App"
VITE_UID_PREFIX=A
```

### Database
- **URL**: postgresql://postgres:[password]@db.qeozkzbjvvkgsvtitbny.supabase.co:5432/postgres
- **Tables**: 38 tables with 69 RLS policies
- **Admin Login**: <EMAIL> / Admin@123

## 🏗️ Project Structure
```
src/
├── components/        # React components organized by feature
├── hooks/            # Custom React hooks for data fetching
├── pages/            # Route components
├── services/         # Business logic and API services
├── utils/            # Helper functions and utilities
├── types/            # TypeScript type definitions
docs/
├── features/         # V2 verified feature documentation
├── archive/          # Legacy pre-v2 documentation
└── *.md             # Core docs (README, SYSTEM_STATE, gaps-and-roadmap)
```

## 🔑 Critical Business Rules

### CT Numbers
- **Format**: 14 alphanumeric characters, ALL UPPERCASE
- **Uniqueness**: System warns on duplicates, requires approval
- **HP Specific**: CT system primarily for HP orders

### Quantity States (Progressive System)
```
TotalOrderQuantity → PendingProcurementArrangement → RequestedFromStock → 
AwaitingKittingPacking → InKittingPacking → KittedPacked_AwaitingScreeningQC → 
InScreeningQC → ScreeningQCPassed_ReadyForInvoice → Invoiced → ShippedDelivered
```

### User Roles & Permissions
1. **Director/Admin** - Full access
2. **Accountant** - Invoicing, prices visible
3. **Warehouse Manager** - SB operations, no prices
4. **SB Staff** - Kitting/packing or QC tasks
5. **NP Manager** - NP operations, no customer names
6. **NP Staff** - Testing tasks only
7. **Procurement** - Vendor communication

### Date Format
**ALWAYS use DDMMYY format** throughout the system

## 📝 Development Guidelines

### Code Style
- Follow existing patterns in codebase
- Use TypeScript strict mode
- Implement proper error handling
- Add loading states for async operations
- NO COMMENTS unless explicitly requested

### Database Operations
- Always use Row Level Security
- Log all significant actions
- Use transactions for multi-step operations
- Implement optimistic updates for better UX

### UI/UX Principles
- Real-time updates without page refresh
- Card-based order visualization
- Progressive quantity bars with color coding
- Mobile-responsive design
- Minimal clicks for common operations

## 🚀 4-Week Implementation Roadmap

### Week 1: Production Blockers
- Day 1-2: Fix TypeScript build errors
- Day 3-5: Implement MCP backend server

### Week 2: Critical Integrations
- Day 1-2: WhatsApp configuration (Meta + N8N)
- Day 3-4: FAI image extraction service
- Day 5: Template delete handler + ZPL integration

### Week 3: Data & Warnings
- Day 1-2: CSV import implementation
- Day 3-4: Multi-PO invoice warnings
- Day 5: Testing and bug fixes

### Week 4: Polish & Launch
- Day 1: Email/SMS configuration
- Day 2: Data cleanup
- Day 3-4: End-to-end testing
- Day 5: Production deployment

## 📚 Documentation V2 (Verified June 2025)

### Core Documents
- **`docs/README.md`** - Complete navigation map
- **`docs/SYSTEM_STATE.md`** - Verified system truth
- **`docs/gaps-and-roadmap.md`** - 12 gaps with 4-week roadmap

### Feature Documentation
```
docs/features/
├── order-management/     # 5 comprehensive docs
├── printing-ecosystem/   # 8 comprehensive docs
└── integration-layer/    # 6 comprehensive docs
```

### Reference Documents
- `TODO.md` - Active development tasks
- `USER_GUIDE.md` - End user instructions
- `API_DOCUMENTATION.md` - Technical API reference
- `SUPABASE_SETUP.md` - Database configuration
- `QUANTITY_NAMING_CONVENTION.md` - Quick reference

## 🎨 Key Components & Services

### React Components
- `CTNumberModal.tsx` - Unified CT assignment & printing (auto-progress fixed)
- `OrderCard.tsx` / `OrderRowCard.tsx` - Order display layouts
- `QuantityStateBadges.tsx` - Visual quantity distribution
- `LabelDesigner.tsx` - Fabric.js WYSIWYG designer
- `TemplateManager.tsx` - Template CRUD interface
- `TemplateManagerPage.tsx` - Standalone template management page
- `QuickPrintPage.tsx` - Dedicated quick print interface
- `PrintQueuePage.tsx` - Print job monitoring and management

### Services & Hooks
- `ApprovalWorkflowManager.ts` - WhatsApp workflow orchestration
- `WhatsAppProviderManager.ts` - Dual provider system
- `useOrders.ts` - Order data fetching & subscriptions
- `useMCPPrinting.ts` - Printer discovery & job management

## 🐛 Known Issues & Workarounds

### Current Bugs
1. Template delete handler not implemented
2. Some templates have null template_type

### Workarounds
1. MCP disabled: Use ZPL file download
2. WhatsApp not configured: Manual approval
3. No physical printer: Use Labelary.com

## 🚨 Critical Reminders
1. **Git Protocol**: ALWAYS push immediately after commit
2. **Date Format**: Use IST format DD/MM/YY HH:MM AM/PM in docs
3. **Testing**: Verify with actual code before claiming completion
4. **MCP Note**: Backend disabled to prevent console spam
5. **Quantity Mapping**: Database uses SHORT names, frontend uses LONG names
6. **Build First**: Must fix TypeScript errors before any deployment

## 🔄 Recent Achievements
- **CT Workflow Unification**: Reduced code by 24.4%, single modal for all operations
- **Sprint Branch Features**: Added 3 professional dashboards not in requirements
- **Documentation V2**: Complete verification and reorganization (June 2025)
- **Quantity Tracking**: 100% complete with visual indicators
- **CT Modal Auto-Progress**: Fixed workflow transition bug for new orders
- **Printing Navigation**: Consolidated printing features with dedicated menu, Template Manager, Quick Print, and Print Queue pages

---
**Last Updated**: 14/06/25 10:00 PM IST  
**System State**: 91% Complete, 10 gaps identified  
**Next Priority**: Week 1 - Fix build errors, MCP backend  
**Repository**: https://github.com/bpipl/minierp2.git