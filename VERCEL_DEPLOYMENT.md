# Vercel Deployment Guide

## Prerequisites
- GitHub account connected to Vercel
- Vercel account (free tier is sufficient)
- Access to Supabase project credentials

## Environment Variables Required in Vercel

### Required Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://qeozkzbjvvkgsvtitbny.supabase.co
VITE_SUPABASE_ANON_KEY=[Your actual anon key from Supabase]

# Application Settings
VITE_APP_TITLE=Mini-ERP Order Management QC App
VITE_LOCATION=SB
VITE_UID_PREFIX=A
```

### Optional Variables (Add if using these features)
```bash
# MCP Backend (if re-enabled)
VITE_MCP_API_URL=https://your-mcp-backend.vercel.app/api/mcp

# WhatsApp Integration
VITE_WHATSAPP_API_URL=https://graph.facebook.com/v17.0
VITE_WHATSAPP_PHONE_NUMBER_ID=[Your WhatsApp Phone Number ID]
VITE_WHATSAPP_ACCESS_TOKEN=[Your WhatsApp Access Token]

# N8N Webhooks
VITE_N8N_WEBHOOK_URL=[Your N8N webhook URL]

# Feature Flags
VITE_ENABLE_MCP=false
VITE_ENABLE_WHATSAPP=false
VITE_ENABLE_N8N=false
```

## Deployment Steps

### 1. Connect GitHub to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Click "Add New..." → "Project"
3. Import your GitHub repository
4. Select the `MiniERP2` repository

### 2. Configure Build Settings
- **Framework Preset**: Vite
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### 3. Add Environment Variables
1. In Vercel project settings, go to "Environment Variables"
2. Add each variable from the "Required Variables" section above
3. For `VITE_SUPABASE_ANON_KEY`, get it from:
   - Supabase Dashboard → Settings → API
   - Copy the "anon public" key

### 4. Deploy
1. Click "Deploy"
2. Wait for the build to complete (usually 2-3 minutes)
3. Your app will be available at: `https://your-project.vercel.app`

## Post-Deployment

### Update CORS Settings in Supabase
1. Go to Supabase Dashboard → Authentication → URL Configuration
2. Add your Vercel URLs to:
   - Site URL: `https://your-project.vercel.app`
   - Redirect URLs: `https://your-project.vercel.app/*`

### Benefits of Vercel Deployment
- ✅ WebSocket connections will work properly
- ✅ No CORS issues
- ✅ Automatic HTTPS/SSL
- ✅ Global CDN for fast loading
- ✅ Automatic deployments on git push
- ✅ Preview deployments for branches

### Custom Domain (Optional)
1. In Vercel project settings → Domains
2. Add your custom domain
3. Update DNS settings as instructed

## Troubleshooting

### If WebSocket still has issues:
- Check Supabase Realtime is enabled in your project
- Ensure the anon key has proper permissions

### If build fails:
- Check all required dependencies are in package.json
- Verify TypeScript errors are resolved
- Check build logs in Vercel dashboard

## GitHub Integration Features
- **Automatic Deployments**: Every push to main branch triggers deployment
- **Preview Deployments**: Every pull request gets a preview URL
- **Rollback**: Easy rollback to previous deployments

## Next Steps After Deployment
1. Test CT Modal auto-progress in production
2. Verify WebSocket connections are stable
3. Test all quantity transitions
4. Monitor performance in Vercel Analytics